[{"D:\\برمجة\\tste 1\\professional-display-system\\src\\index.tsx": "1", "D:\\برمجة\\tste 1\\professional-display-system\\src\\reportWebVitals.ts": "2", "D:\\برمجة\\tste 1\\professional-display-system\\src\\App.tsx": "3", "D:\\برمجة\\tste 1\\professional-display-system\\src\\Router.tsx": "4", "D:\\برمجة\\tste 1\\professional-display-system\\src\\pages\\HomePage.tsx": "5", "D:\\برمجة\\tste 1\\professional-display-system\\src\\context\\AppContext.tsx": "6", "D:\\برمجة\\tste 1\\professional-display-system\\src\\pages\\DisplaySelectionPage.tsx": "7", "D:\\برمجة\\tste 1\\professional-display-system\\src\\components\\Notification.tsx": "8", "D:\\برمجة\\tste 1\\professional-display-system\\src\\components\\LoadingSpinner.tsx": "9", "D:\\برمجة\\tste 1\\professional-display-system\\src\\components\\Button.tsx": "10", "D:\\برمجة\\tste 1\\professional-display-system\\src\\services\\mockDataService.ts": "11"}, {"size": 554, "mtime": 1752395709779, "results": "12", "hashOfConfig": "13"}, {"size": 425, "mtime": 1752395709532, "results": "14", "hashOfConfig": "13"}, {"size": 767, "mtime": 1752396258515, "results": "15", "hashOfConfig": "13"}, {"size": 1397, "mtime": 1752396246679, "results": "16", "hashOfConfig": "13"}, {"size": 5605, "mtime": 1752396271233, "results": "17", "hashOfConfig": "13"}, {"size": 12448, "mtime": 1752395888583, "results": "18", "hashOfConfig": "13"}, {"size": 9724, "mtime": 1752396938356, "results": "19", "hashOfConfig": "13"}, {"size": 3797, "mtime": 1752395913084, "results": "20", "hashOfConfig": "13"}, {"size": 911, "mtime": 1752395926200, "results": "21", "hashOfConfig": "13"}, {"size": 1820, "mtime": 1752396927140, "results": "22", "hashOfConfig": "13"}, {"size": 14158, "mtime": 1752395838057, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1nka1ff", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\برمجة\\tste 1\\professional-display-system\\src\\index.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\reportWebVitals.ts", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\App.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\Router.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\pages\\HomePage.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\context\\AppContext.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\pages\\DisplaySelectionPage.tsx", ["57"], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\components\\Notification.tsx", ["58"], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\components\\LoadingSpinner.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\components\\Button.tsx", [], [], "D:\\برمجة\\tste 1\\professional-display-system\\src\\services\\mockDataService.ts", [], [], {"ruleId": "59", "severity": 1, "message": "60", "line": 26, "column": 6, "nodeType": "61", "endLine": 26, "endColumn": 8, "suggestions": "62"}, {"ruleId": "63", "severity": 1, "message": "64", "line": 4, "column": 11, "nodeType": "65", "messageId": "66", "endLine": 4, "endColumn": 28}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDisplays'. Either include it or remove the dependency array.", "ArrayExpression", ["67"], "@typescript-eslint/no-unused-vars", "'NotificationState' is defined but never used.", "Identifier", "unusedVar", {"desc": "68", "fix": "69"}, "Update the dependencies array to be: [loadDisplays]", {"range": "70", "text": "71"}, [1005, 1007], "[loadDisplays]"]