{"ast": null, "code": "import{useState,useEffect,useContext,createContext}from'react';import{authService}from'../services/api';import{saveToLocalStorage,getFromLocalStorage,removeFromLocalStorage}from'../utils/helpers';// إنشاء Context للمصادقة\nimport{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext();// Provider للمصادقة\nexport const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[isAuthenticated,setIsAuthenticated]=useState(false);// التحقق من وجود token عند تحميل التطبيق\nuseEffect(()=>{const checkAuth=async()=>{try{const token=localStorage.getItem('authToken');const userInfo=getFromLocalStorage('userInfo');if(token&&userInfo){// التحقق من صحة الرمز المميز\nconst response=await authService.verifyToken();if(response.success){setUser(userInfo);setIsAuthenticated(true);}else{// إزالة البيانات غير الصحيحة\nlogout();}}}catch(error){console.error('Error checking auth:',error);logout();}finally{setLoading(false);}};checkAuth();},[]);// تسجيل الدخول\nconst login=(userData,token)=>{localStorage.setItem('authToken',token);saveToLocalStorage('userInfo',userData);setUser(userData);setIsAuthenticated(true);};// تسجيل الخروج\nconst logout=()=>{removeFromLocalStorage('authToken');removeFromLocalStorage('userInfo');setUser(null);setIsAuthenticated(false);};// تحديث بيانات المستخدم\nconst updateUser=userData=>{saveToLocalStorage('userInfo',userData);setUser(userData);};const value={user,loading,isAuthenticated,login,logout,updateUser};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};// Hook لاستخدام المصادقة\nexport const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};// Hook لإرسال OTP\nexport const useOTP=()=>{const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[otpSent,setOtpSent]=useState(false);const sendOTP=async function(phoneNumber,email){let language=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'ar';setLoading(true);setError(null);try{const response=await authService.sendOTP(phoneNumber,email,language);if(response.success){setOtpSent(true);return response;}else{setError(response.message||'فشل في إرسال رمز التحقق');return null;}}catch(error){setError(error.message||'خطأ في الشبكة');return null;}finally{setLoading(false);}};const verifyOTP=async(phoneNumber,otpCode)=>{setLoading(true);setError(null);try{const response=await authService.verifyOTP(phoneNumber,otpCode);if(response.success){return response;}else{setError(response.message||'رمز التحقق غير صحيح');return null;}}catch(error){setError(error.message||'خطأ في التحقق من الرمز');return null;}finally{setLoading(false);}};const resetOTP=()=>{setOtpSent(false);setError(null);};return{loading,error,otpSent,sendOTP,verifyOTP,resetOTP};};// Hook لتسجيل دخول المالك\nexport const useOwnerAuth=()=>{const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const{login}=useAuth();const ownerLogin=async password=>{setLoading(true);setError(null);try{const response=await authService.ownerLogin(password);if(response.success){login(response.data.owner,response.data.token);return response;}else{setError(response.message||'كلمة المرور غير صحيحة');return null;}}catch(error){setError(error.message||'خطأ في تسجيل الدخول');return null;}finally{setLoading(false);}};return{loading,error,ownerLogin};};export default useAuth;", "map": {"version": 3, "names": ["useState", "useEffect", "useContext", "createContext", "authService", "saveToLocalStorage", "getFromLocalStorage", "removeFromLocalStorage", "jsx", "_jsx", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuth", "token", "localStorage", "getItem", "userInfo", "response", "verifyToken", "success", "logout", "error", "console", "login", "userData", "setItem", "updateUser", "value", "Provider", "useAuth", "context", "Error", "useOTP", "setError", "otpSent", "setOtpSent", "sendOTP", "phoneNumber", "email", "language", "arguments", "length", "undefined", "message", "verifyOTP", "otpCode", "resetOTP", "useOwnerAuth", "owner<PERSON><PERSON><PERSON>", "password", "data", "owner"], "sources": ["D:/برمجة/tste 1/client/src/hooks/useAuth.js"], "sourcesContent": ["import { useState, useEffect, useContext, createContext } from 'react';\nimport { authService } from '../services/api';\nimport { saveToLocalStorage, getFromLocalStorage, removeFromLocalStorage } from '../utils/helpers';\n\n// إنشاء Context للمصادقة\nconst AuthContext = createContext();\n\n// Provider للمصادقة\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // التحقق من وجود token عند تحميل التطبيق\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userInfo = getFromLocalStorage('userInfo');\n        \n        if (token && userInfo) {\n          // التحقق من صحة الرمز المميز\n          const response = await authService.verifyToken();\n          if (response.success) {\n            setUser(userInfo);\n            setIsAuthenticated(true);\n          } else {\n            // إزالة البيانات غير الصحيحة\n            logout();\n          }\n        }\n      } catch (error) {\n        console.error('Error checking auth:', error);\n        logout();\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // تسجيل الدخول\n  const login = (userData, token) => {\n    localStorage.setItem('authToken', token);\n    saveToLocalStorage('userInfo', userData);\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n\n  // تسجيل الخروج\n  const logout = () => {\n    removeFromLocalStorage('authToken');\n    removeFromLocalStorage('userInfo');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  // تحديث بيانات المستخدم\n  const updateUser = (userData) => {\n    saveToLocalStorage('userInfo', userData);\n    setUser(userData);\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook لاستخدام المصادقة\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Hook لإرسال OTP\nexport const useOTP = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [otpSent, setOtpSent] = useState(false);\n\n  const sendOTP = async (phoneNumber, email, language = 'ar') => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await authService.sendOTP(phoneNumber, email, language);\n      if (response.success) {\n        setOtpSent(true);\n        return response;\n      } else {\n        setError(response.message || 'فشل في إرسال رمز التحقق');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في الشبكة');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const verifyOTP = async (phoneNumber, otpCode) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await authService.verifyOTP(phoneNumber, otpCode);\n      if (response.success) {\n        return response;\n      } else {\n        setError(response.message || 'رمز التحقق غير صحيح');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في التحقق من الرمز');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetOTP = () => {\n    setOtpSent(false);\n    setError(null);\n  };\n\n  return {\n    loading,\n    error,\n    otpSent,\n    sendOTP,\n    verifyOTP,\n    resetOTP,\n  };\n};\n\n// Hook لتسجيل دخول المالك\nexport const useOwnerAuth = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const { login } = useAuth();\n\n  const ownerLogin = async (password) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await authService.ownerLogin(password);\n      if (response.success) {\n        login(response.data.owner, response.data.token);\n        return response;\n      } else {\n        setError(response.message || 'كلمة المرور غير صحيحة');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في تسجيل الدخول');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    loading,\n    error,\n    ownerLogin,\n  };\n};\n\nexport default useAuth;\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,UAAU,CAAEC,aAAa,KAAQ,OAAO,CACtE,OAASC,WAAW,KAAQ,iBAAiB,CAC7C,OAASC,kBAAkB,CAAEC,mBAAmB,CAAEC,sBAAsB,KAAQ,kBAAkB,CAElG;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAAC,CAAC,CAEnC;AACA,MAAO,MAAM,CAAAQ,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkB,eAAe,CAAEC,kBAAkB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAGlB,mBAAmB,CAAC,UAAU,CAAC,CAEhD,GAAIe,KAAK,EAAIG,QAAQ,CAAE,CACrB;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAArB,WAAW,CAACsB,WAAW,CAAC,CAAC,CAChD,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpBZ,OAAO,CAACS,QAAQ,CAAC,CACjBL,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,CACL;AACAS,MAAM,CAAC,CAAC,CACV,CACF,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CD,MAAM,CAAC,CAAC,CACV,CAAC,OAAS,CACRX,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAW,KAAK,CAAGA,CAACC,QAAQ,CAAEX,KAAK,GAAK,CACjCC,YAAY,CAACW,OAAO,CAAC,WAAW,CAAEZ,KAAK,CAAC,CACxChB,kBAAkB,CAAC,UAAU,CAAE2B,QAAQ,CAAC,CACxCjB,OAAO,CAACiB,QAAQ,CAAC,CACjBb,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAS,MAAM,CAAGA,CAAA,GAAM,CACnBrB,sBAAsB,CAAC,WAAW,CAAC,CACnCA,sBAAsB,CAAC,UAAU,CAAC,CAClCQ,OAAO,CAAC,IAAI,CAAC,CACbI,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAe,UAAU,CAAIF,QAAQ,EAAK,CAC/B3B,kBAAkB,CAAC,UAAU,CAAE2B,QAAQ,CAAC,CACxCjB,OAAO,CAACiB,QAAQ,CAAC,CACnB,CAAC,CAED,KAAM,CAAAG,KAAK,CAAG,CACZrB,IAAI,CACJE,OAAO,CACPE,eAAe,CACfa,KAAK,CACLH,MAAM,CACNM,UACF,CAAC,CAED,mBACEzB,IAAA,CAACC,WAAW,CAAC0B,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAtB,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED;AACA,MAAO,MAAM,CAAAwB,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGpC,UAAU,CAACQ,WAAW,CAAC,CACvC,GAAI,CAAC4B,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,MAAM,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAACxB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC6B,KAAK,CAAEY,QAAQ,CAAC,CAAGzC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC0C,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAA4C,OAAO,CAAG,cAAAA,CAAOC,WAAW,CAAEC,KAAK,CAAsB,IAApB,CAAAC,QAAQ,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACxD/B,UAAU,CAAC,IAAI,CAAC,CAChBwB,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAArB,WAAW,CAACwC,OAAO,CAACC,WAAW,CAAEC,KAAK,CAAEC,QAAQ,CAAC,CACxE,GAAItB,QAAQ,CAACE,OAAO,CAAE,CACpBgB,UAAU,CAAC,IAAI,CAAC,CAChB,MAAO,CAAAlB,QAAQ,CACjB,CAAC,IAAM,CACLgB,QAAQ,CAAChB,QAAQ,CAAC0B,OAAO,EAAI,yBAAyB,CAAC,CACvD,MAAO,KAAI,CACb,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdY,QAAQ,CAACZ,KAAK,CAACsB,OAAO,EAAI,eAAe,CAAC,CAC1C,MAAO,KAAI,CACb,CAAC,OAAS,CACRlC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmC,SAAS,CAAG,KAAAA,CAAOP,WAAW,CAAEQ,OAAO,GAAK,CAChDpC,UAAU,CAAC,IAAI,CAAC,CAChBwB,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAArB,WAAW,CAACgD,SAAS,CAACP,WAAW,CAAEQ,OAAO,CAAC,CAClE,GAAI5B,QAAQ,CAACE,OAAO,CAAE,CACpB,MAAO,CAAAF,QAAQ,CACjB,CAAC,IAAM,CACLgB,QAAQ,CAAChB,QAAQ,CAAC0B,OAAO,EAAI,qBAAqB,CAAC,CACnD,MAAO,KAAI,CACb,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdY,QAAQ,CAACZ,KAAK,CAACsB,OAAO,EAAI,wBAAwB,CAAC,CACnD,MAAO,KAAI,CACb,CAAC,OAAS,CACRlC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAqC,QAAQ,CAAGA,CAAA,GAAM,CACrBX,UAAU,CAAC,KAAK,CAAC,CACjBF,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,CAED,MAAO,CACLzB,OAAO,CACPa,KAAK,CACLa,OAAO,CACPE,OAAO,CACPQ,SAAS,CACTE,QACF,CAAC,CACH,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACvC,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC6B,KAAK,CAAEY,QAAQ,CAAC,CAAGzC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAE+B,KAAM,CAAC,CAAGM,OAAO,CAAC,CAAC,CAE3B,KAAM,CAAAmB,UAAU,CAAG,KAAO,CAAAC,QAAQ,EAAK,CACrCxC,UAAU,CAAC,IAAI,CAAC,CAChBwB,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAArB,WAAW,CAACoD,UAAU,CAACC,QAAQ,CAAC,CACvD,GAAIhC,QAAQ,CAACE,OAAO,CAAE,CACpBI,KAAK,CAACN,QAAQ,CAACiC,IAAI,CAACC,KAAK,CAAElC,QAAQ,CAACiC,IAAI,CAACrC,KAAK,CAAC,CAC/C,MAAO,CAAAI,QAAQ,CACjB,CAAC,IAAM,CACLgB,QAAQ,CAAChB,QAAQ,CAAC0B,OAAO,EAAI,uBAAuB,CAAC,CACrD,MAAO,KAAI,CACb,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdY,QAAQ,CAACZ,KAAK,CAACsB,OAAO,EAAI,qBAAqB,CAAC,CAChD,MAAO,KAAI,CACb,CAAC,OAAS,CACRlC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,MAAO,CACLD,OAAO,CACPa,KAAK,CACL2B,UACF,CAAC,CACH,CAAC,CAED,cAAe,CAAAnB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}