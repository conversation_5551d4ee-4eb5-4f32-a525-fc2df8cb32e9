# نظام إدارة الشاشات التفاعلي
## Display Management System

نظام شامل لإدارة الشاشات التفاعلية مع دعم الدفع الإلكتروني والمصادقة عبر OTP.

## المميزات الرئيسية

### واجهة العميل
- **الصفحة الرئيسية**: شعار الشركة مع اختيار اللغة (عربي/إنجليزي)
- **اختيار الشاشة**: عرض الشاشات المتاحة (1-5) مع حالة كل شاشة
- **تسجيل الدخول**: مصادقة باستخدام رقم الهاتف و OTP عبر البريد الإلكتروني
- **إدخال الاسم**: لوحة مفاتيح تدعم العربية والإنجليزية
- **الدفع**: محاكاة دفع NFC باستخدام Stripe Sandbox
- **صفحة النجاح**: تأكيد المعاملة مع تفاصيل كاملة

### واجهة المالك
- **لوحة التحكم**: إحصائيات حية للمعاملات والإيرادات
- **مراقبة الشاشات**: عرض حالة جميع الشاشات في الوقت الفعلي
- **إدارة المعاملات**: عرض المعاملات النشطة والسابقة
- **الإعدادات**: تعديل الأسعار والمدة والرسائل

### شاشات العرض
- **عرض الأسماء**: عرض اسم العميل على الشاشة المختارة
- **العد التنازلي**: عرض الوقت المتبقي للعرض
- **التحديث المباشر**: تحديث المحتوى في الوقت الفعلي

## التقنيات المستخدمة

### الواجهة الأمامية (Frontend)
- **React.js**: مكتبة JavaScript لبناء واجهات المستخدم
- **React Router**: للتنقل بين الصفحات
- **Styled Components**: لتصميم المكونات
- **React i18next**: للترجمة ودعم اللغات المتعددة
- **Socket.IO Client**: للتحديثات المباشرة
- **Axios**: لطلبات HTTP
- **Stripe.js**: لمعالجة المدفوعات

### الواجهة الخلفية (Backend)
- **Node.js**: بيئة تشغيل JavaScript
- **Express.js**: إطار عمل الخادم
- **SQLite**: قاعدة البيانات
- **Socket.IO**: للاتصال المباشر
- **NodeMailer**: لإرسال البريد الإلكتروني
- **Stripe**: لمعالجة المدفوعات
- **JWT**: للمصادقة والتوثيق

### أدوات إضافية
- **Electron**: لتحويل التطبيق إلى تطبيق سطح مكتب
- **Electron Builder**: لبناء ملفات التثبيت

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd display-management-system
```

### 2. تثبيت التبعيات
```bash
# تثبيت تبعيات الخادم
npm install

# تثبيت تبعيات العميل
cd client
npm install
cd ..
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env` وقم بتعديل القيم:

```env
# إعدادات الخادم
PORT=3001
NODE_ENV=development

# إعدادات قاعدة البيانات
DB_PATH=./database/app.db

# إعدادات JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# إعدادات البريد الإلكتروني
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=Display Management System <<EMAIL>>

# إعدادات Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# إعدادات التطبيق
DEFAULT_DISPLAY_PRICE=50
DEFAULT_DISPLAY_DURATION=300
MAX_DISPLAYS=5
OTP_EXPIRY_MINUTES=5
OWNER_PASSWORD=admin123
```

### 4. تشغيل التطبيق

#### وضع التطوير
```bash
# تشغيل الخادم والعميل معاً
npm run dev
```

#### وضع الإنتاج
```bash
# بناء العميل
npm run build

# تشغيل الخادم
npm start
```

#### تشغيل Electron
```bash
# وضع التطوير
npm run electron-dev

# بناء ملف تنفيذي
npm run dist
```

## الاستخدام

### للعملاء
1. افتح التطبيق على `http://localhost:3000`
2. اختر اللغة المفضلة
3. اضغط "ابدأ المعاملة"
4. اختر الشاشة المطلوبة
5. أدخل رقم الهاتف والبريد الإلكتروني
6. أدخل رمز OTP المرسل للبريد
7. اكتب اسمك
8. أكمل عملية الدفع
9. شاهد اسمك على الشاشة المختارة

### للمالك
1. اذهب إلى `http://localhost:3000/owner-login`
2. أدخل كلمة المرور (افتراضياً: admin123)
3. راقب الإحصائيات والمعاملات
4. أدر إعدادات النظام

### لعرض الشاشات
- اذهب إلى `http://localhost:3000/display/[رقم الشاشة]`
- مثال: `http://localhost:3000/display/1`

## الميزات المتقدمة

### الدفع الإلكتروني
- تكامل مع Stripe Sandbox للاختبار
- محاكاة دفع NFC
- معالجة آمنة للمدفوعات

### المصادقة
- OTP عبر البريد الإلكتروني
- JWT للجلسات الآمنة
- حماية مسارات المالك

### التحديثات المباشرة
- Socket.IO للتحديثات الفورية
- مزامنة حالة الشاشات
- إشعارات المعاملات

## الأمان

- تشفير كلمات المرور
- حماية من CSRF
- تحديد معدل الطلبات
- تنظيف البيانات المدخلة

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال pull request.

---

تم تطوير هذا النظام بواسطة Augment Agent 🤖
