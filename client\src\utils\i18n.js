import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// ترجمات العربية
const arabicTranslations = {
  translation: {
    // الصفحة الرئيسية
    welcome: 'أهلاً وسهلاً بكم',
    companyName: 'نظام إدارة الشاشات',
    selectLanguage: 'اختر اللغة',
    startTransaction: 'ابدأ المعاملة',
    
    // اختيار الشاشة
    selectDisplay: 'اختر الشاشة',
    displayNumber: 'شاشة {{number}}',
    available: 'متاحة',
    occupied: 'مشغولة',
    reserved: 'محجوزة',
    maintenance: 'صيانة',
    timeRemaining: 'الوقت المتبقي: {{time}}',
    
    // تسجيل الدخول
    phoneNumber: 'رقم الهاتف',
    email: 'البريد الإلكتروني',
    enterPhoneNumber: 'أدخل رقم الهاتف',
    enterEmail: 'أدخل البريد الإلكتروني',
    sendOTP: 'إرسال رمز التحقق',
    otpCode: 'رمز التحقق',
    enterOTP: 'أدخل رمز التحقق',
    verifyOTP: 'تحقق من الرمز',
    otpSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',
    
    // إدخال الاسم
    customerName: 'اسم العميل',
    enterName: 'أدخل اسمك',
    displayPrice: 'السعر: {{price}} ريال',
    displayDuration: 'مدة العرض: {{duration}} دقيقة',
    proceedToPayment: 'المتابعة للدفع',
    
    // الدفع
    payment: 'الدفع',
    paymentMethod: 'طريقة الدفع',
    nfcPayment: 'دفع بالبطاقة اللاسلكية',
    cardPayment: 'دفع بالبطاقة',
    placeCardOnReader: 'ضع بطاقتك على القارئ',
    processing: 'جاري المعالجة...',
    paymentSuccessful: 'تم الدفع بنجاح',
    paymentFailed: 'فشل في الدفع',
    
    // المعاملة
    transactionNumber: 'رقم المعاملة',
    transactionDetails: 'تفاصيل المعاملة',
    amount: 'المبلغ',
    duration: 'المدة',
    startTime: 'وقت البداية',
    endTime: 'وقت الانتهاء',
    
    // أزرار عامة
    next: 'التالي',
    back: 'السابق',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    retry: 'إعادة المحاولة',
    close: 'إغلاق',
    
    // رسائل الخطأ
    error: 'خطأ',
    invalidPhoneNumber: 'رقم الهاتف غير صحيح',
    invalidEmail: 'البريد الإلكتروني غير صحيح',
    invalidOTP: 'رمز التحقق غير صحيح',
    networkError: 'خطأ في الشبكة',
    serverError: 'خطأ في الخادم',
    
    // واجهة المالك
    ownerDashboard: 'لوحة تحكم المالك',
    statistics: 'الإحصائيات',
    todayTransactions: 'معاملات اليوم',
    todayRevenue: 'إيرادات اليوم',
    totalTransactions: 'إجمالي المعاملات',
    totalRevenue: 'إجمالي الإيرادات',
    activeTransactions: 'المعاملات النشطة',
    displayStatus: 'حالة الشاشات',
    settings: 'الإعدادات',
    reports: 'التقارير',
    
    // الوقت
    minutes: 'دقيقة',
    seconds: 'ثانية',
    hours: 'ساعة',
    days: 'يوم',
  }
};

// ترجمات الإنجليزية
const englishTranslations = {
  translation: {
    // Home page
    welcome: 'Welcome',
    companyName: 'Display Management System',
    selectLanguage: 'Select Language',
    startTransaction: 'Start Transaction',
    
    // Display selection
    selectDisplay: 'Select Display',
    displayNumber: 'Display {{number}}',
    available: 'Available',
    occupied: 'Occupied',
    reserved: 'Reserved',
    maintenance: 'Maintenance',
    timeRemaining: 'Time remaining: {{time}}',
    
    // Login
    phoneNumber: 'Phone Number',
    email: 'Email',
    enterPhoneNumber: 'Enter phone number',
    enterEmail: 'Enter email address',
    sendOTP: 'Send Verification Code',
    otpCode: 'Verification Code',
    enterOTP: 'Enter verification code',
    verifyOTP: 'Verify Code',
    otpSent: 'Verification code sent to your email',
    
    // Name input
    customerName: 'Customer Name',
    enterName: 'Enter your name',
    displayPrice: 'Price: {{price}} SAR',
    displayDuration: 'Duration: {{duration}} minutes',
    proceedToPayment: 'Proceed to Payment',
    
    // Payment
    payment: 'Payment',
    paymentMethod: 'Payment Method',
    nfcPayment: 'NFC Card Payment',
    cardPayment: 'Card Payment',
    placeCardOnReader: 'Place your card on the reader',
    processing: 'Processing...',
    paymentSuccessful: 'Payment Successful',
    paymentFailed: 'Payment Failed',
    
    // Transaction
    transactionNumber: 'Transaction Number',
    transactionDetails: 'Transaction Details',
    amount: 'Amount',
    duration: 'Duration',
    startTime: 'Start Time',
    endTime: 'End Time',
    
    // General buttons
    next: 'Next',
    back: 'Back',
    cancel: 'Cancel',
    confirm: 'Confirm',
    retry: 'Retry',
    close: 'Close',
    
    // Error messages
    error: 'Error',
    invalidPhoneNumber: 'Invalid phone number',
    invalidEmail: 'Invalid email address',
    invalidOTP: 'Invalid verification code',
    networkError: 'Network error',
    serverError: 'Server error',
    
    // Owner interface
    ownerDashboard: 'Owner Dashboard',
    statistics: 'Statistics',
    todayTransactions: "Today's Transactions",
    todayRevenue: "Today's Revenue",
    totalTransactions: 'Total Transactions',
    totalRevenue: 'Total Revenue',
    activeTransactions: 'Active Transactions',
    displayStatus: 'Display Status',
    settings: 'Settings',
    reports: 'Reports',
    
    // Time
    minutes: 'minutes',
    seconds: 'seconds',
    hours: 'hours',
    days: 'days',
  }
};

// إعداد i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      ar: arabicTranslations,
      en: englishTranslations,
    },
    lng: localStorage.getItem('language') || 'ar', // اللغة الافتراضية
    fallbackLng: 'ar',
    
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    
    react: {
      useSuspense: false,
    },
  });

export default i18n;
