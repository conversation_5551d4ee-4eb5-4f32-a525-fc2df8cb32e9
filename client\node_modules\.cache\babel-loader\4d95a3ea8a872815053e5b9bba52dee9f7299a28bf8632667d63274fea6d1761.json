{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\DisplayScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { displayService } from '../services/api';\nimport { formatTimeRemaining, calculateTimeRemaining } from '../utils/helpers';\nimport io from 'socket.io-client';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n`;\nconst pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n`;\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${pulse} 4s ease-in-out infinite;\n  }\n`;\n_c = Container;\nconst DisplayCard = styled.div`\n  background: white;\n  border-radius: 30px;\n  padding: 60px;\n  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);\n  text-align: center;\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 1;\n  animation: ${fadeIn} 1s ease-out;\n`;\n_c2 = DisplayCard;\nconst DisplayNumber = styled.div`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  background: #667eea;\n  color: white;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 1.2rem;\n`;\n_c3 = DisplayNumber;\nconst CustomerName = styled.h1`\n  font-size: 4rem;\n  font-weight: bold;\n  color: #333;\n  margin: 40px 0;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);\n  word-break: break-word;\n  line-height: 1.2;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c4 = CustomerName;\nconst WelcomeMessage = styled.div`\n  font-size: 1.8rem;\n  color: #666;\n  margin-bottom: 30px;\n  font-weight: 600;\n\n  @media (max-width: 768px) {\n    font-size: 1.3rem;\n  }\n`;\n_c5 = WelcomeMessage;\nconst TimeRemaining = styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 30px 0;\n  border-left: 5px solid #667eea;\n`;\n_c6 = TimeRemaining;\nconst TimeLabel = styled.div`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 10px;\n`;\n_c7 = TimeLabel;\nconst TimeValue = styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #667eea;\n  font-family: 'Courier New', monospace;\n`;\n_c8 = TimeValue;\nconst StatusMessage = styled.div`\n  background: ${props => {\n  switch (props.type) {\n    case 'waiting':\n      return '#ffc107';\n    case 'active':\n      return '#28a745';\n    case 'ending':\n      return '#dc3545';\n    default:\n      return '#6c757d';\n  }\n}};\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin: 20px 0;\n`;\n_c9 = StatusMessage;\nconst LoadingSpinner = styled.div`\n  width: 60px;\n  height: 60px;\n  border: 6px solid #f3f3f3;\n  border-top: 6px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 40px auto;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n_c0 = LoadingSpinner;\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  text-align: center;\n  font-size: 1.2rem;\n`;\n_c1 = ErrorMessage;\nconst DisplayScreen = () => {\n  _s();\n  const {\n    displayId\n  } = useParams();\n  const [displayData, setDisplayData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [timeRemaining, setTimeRemaining] = useState(null);\n  const [socket, setSocket] = useState(null);\n  useEffect(() => {\n    var _process$env$REACT_AP;\n    // إعداد Socket.IO للتحديثات المباشرة\n    const newSocket = io(((_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace('/api', '')) || 'http://localhost:3001');\n    setSocket(newSocket);\n\n    // الانضمام لغرفة الشاشة\n    newSocket.emit('join-display', displayId);\n\n    // الاستماع للتحديثات\n    newSocket.on('display-activated', data => {\n      fetchDisplayData();\n    });\n    newSocket.on('transaction-ended', () => {\n      fetchDisplayData();\n    });\n    return () => {\n      newSocket.disconnect();\n    };\n  }, [displayId]);\n  useEffect(() => {\n    fetchDisplayData();\n\n    // تحديث البيانات كل 10 ثوان\n    const interval = setInterval(fetchDisplayData, 10000);\n    return () => clearInterval(interval);\n  }, [displayId]);\n  useEffect(() => {\n    // تحديث العد التنازلي كل ثانية\n    if (displayData && displayData.status === 'occupied' && displayData.endTime) {\n      const timer = setInterval(() => {\n        const remaining = calculateTimeRemaining(displayData.endTime);\n        setTimeRemaining(remaining);\n        if (remaining.expired) {\n          fetchDisplayData();\n        }\n      }, 1000);\n      return () => clearInterval(timer);\n    }\n  }, [displayData]);\n  const fetchDisplayData = async () => {\n    try {\n      const response = await displayService.getDisplay(displayId);\n      if (response.success) {\n        setDisplayData(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل بيانات الشاشة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching display data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(DisplayCard, {\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '1.2rem',\n            color: '#666',\n            marginTop: '20px'\n          },\n          children: \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0645\\u064A\\u0644...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(DisplayCard, {\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this);\n  }\n  if (!displayData) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(DisplayCard, {\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          children: \"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u063A\\u064A\\u0631 \\u0645\\u0648\\u062C\\u0648\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(DisplayCard, {\n      children: [/*#__PURE__*/_jsxDEV(DisplayNumber, {\n        children: [\"\\u0634\\u0627\\u0634\\u0629 \", displayData.displayNumber]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), displayData.status === 'available' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(WelcomeMessage, {\n          children: \"\\u0623\\u0647\\u0644\\u0627\\u064B \\u0648\\u0633\\u0647\\u0644\\u0627\\u064B \\u0628\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatusMessage, {\n          type: \"waiting\",\n          children: \"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u0645\\u062A\\u0627\\u062D\\u0629 \\u0644\\u0644\\u062D\\u062C\\u0632\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), displayData.status === 'occupied' && displayData.customerName && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(WelcomeMessage, {\n          children: \"\\u0623\\u0647\\u0644\\u0627\\u064B \\u0648\\u0633\\u0647\\u0644\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CustomerName, {\n          children: displayData.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), timeRemaining && !timeRemaining.expired && /*#__PURE__*/_jsxDEV(TimeRemaining, {\n          children: [/*#__PURE__*/_jsxDEV(TimeLabel, {\n            children: \"\\u0627\\u0644\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u062A\\u0628\\u0642\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TimeValue, {\n            children: formatTimeRemaining(timeRemaining.remaining)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(StatusMessage, {\n          type: timeRemaining && timeRemaining.remaining < 60 ? 'ending' : 'active',\n          children: timeRemaining && timeRemaining.remaining < 60 ? 'العرض على وشك الانتهاء' : 'العرض نشط الآن'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), displayData.status === 'reserved' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(WelcomeMessage, {\n          children: \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0636\\u064A\\u0631...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatusMessage, {\n          type: \"waiting\",\n          children: \"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u0645\\u062D\\u062C\\u0648\\u0632\\u0629 - \\u0641\\u064A \\u0627\\u0646\\u062A\\u0638\\u0627\\u0631 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), displayData.status === 'maintenance' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(WelcomeMessage, {\n          children: \"\\u0646\\u0639\\u062A\\u0630\\u0631 \\u0644\\u0644\\u0625\\u0632\\u0639\\u0627\\u062C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatusMessage, {\n          type: \"maintenance\",\n          children: \"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u062A\\u062D\\u062A \\u0627\\u0644\\u0635\\u064A\\u0627\\u0646\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(DisplayScreen, \"mppwv31UKxfMH3CdQnYRolKRMKY=\", false, function () {\n  return [useParams];\n});\n_c10 = DisplayScreen;\nexport default DisplayScreen;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"DisplayCard\");\n$RefreshReg$(_c3, \"DisplayNumber\");\n$RefreshReg$(_c4, \"CustomerName\");\n$RefreshReg$(_c5, \"WelcomeMessage\");\n$RefreshReg$(_c6, \"TimeRemaining\");\n$RefreshReg$(_c7, \"TimeLabel\");\n$RefreshReg$(_c8, \"TimeValue\");\n$RefreshReg$(_c9, \"StatusMessage\");\n$RefreshReg$(_c0, \"LoadingSpinner\");\n$RefreshReg$(_c1, \"ErrorMessage\");\n$RefreshReg$(_c10, \"DisplayScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "styled", "keyframes", "displayService", "formatTimeRemaining", "calculateTimeRemaining", "io", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "fadeIn", "pulse", "Container", "div", "_c", "DisplayCard", "_c2", "DisplayNumber", "_c3", "CustomerName", "h1", "_c4", "WelcomeMessage", "_c5", "TimeRemaining", "_c6", "TimeLabel", "_c7", "TimeValue", "_c8", "StatusMessage", "props", "type", "_c9", "LoadingSpinner", "_c0", "ErrorMessage", "_c1", "DisplayScreen", "_s", "displayId", "displayData", "setDisplayData", "loading", "setLoading", "error", "setError", "timeRemaining", "setTimeRemaining", "socket", "setSocket", "_process$env$REACT_AP", "newSocket", "process", "env", "REACT_APP_API_URL", "replace", "emit", "on", "data", "fetchDisplayData", "disconnect", "interval", "setInterval", "clearInterval", "status", "endTime", "timer", "remaining", "expired", "response", "getDisplay", "success", "message", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "marginTop", "displayNumber", "customerName", "_c10", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/DisplayScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { displayService } from '../services/api';\nimport { formatTimeRemaining, calculateTimeRemaining } from '../utils/helpers';\nimport io from 'socket.io-client';\n\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n`;\n\nconst pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n`;\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${pulse} 4s ease-in-out infinite;\n  }\n`;\n\nconst DisplayCard = styled.div`\n  background: white;\n  border-radius: 30px;\n  padding: 60px;\n  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);\n  text-align: center;\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 1;\n  animation: ${fadeIn} 1s ease-out;\n`;\n\nconst DisplayNumber = styled.div`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  background: #667eea;\n  color: white;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 1.2rem;\n`;\n\nconst CustomerName = styled.h1`\n  font-size: 4rem;\n  font-weight: bold;\n  color: #333;\n  margin: 40px 0;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);\n  word-break: break-word;\n  line-height: 1.2;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n\nconst WelcomeMessage = styled.div`\n  font-size: 1.8rem;\n  color: #666;\n  margin-bottom: 30px;\n  font-weight: 600;\n\n  @media (max-width: 768px) {\n    font-size: 1.3rem;\n  }\n`;\n\nconst TimeRemaining = styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 30px 0;\n  border-left: 5px solid #667eea;\n`;\n\nconst TimeLabel = styled.div`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 10px;\n`;\n\nconst TimeValue = styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #667eea;\n  font-family: 'Courier New', monospace;\n`;\n\nconst StatusMessage = styled.div`\n  background: ${props => {\n    switch (props.type) {\n      case 'waiting': return '#ffc107';\n      case 'active': return '#28a745';\n      case 'ending': return '#dc3545';\n      default: return '#6c757d';\n    }\n  }};\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin: 20px 0;\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 60px;\n  height: 60px;\n  border: 6px solid #f3f3f3;\n  border-top: 6px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 40px auto;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  text-align: center;\n  font-size: 1.2rem;\n`;\n\nconst DisplayScreen = () => {\n  const { displayId } = useParams();\n  const [displayData, setDisplayData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [timeRemaining, setTimeRemaining] = useState(null);\n  const [socket, setSocket] = useState(null);\n\n  useEffect(() => {\n    // إعداد Socket.IO للتحديثات المباشرة\n    const newSocket = io(process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:3001');\n    setSocket(newSocket);\n\n    // الانضمام لغرفة الشاشة\n    newSocket.emit('join-display', displayId);\n\n    // الاستماع للتحديثات\n    newSocket.on('display-activated', (data) => {\n      fetchDisplayData();\n    });\n\n    newSocket.on('transaction-ended', () => {\n      fetchDisplayData();\n    });\n\n    return () => {\n      newSocket.disconnect();\n    };\n  }, [displayId]);\n\n  useEffect(() => {\n    fetchDisplayData();\n    \n    // تحديث البيانات كل 10 ثوان\n    const interval = setInterval(fetchDisplayData, 10000);\n    return () => clearInterval(interval);\n  }, [displayId]);\n\n  useEffect(() => {\n    // تحديث العد التنازلي كل ثانية\n    if (displayData && displayData.status === 'occupied' && displayData.endTime) {\n      const timer = setInterval(() => {\n        const remaining = calculateTimeRemaining(displayData.endTime);\n        setTimeRemaining(remaining);\n        \n        if (remaining.expired) {\n          fetchDisplayData();\n        }\n      }, 1000);\n\n      return () => clearInterval(timer);\n    }\n  }, [displayData]);\n\n  const fetchDisplayData = async () => {\n    try {\n      const response = await displayService.getDisplay(displayId);\n      if (response.success) {\n        setDisplayData(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل بيانات الشاشة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching display data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container>\n        <DisplayCard>\n          <LoadingSpinner />\n          <div style={{ fontSize: '1.2rem', color: '#666', marginTop: '20px' }}>\n            جاري التحميل...\n          </div>\n        </DisplayCard>\n      </Container>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container>\n        <DisplayCard>\n          <ErrorMessage>{error}</ErrorMessage>\n        </DisplayCard>\n      </Container>\n    );\n  }\n\n  if (!displayData) {\n    return (\n      <Container>\n        <DisplayCard>\n          <ErrorMessage>الشاشة غير موجودة</ErrorMessage>\n        </DisplayCard>\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      <DisplayCard>\n        <DisplayNumber>\n          شاشة {displayData.displayNumber}\n        </DisplayNumber>\n\n        {displayData.status === 'available' && (\n          <>\n            <WelcomeMessage>أهلاً وسهلاً بكم</WelcomeMessage>\n            <StatusMessage type=\"waiting\">\n              الشاشة متاحة للحجز\n            </StatusMessage>\n          </>\n        )}\n\n        {displayData.status === 'occupied' && displayData.customerName && (\n          <>\n            <WelcomeMessage>أهلاً وسهلاً</WelcomeMessage>\n            <CustomerName>{displayData.customerName}</CustomerName>\n            \n            {timeRemaining && !timeRemaining.expired && (\n              <TimeRemaining>\n                <TimeLabel>الوقت المتبقي</TimeLabel>\n                <TimeValue>{formatTimeRemaining(timeRemaining.remaining)}</TimeValue>\n              </TimeRemaining>\n            )}\n\n            <StatusMessage type={timeRemaining && timeRemaining.remaining < 60 ? 'ending' : 'active'}>\n              {timeRemaining && timeRemaining.remaining < 60 ? \n                'العرض على وشك الانتهاء' : \n                'العرض نشط الآن'\n              }\n            </StatusMessage>\n          </>\n        )}\n\n        {displayData.status === 'reserved' && (\n          <>\n            <WelcomeMessage>جاري التحضير...</WelcomeMessage>\n            <StatusMessage type=\"waiting\">\n              الشاشة محجوزة - في انتظار الدفع\n            </StatusMessage>\n          </>\n        )}\n\n        {displayData.status === 'maintenance' && (\n          <>\n            <WelcomeMessage>نعتذر للإزعاج</WelcomeMessage>\n            <StatusMessage type=\"maintenance\">\n              الشاشة تحت الصيانة\n            </StatusMessage>\n          </>\n        )}\n      </DisplayCard>\n    </Container>\n  );\n};\n\nexport default DisplayScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,mBAAmB,EAAEC,sBAAsB,QAAQ,kBAAkB;AAC9E,OAAOC,EAAE,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,MAAM,GAAGT,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMU,KAAK,GAAGV,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMW,SAAS,GAAGZ,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBF,KAAK;AACtB;AACA,CAAC;AAACG,EAAA,GApBIF,SAAS;AAsBf,MAAMG,WAAW,GAAGf,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,MAAM;AACrB,CAAC;AAACM,GAAA,GAXID,WAAW;AAajB,MAAME,aAAa,GAAGjB,MAAM,CAACa,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAVID,aAAa;AAYnB,MAAME,YAAY,GAAGnB,MAAM,CAACoB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIF,YAAY;AAclB,MAAMG,cAAc,GAAGtB,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAGxB,MAAM,CAACa,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,aAAa;AAQnB,MAAME,SAAS,GAAG1B,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAG5B,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,SAAS;AAOf,MAAME,aAAa,GAAG9B,MAAM,CAACa,GAAG;AAChC,gBAAgBkB,KAAK,IAAI;EACrB,QAAQA,KAAK,CAACC,IAAI;IAChB,KAAK,SAAS;MAAE,OAAO,SAAS;IAChC,KAAK,QAAQ;MAAE,OAAO,SAAS;IAC/B,KAAK,QAAQ;MAAE,OAAO,SAAS;IAC/B;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIH,aAAa;AAiBnB,MAAMI,cAAc,GAAGlC,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAbID,cAAc;AAepB,MAAME,YAAY,GAAGpC,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAPID,YAAY;AASlB,MAAME,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAU,CAAC,GAAGzC,SAAS,CAAC,CAAC;EACjC,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAE1CC,SAAS,CAAC,MAAM;IAAA,IAAAqD,qBAAA;IACd;IACA,MAAMC,SAAS,GAAG/C,EAAE,CAAC,EAAA8C,qBAAA,GAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,KAAI,uBAAuB,CAAC;IACnGN,SAAS,CAACE,SAAS,CAAC;;IAEpB;IACAA,SAAS,CAACK,IAAI,CAAC,cAAc,EAAEjB,SAAS,CAAC;;IAEzC;IACAY,SAAS,CAACM,EAAE,CAAC,mBAAmB,EAAGC,IAAI,IAAK;MAC1CC,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;IAEFR,SAAS,CAACM,EAAE,CAAC,mBAAmB,EAAE,MAAM;MACtCE,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;IAEF,OAAO,MAAM;MACXR,SAAS,CAACS,UAAU,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACrB,SAAS,CAAC,CAAC;EAEf1C,SAAS,CAAC,MAAM;IACd8D,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAME,QAAQ,GAAGC,WAAW,CAACH,gBAAgB,EAAE,KAAK,CAAC;IACrD,OAAO,MAAMI,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACtB,SAAS,CAAC,CAAC;EAEf1C,SAAS,CAAC,MAAM;IACd;IACA,IAAI2C,WAAW,IAAIA,WAAW,CAACwB,MAAM,KAAK,UAAU,IAAIxB,WAAW,CAACyB,OAAO,EAAE;MAC3E,MAAMC,KAAK,GAAGJ,WAAW,CAAC,MAAM;QAC9B,MAAMK,SAAS,GAAGhE,sBAAsB,CAACqC,WAAW,CAACyB,OAAO,CAAC;QAC7DlB,gBAAgB,CAACoB,SAAS,CAAC;QAE3B,IAAIA,SAAS,CAACC,OAAO,EAAE;UACrBT,gBAAgB,CAAC,CAAC;QACpB;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMI,aAAa,CAACG,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAAC1B,WAAW,CAAC,CAAC;EAEjB,MAAMmB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMpE,cAAc,CAACqE,UAAU,CAAC/B,SAAS,CAAC;MAC3D,IAAI8B,QAAQ,CAACE,OAAO,EAAE;QACpB9B,cAAc,CAAC4B,QAAQ,CAACX,IAAI,CAAC;QAC7Bb,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACLA,QAAQ,CAACwB,QAAQ,CAACG,OAAO,IAAI,4BAA4B,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,QAAQ,CAAC,eAAe,CAAC;MACzB4B,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEpC,OAAA,CAACK,SAAS;MAAA+D,QAAA,eACRpE,OAAA,CAACQ,WAAW;QAAA4D,QAAA,gBACVpE,OAAA,CAAC2B,cAAc;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClBxE,OAAA;UAAKyE,KAAK,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEhB;EAEA,IAAIlC,KAAK,EAAE;IACT,oBACEtC,OAAA,CAACK,SAAS;MAAA+D,QAAA,eACRpE,OAAA,CAACQ,WAAW;QAAA4D,QAAA,eACVpE,OAAA,CAAC6B,YAAY;UAAAuC,QAAA,EAAE9B;QAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEhB;EAEA,IAAI,CAACtC,WAAW,EAAE;IAChB,oBACElC,OAAA,CAACK,SAAS;MAAA+D,QAAA,eACRpE,OAAA,CAACQ,WAAW;QAAA4D,QAAA,eACVpE,OAAA,CAAC6B,YAAY;UAAAuC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEhB;EAEA,oBACExE,OAAA,CAACK,SAAS;IAAA+D,QAAA,eACRpE,OAAA,CAACQ,WAAW;MAAA4D,QAAA,gBACVpE,OAAA,CAACU,aAAa;QAAA0D,QAAA,GAAC,2BACR,EAAClC,WAAW,CAAC2C,aAAa;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,EAEftC,WAAW,CAACwB,MAAM,KAAK,WAAW,iBACjC1D,OAAA,CAAAE,SAAA;QAAAkE,QAAA,gBACEpE,OAAA,CAACe,cAAc;UAAAqD,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eACjDxE,OAAA,CAACuB,aAAa;UAACE,IAAI,EAAC,SAAS;UAAA2C,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA,eAChB,CACH,EAEAtC,WAAW,CAACwB,MAAM,KAAK,UAAU,IAAIxB,WAAW,CAAC4C,YAAY,iBAC5D9E,OAAA,CAAAE,SAAA;QAAAkE,QAAA,gBACEpE,OAAA,CAACe,cAAc;UAAAqD,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAC7CxE,OAAA,CAACY,YAAY;UAAAwD,QAAA,EAAElC,WAAW,CAAC4C;QAAY;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,EAEtDhC,aAAa,IAAI,CAACA,aAAa,CAACsB,OAAO,iBACtC9D,OAAA,CAACiB,aAAa;UAAAmD,QAAA,gBACZpE,OAAA,CAACmB,SAAS;YAAAiD,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACpCxE,OAAA,CAACqB,SAAS;YAAA+C,QAAA,EAAExE,mBAAmB,CAAC4C,aAAa,CAACqB,SAAS;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAChB,eAEDxE,OAAA,CAACuB,aAAa;UAACE,IAAI,EAAEe,aAAa,IAAIA,aAAa,CAACqB,SAAS,GAAG,EAAE,GAAG,QAAQ,GAAG,QAAS;UAAAO,QAAA,EACtF5B,aAAa,IAAIA,aAAa,CAACqB,SAAS,GAAG,EAAE,GAC5C,wBAAwB,GACxB;QAAgB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL,CAAC;MAAA,eAChB,CACH,EAEAtC,WAAW,CAACwB,MAAM,KAAK,UAAU,iBAChC1D,OAAA,CAAAE,SAAA;QAAAkE,QAAA,gBACEpE,OAAA,CAACe,cAAc;UAAAqD,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAChDxE,OAAA,CAACuB,aAAa;UAACE,IAAI,EAAC,SAAS;UAAA2C,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA,eAChB,CACH,EAEAtC,WAAW,CAACwB,MAAM,KAAK,aAAa,iBACnC1D,OAAA,CAAAE,SAAA;QAAAkE,QAAA,gBACEpE,OAAA,CAACe,cAAc;UAAAqD,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAC9CxE,OAAA,CAACuB,aAAa;UAACE,IAAI,EAAC,aAAa;UAAA2C,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA,eAChB,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEhB,CAAC;AAACxC,EAAA,CAjKID,aAAa;EAAA,QACKvC,SAAS;AAAA;AAAAuF,IAAA,GAD3BhD,aAAa;AAmKnB,eAAeA,aAAa;AAAC,IAAAxB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAiD,IAAA;AAAAC,YAAA,CAAAzE,EAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}