{"name": "display-management-system", "version": "1.0.0", "description": "نظام إدارة الشاشات التفاعلي مع واجهة العميل والمالك", "main": "src/main.js", "homepage": "./", "scripts": {"start": "node src/server.js", "dev": "concurrently \"nodemon src/server.js\" \"npm run client\"", "client": "cd client && npm start", "build": "cd client && npm run build", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "dist": "npm run build && electron-builder", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["display", "management", "nfc", "payment", "kiosk"], "author": "Display Management System", "license": "MIT", "build": {"appId": "com.displaymanagement.app", "productName": "نظام إدارة الشاشات", "directories": {"output": "dist"}, "files": ["src/**/*", "client/build/**/*", "database/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "mac": {"target": "dmg", "icon": "public/icon.icns"}, "linux": {"target": "AppImage", "icon": "public/icon.png"}}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "i18next": "^25.3.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "react-i18next": "^15.6.0", "react-router-dom": "^7.6.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "stripe": "^18.3.0", "styled-components": "^6.1.19", "uuid": "^11.1.0"}, "devDependencies": {"concurrently": "^9.2.0", "electron": "^37.2.1", "electron-builder": "^26.0.12", "nodemon": "^3.1.10"}}