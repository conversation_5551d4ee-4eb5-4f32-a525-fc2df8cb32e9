{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams}from'react-router-dom';import styled,{keyframes}from'styled-components';import{displayService}from'../services/api';import{formatTimeRemaining,calculateTimeRemaining}from'../utils/helpers';import io from'socket.io-client';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const fadeIn=keyframes`\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n`;const pulse=keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n`;const Container=styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${pulse} 4s ease-in-out infinite;\n  }\n`;const DisplayCard=styled.div`\n  background: white;\n  border-radius: 30px;\n  padding: 60px;\n  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);\n  text-align: center;\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 1;\n  animation: ${fadeIn} 1s ease-out;\n`;const DisplayNumber=styled.div`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  background: #667eea;\n  color: white;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 1.2rem;\n`;const CustomerName=styled.h1`\n  font-size: 4rem;\n  font-weight: bold;\n  color: #333;\n  margin: 40px 0;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);\n  word-break: break-word;\n  line-height: 1.2;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;const WelcomeMessage=styled.div`\n  font-size: 1.8rem;\n  color: #666;\n  margin-bottom: 30px;\n  font-weight: 600;\n\n  @media (max-width: 768px) {\n    font-size: 1.3rem;\n  }\n`;const TimeRemaining=styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 30px 0;\n  border-left: 5px solid #667eea;\n`;const TimeLabel=styled.div`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 10px;\n`;const TimeValue=styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #667eea;\n  font-family: 'Courier New', monospace;\n`;const StatusMessage=styled.div`\n  background: ${props=>{switch(props.type){case'waiting':return'#ffc107';case'active':return'#28a745';case'ending':return'#dc3545';default:return'#6c757d';}}};\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin: 20px 0;\n`;const LoadingSpinner=styled.div`\n  width: 60px;\n  height: 60px;\n  border: 6px solid #f3f3f3;\n  border-top: 6px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 40px auto;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  text-align: center;\n  font-size: 1.2rem;\n`;const DisplayScreen=()=>{const{displayId}=useParams();const[displayData,setDisplayData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[timeRemaining,setTimeRemaining]=useState(null);const[socket,setSocket]=useState(null);useEffect(()=>{var _process$env$REACT_AP;// إعداد Socket.IO للتحديثات المباشرة\nconst newSocket=io(((_process$env$REACT_AP=process.env.REACT_APP_API_URL)===null||_process$env$REACT_AP===void 0?void 0:_process$env$REACT_AP.replace('/api',''))||'http://localhost:3001');setSocket(newSocket);// الانضمام لغرفة الشاشة\nnewSocket.emit('join-display',displayId);// الاستماع للتحديثات\nnewSocket.on('display-activated',data=>{fetchDisplayData();});newSocket.on('transaction-ended',()=>{fetchDisplayData();});return()=>{newSocket.disconnect();};},[displayId]);useEffect(()=>{fetchDisplayData();// تحديث البيانات كل 10 ثوان\nconst interval=setInterval(fetchDisplayData,10000);return()=>clearInterval(interval);},[displayId]);useEffect(()=>{// تحديث العد التنازلي كل ثانية\nif(displayData&&displayData.status==='occupied'&&displayData.endTime){const timer=setInterval(()=>{const remaining=calculateTimeRemaining(displayData.endTime);setTimeRemaining(remaining);if(remaining.expired){fetchDisplayData();}},1000);return()=>clearInterval(timer);}},[displayData]);const fetchDisplayData=async()=>{try{const response=await displayService.getDisplay(displayId);if(response.success){setDisplayData(response.data);setError(null);}else{setError(response.message||'فشل في تحميل بيانات الشاشة');}}catch(error){setError('خطأ في الشبكة');console.error('Error fetching display data:',error);}finally{setLoading(false);}};if(loading){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsxs(DisplayCard,{children:[/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'1.2rem',color:'#666',marginTop:'20px'},children:\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0645\\u064A\\u0644...\"})]})});}if(error){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(DisplayCard,{children:/*#__PURE__*/_jsx(ErrorMessage,{children:error})})});}if(!displayData){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(DisplayCard,{children:/*#__PURE__*/_jsx(ErrorMessage,{children:\"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u063A\\u064A\\u0631 \\u0645\\u0648\\u062C\\u0648\\u062F\\u0629\"})})});}return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsxs(DisplayCard,{children:[/*#__PURE__*/_jsxs(DisplayNumber,{children:[\"\\u0634\\u0627\\u0634\\u0629 \",displayData.displayNumber]}),displayData.status==='available'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(WelcomeMessage,{children:\"\\u0623\\u0647\\u0644\\u0627\\u064B \\u0648\\u0633\\u0647\\u0644\\u0627\\u064B \\u0628\\u0643\\u0645\"}),/*#__PURE__*/_jsx(StatusMessage,{type:\"waiting\",children:\"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u0645\\u062A\\u0627\\u062D\\u0629 \\u0644\\u0644\\u062D\\u062C\\u0632\"})]}),displayData.status==='occupied'&&displayData.customerName&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(WelcomeMessage,{children:\"\\u0623\\u0647\\u0644\\u0627\\u064B \\u0648\\u0633\\u0647\\u0644\\u0627\\u064B\"}),/*#__PURE__*/_jsx(CustomerName,{children:displayData.customerName}),timeRemaining&&!timeRemaining.expired&&/*#__PURE__*/_jsxs(TimeRemaining,{children:[/*#__PURE__*/_jsx(TimeLabel,{children:\"\\u0627\\u0644\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u062A\\u0628\\u0642\\u064A\"}),/*#__PURE__*/_jsx(TimeValue,{children:formatTimeRemaining(timeRemaining.remaining)})]}),/*#__PURE__*/_jsx(StatusMessage,{type:timeRemaining&&timeRemaining.remaining<60?'ending':'active',children:timeRemaining&&timeRemaining.remaining<60?'العرض على وشك الانتهاء':'العرض نشط الآن'})]}),displayData.status==='reserved'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(WelcomeMessage,{children:\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0636\\u064A\\u0631...\"}),/*#__PURE__*/_jsx(StatusMessage,{type:\"waiting\",children:\"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u0645\\u062D\\u062C\\u0648\\u0632\\u0629 - \\u0641\\u064A \\u0627\\u0646\\u062A\\u0638\\u0627\\u0631 \\u0627\\u0644\\u062F\\u0641\\u0639\"})]}),displayData.status==='maintenance'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(WelcomeMessage,{children:\"\\u0646\\u0639\\u062A\\u0630\\u0631 \\u0644\\u0644\\u0625\\u0632\\u0639\\u0627\\u062C\"}),/*#__PURE__*/_jsx(StatusMessage,{type:\"maintenance\",children:\"\\u0627\\u0644\\u0634\\u0627\\u0634\\u0629 \\u062A\\u062D\\u062A \\u0627\\u0644\\u0635\\u064A\\u0627\\u0646\\u0629\"})]})]})});};export default DisplayScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "styled", "keyframes", "displayService", "formatTimeRemaining", "calculateTimeRemaining", "io", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "fadeIn", "pulse", "Container", "div", "DisplayCard", "DisplayNumber", "CustomerName", "h1", "WelcomeMessage", "TimeRemaining", "TimeLabel", "TimeValue", "StatusMessage", "props", "type", "LoadingSpinner", "ErrorMessage", "DisplayScreen", "displayId", "displayData", "setDisplayData", "loading", "setLoading", "error", "setError", "timeRemaining", "setTimeRemaining", "socket", "setSocket", "_process$env$REACT_AP", "newSocket", "process", "env", "REACT_APP_API_URL", "replace", "emit", "on", "data", "fetchDisplayData", "disconnect", "interval", "setInterval", "clearInterval", "status", "endTime", "timer", "remaining", "expired", "response", "getDisplay", "success", "message", "console", "children", "style", "fontSize", "color", "marginTop", "displayNumber", "customerName"], "sources": ["D:/برمجة/tste 1/client/src/pages/DisplayScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { displayService } from '../services/api';\nimport { formatTimeRemaining, calculateTimeRemaining } from '../utils/helpers';\nimport io from 'socket.io-client';\n\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n`;\n\nconst pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n`;\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${pulse} 4s ease-in-out infinite;\n  }\n`;\n\nconst DisplayCard = styled.div`\n  background: white;\n  border-radius: 30px;\n  padding: 60px;\n  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);\n  text-align: center;\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 1;\n  animation: ${fadeIn} 1s ease-out;\n`;\n\nconst DisplayNumber = styled.div`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  background: #667eea;\n  color: white;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 1.2rem;\n`;\n\nconst CustomerName = styled.h1`\n  font-size: 4rem;\n  font-weight: bold;\n  color: #333;\n  margin: 40px 0;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);\n  word-break: break-word;\n  line-height: 1.2;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n\nconst WelcomeMessage = styled.div`\n  font-size: 1.8rem;\n  color: #666;\n  margin-bottom: 30px;\n  font-weight: 600;\n\n  @media (max-width: 768px) {\n    font-size: 1.3rem;\n  }\n`;\n\nconst TimeRemaining = styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 30px 0;\n  border-left: 5px solid #667eea;\n`;\n\nconst TimeLabel = styled.div`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 10px;\n`;\n\nconst TimeValue = styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #667eea;\n  font-family: 'Courier New', monospace;\n`;\n\nconst StatusMessage = styled.div`\n  background: ${props => {\n    switch (props.type) {\n      case 'waiting': return '#ffc107';\n      case 'active': return '#28a745';\n      case 'ending': return '#dc3545';\n      default: return '#6c757d';\n    }\n  }};\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin: 20px 0;\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 60px;\n  height: 60px;\n  border: 6px solid #f3f3f3;\n  border-top: 6px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 40px auto;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  text-align: center;\n  font-size: 1.2rem;\n`;\n\nconst DisplayScreen = () => {\n  const { displayId } = useParams();\n  const [displayData, setDisplayData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [timeRemaining, setTimeRemaining] = useState(null);\n  const [socket, setSocket] = useState(null);\n\n  useEffect(() => {\n    // إعداد Socket.IO للتحديثات المباشرة\n    const newSocket = io(process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:3001');\n    setSocket(newSocket);\n\n    // الانضمام لغرفة الشاشة\n    newSocket.emit('join-display', displayId);\n\n    // الاستماع للتحديثات\n    newSocket.on('display-activated', (data) => {\n      fetchDisplayData();\n    });\n\n    newSocket.on('transaction-ended', () => {\n      fetchDisplayData();\n    });\n\n    return () => {\n      newSocket.disconnect();\n    };\n  }, [displayId]);\n\n  useEffect(() => {\n    fetchDisplayData();\n    \n    // تحديث البيانات كل 10 ثوان\n    const interval = setInterval(fetchDisplayData, 10000);\n    return () => clearInterval(interval);\n  }, [displayId]);\n\n  useEffect(() => {\n    // تحديث العد التنازلي كل ثانية\n    if (displayData && displayData.status === 'occupied' && displayData.endTime) {\n      const timer = setInterval(() => {\n        const remaining = calculateTimeRemaining(displayData.endTime);\n        setTimeRemaining(remaining);\n        \n        if (remaining.expired) {\n          fetchDisplayData();\n        }\n      }, 1000);\n\n      return () => clearInterval(timer);\n    }\n  }, [displayData]);\n\n  const fetchDisplayData = async () => {\n    try {\n      const response = await displayService.getDisplay(displayId);\n      if (response.success) {\n        setDisplayData(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل بيانات الشاشة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching display data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container>\n        <DisplayCard>\n          <LoadingSpinner />\n          <div style={{ fontSize: '1.2rem', color: '#666', marginTop: '20px' }}>\n            جاري التحميل...\n          </div>\n        </DisplayCard>\n      </Container>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container>\n        <DisplayCard>\n          <ErrorMessage>{error}</ErrorMessage>\n        </DisplayCard>\n      </Container>\n    );\n  }\n\n  if (!displayData) {\n    return (\n      <Container>\n        <DisplayCard>\n          <ErrorMessage>الشاشة غير موجودة</ErrorMessage>\n        </DisplayCard>\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      <DisplayCard>\n        <DisplayNumber>\n          شاشة {displayData.displayNumber}\n        </DisplayNumber>\n\n        {displayData.status === 'available' && (\n          <>\n            <WelcomeMessage>أهلاً وسهلاً بكم</WelcomeMessage>\n            <StatusMessage type=\"waiting\">\n              الشاشة متاحة للحجز\n            </StatusMessage>\n          </>\n        )}\n\n        {displayData.status === 'occupied' && displayData.customerName && (\n          <>\n            <WelcomeMessage>أهلاً وسهلاً</WelcomeMessage>\n            <CustomerName>{displayData.customerName}</CustomerName>\n            \n            {timeRemaining && !timeRemaining.expired && (\n              <TimeRemaining>\n                <TimeLabel>الوقت المتبقي</TimeLabel>\n                <TimeValue>{formatTimeRemaining(timeRemaining.remaining)}</TimeValue>\n              </TimeRemaining>\n            )}\n\n            <StatusMessage type={timeRemaining && timeRemaining.remaining < 60 ? 'ending' : 'active'}>\n              {timeRemaining && timeRemaining.remaining < 60 ? \n                'العرض على وشك الانتهاء' : \n                'العرض نشط الآن'\n              }\n            </StatusMessage>\n          </>\n        )}\n\n        {displayData.status === 'reserved' && (\n          <>\n            <WelcomeMessage>جاري التحضير...</WelcomeMessage>\n            <StatusMessage type=\"waiting\">\n              الشاشة محجوزة - في انتظار الدفع\n            </StatusMessage>\n          </>\n        )}\n\n        {displayData.status === 'maintenance' && (\n          <>\n            <WelcomeMessage>نعتذر للإزعاج</WelcomeMessage>\n            <StatusMessage type=\"maintenance\">\n              الشاشة تحت الصيانة\n            </StatusMessage>\n          </>\n        )}\n      </DisplayCard>\n    </Container>\n  );\n};\n\nexport default DisplayScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,MAAO,CAAAC,MAAM,EAAIC,SAAS,KAAQ,mBAAmB,CACrD,OAASC,cAAc,KAAQ,iBAAiB,CAChD,OAASC,mBAAmB,CAAEC,sBAAsB,KAAQ,kBAAkB,CAC9E,MAAO,CAAAC,EAAE,KAAM,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAElC,KAAM,CAAAC,MAAM,CAAGX,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,KAAK,CAAGZ,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,SAAS,CAAGd,MAAM,CAACe,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBF,KAAK;AACtB;AACA,CAAC,CAED,KAAM,CAAAG,WAAW,CAAGhB,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,MAAM;AACrB,CAAC,CAED,KAAM,CAAAK,aAAa,CAAGjB,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGlB,MAAM,CAACmB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGpB,MAAM,CAACe,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAM,aAAa,CAAGrB,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAO,SAAS,CAAGtB,MAAM,CAACe,GAAG;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,SAAS,CAAGvB,MAAM,CAACe,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,aAAa,CAAGxB,MAAM,CAACe,GAAG;AAChC,gBAAgBU,KAAK,EAAI,CACrB,OAAQA,KAAK,CAACC,IAAI,EAChB,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,IAAK,QAAQ,CAAE,MAAO,SAAS,CAC/B,IAAK,QAAQ,CAAE,MAAO,SAAS,CAC/B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAG3B,MAAM,CAACe,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,YAAY,CAAG5B,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAc,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,SAAU,CAAC,CAAG/B,SAAS,CAAC,CAAC,CACjC,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACoC,OAAO,CAAEC,UAAU,CAAC,CAAGrC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsC,KAAK,CAAEC,QAAQ,CAAC,CAAGvC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC0C,MAAM,CAAEC,SAAS,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CAE1CC,SAAS,CAAC,IAAM,KAAA2C,qBAAA,CACd;AACA,KAAM,CAAAC,SAAS,CAAGrC,EAAE,CAAC,EAAAoC,qBAAA,CAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,UAAAJ,qBAAA,iBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,GAAI,uBAAuB,CAAC,CACnGN,SAAS,CAACE,SAAS,CAAC,CAEpB;AACAA,SAAS,CAACK,IAAI,CAAC,cAAc,CAAEjB,SAAS,CAAC,CAEzC;AACAY,SAAS,CAACM,EAAE,CAAC,mBAAmB,CAAGC,IAAI,EAAK,CAC1CC,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAC,CAEFR,SAAS,CAACM,EAAE,CAAC,mBAAmB,CAAE,IAAM,CACtCE,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAC,CAEF,MAAO,IAAM,CACXR,SAAS,CAACS,UAAU,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,CAAE,CAACrB,SAAS,CAAC,CAAC,CAEfhC,SAAS,CAAC,IAAM,CACdoD,gBAAgB,CAAC,CAAC,CAElB;AACA,KAAM,CAAAE,QAAQ,CAAGC,WAAW,CAACH,gBAAgB,CAAE,KAAK,CAAC,CACrD,MAAO,IAAMI,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACtB,SAAS,CAAC,CAAC,CAEfhC,SAAS,CAAC,IAAM,CACd;AACA,GAAIiC,WAAW,EAAIA,WAAW,CAACwB,MAAM,GAAK,UAAU,EAAIxB,WAAW,CAACyB,OAAO,CAAE,CAC3E,KAAM,CAAAC,KAAK,CAAGJ,WAAW,CAAC,IAAM,CAC9B,KAAM,CAAAK,SAAS,CAAGtD,sBAAsB,CAAC2B,WAAW,CAACyB,OAAO,CAAC,CAC7DlB,gBAAgB,CAACoB,SAAS,CAAC,CAE3B,GAAIA,SAAS,CAACC,OAAO,CAAE,CACrBT,gBAAgB,CAAC,CAAC,CACpB,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMI,aAAa,CAACG,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,CAAC1B,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAAmB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAA1D,cAAc,CAAC2D,UAAU,CAAC/B,SAAS,CAAC,CAC3D,GAAI8B,QAAQ,CAACE,OAAO,CAAE,CACpB9B,cAAc,CAAC4B,QAAQ,CAACX,IAAI,CAAC,CAC7Bb,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,IAAM,CACLA,QAAQ,CAACwB,QAAQ,CAACG,OAAO,EAAI,4BAA4B,CAAC,CAC5D,CACF,CAAE,MAAO5B,KAAK,CAAE,CACdC,QAAQ,CAAC,eAAe,CAAC,CACzB4B,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAID,OAAO,CAAE,CACX,mBACE1B,IAAA,CAACO,SAAS,EAAAmD,QAAA,cACRxD,KAAA,CAACO,WAAW,EAAAiD,QAAA,eACV1D,IAAA,CAACoB,cAAc,GAAE,CAAC,cAClBpB,IAAA,QAAK2D,KAAK,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,KAAK,CAAE,MAAM,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,wEAEtE,CAAK,CAAC,EACK,CAAC,CACL,CAAC,CAEhB,CAEA,GAAI9B,KAAK,CAAE,CACT,mBACE5B,IAAA,CAACO,SAAS,EAAAmD,QAAA,cACR1D,IAAA,CAACS,WAAW,EAAAiD,QAAA,cACV1D,IAAA,CAACqB,YAAY,EAAAqC,QAAA,CAAE9B,KAAK,CAAe,CAAC,CACzB,CAAC,CACL,CAAC,CAEhB,CAEA,GAAI,CAACJ,WAAW,CAAE,CAChB,mBACExB,IAAA,CAACO,SAAS,EAAAmD,QAAA,cACR1D,IAAA,CAACS,WAAW,EAAAiD,QAAA,cACV1D,IAAA,CAACqB,YAAY,EAAAqC,QAAA,CAAC,8FAAiB,CAAc,CAAC,CACnC,CAAC,CACL,CAAC,CAEhB,CAEA,mBACE1D,IAAA,CAACO,SAAS,EAAAmD,QAAA,cACRxD,KAAA,CAACO,WAAW,EAAAiD,QAAA,eACVxD,KAAA,CAACQ,aAAa,EAAAgD,QAAA,EAAC,2BACR,CAAClC,WAAW,CAACuC,aAAa,EAClB,CAAC,CAEfvC,WAAW,CAACwB,MAAM,GAAK,WAAW,eACjC9C,KAAA,CAAAE,SAAA,EAAAsD,QAAA,eACE1D,IAAA,CAACa,cAAc,EAAA6C,QAAA,CAAC,wFAAgB,CAAgB,CAAC,cACjD1D,IAAA,CAACiB,aAAa,EAACE,IAAI,CAAC,SAAS,CAAAuC,QAAA,CAAC,oGAE9B,CAAe,CAAC,EAChB,CACH,CAEAlC,WAAW,CAACwB,MAAM,GAAK,UAAU,EAAIxB,WAAW,CAACwC,YAAY,eAC5D9D,KAAA,CAAAE,SAAA,EAAAsD,QAAA,eACE1D,IAAA,CAACa,cAAc,EAAA6C,QAAA,CAAC,qEAAY,CAAgB,CAAC,cAC7C1D,IAAA,CAACW,YAAY,EAAA+C,QAAA,CAAElC,WAAW,CAACwC,YAAY,CAAe,CAAC,CAEtDlC,aAAa,EAAI,CAACA,aAAa,CAACsB,OAAO,eACtClD,KAAA,CAACY,aAAa,EAAA4C,QAAA,eACZ1D,IAAA,CAACe,SAAS,EAAA2C,QAAA,CAAC,2EAAa,CAAW,CAAC,cACpC1D,IAAA,CAACgB,SAAS,EAAA0C,QAAA,CAAE9D,mBAAmB,CAACkC,aAAa,CAACqB,SAAS,CAAC,CAAY,CAAC,EACxD,CAChB,cAEDnD,IAAA,CAACiB,aAAa,EAACE,IAAI,CAAEW,aAAa,EAAIA,aAAa,CAACqB,SAAS,CAAG,EAAE,CAAG,QAAQ,CAAG,QAAS,CAAAO,QAAA,CACtF5B,aAAa,EAAIA,aAAa,CAACqB,SAAS,CAAG,EAAE,CAC5C,wBAAwB,CACxB,gBAAgB,CAEL,CAAC,EAChB,CACH,CAEA3B,WAAW,CAACwB,MAAM,GAAK,UAAU,eAChC9C,KAAA,CAAAE,SAAA,EAAAsD,QAAA,eACE1D,IAAA,CAACa,cAAc,EAAA6C,QAAA,CAAC,wEAAe,CAAgB,CAAC,cAChD1D,IAAA,CAACiB,aAAa,EAACE,IAAI,CAAC,SAAS,CAAAuC,QAAA,CAAC,8JAE9B,CAAe,CAAC,EAChB,CACH,CAEAlC,WAAW,CAACwB,MAAM,GAAK,aAAa,eACnC9C,KAAA,CAAAE,SAAA,EAAAsD,QAAA,eACE1D,IAAA,CAACa,cAAc,EAAA6C,QAAA,CAAC,2EAAa,CAAgB,CAAC,cAC9C1D,IAAA,CAACiB,aAAa,EAACE,IAAI,CAAC,aAAa,CAAAuC,QAAA,CAAC,oGAElC,CAAe,CAAC,EAChB,CACH,EACU,CAAC,CACL,CAAC,CAEhB,CAAC,CAED,cAAe,CAAApC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}