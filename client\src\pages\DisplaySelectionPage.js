import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { displayService } from '../services/api';
import { formatTimeRemaining } from '../utils/helpers';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Header = styled.div`
  text-align: center;
  color: white;
  margin-bottom: 30px;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
`;

const DisplayGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const DisplayCard = styled.div`
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  cursor: ${props => props.available ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.available ? 1 : 0.6};
  transition: all 0.3s ease;
  border: 3px solid ${props => {
    switch (props.status) {
      case 'available': return '#28a745';
      case 'occupied': return '#dc3545';
      case 'reserved': return '#ffc107';
      case 'maintenance': return '#6c757d';
      default: return '#ddd';
    }
  }};

  &:hover {
    transform: ${props => props.available ? 'translateY(-5px)' : 'none'};
    box-shadow: ${props => props.available ? '0 15px 40px rgba(0, 0, 0, 0.15)' : '0 10px 30px rgba(0, 0, 0, 0.1)'};
  }
`;

const DisplayNumber = styled.div`
  font-size: 3rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 15px;
`;

const DisplayName = styled.h3`
  font-size: 1.3rem;
  color: #333;
  text-align: center;
  margin-bottom: 15px;
`;

const StatusBadge = styled.div`
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 15px;
  width: 100%;
  color: white;
  background: ${props => {
    switch (props.status) {
      case 'available': return '#28a745';
      case 'occupied': return '#dc3545';
      case 'reserved': return '#ffc107';
      case 'maintenance': return '#6c757d';
      default: return '#ddd';
    }
  }};
`;

const CustomerInfo = styled.div`
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  margin-top: 15px;
`;

const CustomerName = styled.div`
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
`;

const TimeRemaining = styled.div`
  color: #dc3545;
  font-weight: 600;
  font-size: 1.1rem;
`;

const BackButton = styled.button`
  position: fixed;
  top: 20px;
  ${props => props.isRTL ? 'right: 20px' : 'left: 20px'};
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: white;
    color: #667eea;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: white;
  font-size: 1.2rem;
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  margin: 20px auto;
  max-width: 500px;
`;

const DisplaySelectionPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [displays, setDisplays] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    fetchDisplays();
    
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchDisplays, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchDisplays = async () => {
    try {
      const response = await displayService.getAllDisplays();
      if (response.success) {
        setDisplays(response.data);
        setError(null);
      } else {
        setError(response.message || 'فشل في تحميل الشاشات');
      }
    } catch (error) {
      setError('خطأ في الشبكة');
      console.error('Error fetching displays:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDisplaySelect = (display) => {
    if (display.status === 'available') {
      navigate('/login', { 
        state: { 
          selectedDisplay: display 
        } 
      });
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'available': return t('available');
      case 'occupied': return t('occupied');
      case 'reserved': return t('reserved');
      case 'maintenance': return t('maintenance');
      default: return status;
    }
  };

  if (loading) {
    return (
      <Container isRTL={isRTL}>
        <LoadingSpinner>
          {isRTL ? 'جاري التحميل...' : 'Loading...'}
        </LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container isRTL={isRTL}>
      <BackButton isRTL={isRTL} onClick={handleBack}>
        {t('back')}
      </BackButton>

      <Header>
        <Title>{t('selectDisplay')}</Title>
        <Subtitle>
          {isRTL ? 'اختر الشاشة التي تريد عرض اسمك عليها' : 'Choose the display to show your name'}
        </Subtitle>
      </Header>

      {error && (
        <ErrorMessage>
          {error}
        </ErrorMessage>
      )}

      <DisplayGrid>
        {displays.map((display) => (
          <DisplayCard
            key={display.id}
            available={display.status === 'available'}
            status={display.status}
            onClick={() => handleDisplaySelect(display)}
          >
            <DisplayNumber>
              {display.displayNumber}
            </DisplayNumber>
            
            <DisplayName>
              {display.name}
            </DisplayName>
            
            <StatusBadge status={display.status}>
              {getStatusText(display.status)}
            </StatusBadge>

            {display.status === 'occupied' && display.customerName && (
              <CustomerInfo>
                <CustomerName>
                  {isRTL ? 'العميل: ' : 'Customer: '}{display.customerName}
                </CustomerName>
                {display.timeRemaining && !display.timeRemaining.expired && (
                  <TimeRemaining>
                    {t('timeRemaining', { 
                      time: formatTimeRemaining(display.timeRemaining.remaining) 
                    })}
                  </TimeRemaining>
                )}
              </CustomerInfo>
            )}

            {display.status === 'reserved' && display.customerName && (
              <CustomerInfo>
                <CustomerName>
                  {isRTL ? 'محجوز لـ: ' : 'Reserved for: '}{display.customerName}
                </CustomerName>
              </CustomerInfo>
            )}
          </DisplayCard>
        ))}
      </DisplayGrid>
    </Container>
  );
};

export default DisplaySelectionPage;
