{"ast": null, "code": "import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\n\n// ترجمات العربية\nconst arabicTranslations = {\n  translation: {\n    // الصفحة الرئيسية\n    welcome: 'أهلاً وسهلاً بكم',\n    companyName: 'نظام إدارة الشاشات',\n    selectLanguage: 'اختر اللغة',\n    startTransaction: 'ابدأ المعاملة',\n    // اختيار الشاشة\n    selectDisplay: 'اختر الشاشة',\n    displayNumber: 'شاشة {{number}}',\n    available: 'متاحة',\n    occupied: 'مشغولة',\n    reserved: 'محجوزة',\n    maintenance: 'صيانة',\n    timeRemaining: 'الوقت المتبقي: {{time}}',\n    // تسجيل الدخول\n    phoneNumber: 'رقم الهاتف',\n    email: 'البريد الإلكتروني',\n    enterPhoneNumber: 'أدخل رقم الهاتف',\n    enterEmail: 'أدخل البريد الإلكتروني',\n    sendOTP: 'إرسال رمز التحقق',\n    otpCode: 'رمز التحقق',\n    enterOTP: 'أدخل رمز التحقق',\n    verifyOTP: 'تحقق من الرمز',\n    otpSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',\n    // إدخال الاسم\n    customerName: 'اسم العميل',\n    enterName: 'أدخل اسمك',\n    displayPrice: 'السعر: {{price}} ريال',\n    displayDuration: 'مدة العرض: {{duration}} دقيقة',\n    proceedToPayment: 'المتابعة للدفع',\n    // الدفع\n    payment: 'الدفع',\n    paymentMethod: 'طريقة الدفع',\n    nfcPayment: 'دفع بالبطاقة اللاسلكية',\n    cardPayment: 'دفع بالبطاقة',\n    placeCardOnReader: 'ضع بطاقتك على القارئ',\n    processing: 'جاري المعالجة...',\n    paymentSuccessful: 'تم الدفع بنجاح',\n    paymentFailed: 'فشل في الدفع',\n    // المعاملة\n    transactionNumber: 'رقم المعاملة',\n    transactionDetails: 'تفاصيل المعاملة',\n    amount: 'المبلغ',\n    duration: 'المدة',\n    startTime: 'وقت البداية',\n    endTime: 'وقت الانتهاء',\n    // أزرار عامة\n    next: 'التالي',\n    back: 'السابق',\n    cancel: 'إلغاء',\n    confirm: 'تأكيد',\n    retry: 'إعادة المحاولة',\n    close: 'إغلاق',\n    // رسائل الخطأ\n    error: 'خطأ',\n    invalidPhoneNumber: 'رقم الهاتف غير صحيح',\n    invalidEmail: 'البريد الإلكتروني غير صحيح',\n    invalidOTP: 'رمز التحقق غير صحيح',\n    networkError: 'خطأ في الشبكة',\n    serverError: 'خطأ في الخادم',\n    // واجهة المالك\n    ownerDashboard: 'لوحة تحكم المالك',\n    statistics: 'الإحصائيات',\n    todayTransactions: 'معاملات اليوم',\n    todayRevenue: 'إيرادات اليوم',\n    totalTransactions: 'إجمالي المعاملات',\n    totalRevenue: 'إجمالي الإيرادات',\n    activeTransactions: 'المعاملات النشطة',\n    displayStatus: 'حالة الشاشات',\n    settings: 'الإعدادات',\n    reports: 'التقارير',\n    // الوقت\n    minutes: 'دقيقة',\n    seconds: 'ثانية',\n    hours: 'ساعة',\n    days: 'يوم'\n  }\n};\n\n// ترجمات الإنجليزية\nconst englishTranslations = {\n  translation: {\n    // Home page\n    welcome: 'Welcome',\n    companyName: 'Display Management System',\n    selectLanguage: 'Select Language',\n    startTransaction: 'Start Transaction',\n    // Display selection\n    selectDisplay: 'Select Display',\n    displayNumber: 'Display {{number}}',\n    available: 'Available',\n    occupied: 'Occupied',\n    reserved: 'Reserved',\n    maintenance: 'Maintenance',\n    timeRemaining: 'Time remaining: {{time}}',\n    // Login\n    phoneNumber: 'Phone Number',\n    email: 'Email',\n    enterPhoneNumber: 'Enter phone number',\n    enterEmail: 'Enter email address',\n    sendOTP: 'Send Verification Code',\n    otpCode: 'Verification Code',\n    enterOTP: 'Enter verification code',\n    verifyOTP: 'Verify Code',\n    otpSent: 'Verification code sent to your email',\n    // Name input\n    customerName: 'Customer Name',\n    enterName: 'Enter your name',\n    displayPrice: 'Price: {{price}} SAR',\n    displayDuration: 'Duration: {{duration}} minutes',\n    proceedToPayment: 'Proceed to Payment',\n    // Payment\n    payment: 'Payment',\n    paymentMethod: 'Payment Method',\n    nfcPayment: 'NFC Card Payment',\n    cardPayment: 'Card Payment',\n    placeCardOnReader: 'Place your card on the reader',\n    processing: 'Processing...',\n    paymentSuccessful: 'Payment Successful',\n    paymentFailed: 'Payment Failed',\n    // Transaction\n    transactionNumber: 'Transaction Number',\n    transactionDetails: 'Transaction Details',\n    amount: 'Amount',\n    duration: 'Duration',\n    startTime: 'Start Time',\n    endTime: 'End Time',\n    // General buttons\n    next: 'Next',\n    back: 'Back',\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    retry: 'Retry',\n    close: 'Close',\n    // Error messages\n    error: 'Error',\n    invalidPhoneNumber: 'Invalid phone number',\n    invalidEmail: 'Invalid email address',\n    invalidOTP: 'Invalid verification code',\n    networkError: 'Network error',\n    serverError: 'Server error',\n    // Owner interface\n    ownerDashboard: 'Owner Dashboard',\n    statistics: 'Statistics',\n    todayTransactions: \"Today's Transactions\",\n    todayRevenue: \"Today's Revenue\",\n    totalTransactions: 'Total Transactions',\n    totalRevenue: 'Total Revenue',\n    activeTransactions: 'Active Transactions',\n    displayStatus: 'Display Status',\n    settings: 'Settings',\n    reports: 'Reports',\n    // Time\n    minutes: 'minutes',\n    seconds: 'seconds',\n    hours: 'hours',\n    days: 'days'\n  }\n};\n\n// إعداد i18n\ni18n.use(initReactI18next).init({\n  resources: {\n    ar: arabicTranslations,\n    en: englishTranslations\n  },\n  lng: localStorage.getItem('language') || 'ar',\n  // اللغة الافتراضية\n  fallbackLng: 'ar',\n  interpolation: {\n    escapeValue: false // React already escapes values\n  },\n  react: {\n    useSuspense: false\n  }\n});\nexport default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "arabicTranslations", "translation", "welcome", "companyName", "selectLanguage", "startTransaction", "selectDisplay", "displayNumber", "available", "occupied", "reserved", "maintenance", "timeRemaining", "phoneNumber", "email", "enterPhoneNumber", "enterEmail", "sendOTP", "otpCode", "enterOTP", "verifyOTP", "otpSent", "customerName", "enterName", "displayPrice", "displayDuration", "proceedToPayment", "payment", "paymentMethod", "nfcPayment", "cardPayment", "placeCardOnReader", "processing", "paymentSuccessful", "paymentFailed", "transactionNumber", "transactionDetails", "amount", "duration", "startTime", "endTime", "next", "back", "cancel", "confirm", "retry", "close", "error", "invalidPhoneNumber", "invalidEmail", "invalidOTP", "networkError", "serverError", "ownerDashboard", "statistics", "todayTransactions", "todayRevenue", "totalTransactions", "totalRevenue", "activeTransactions", "displayStatus", "settings", "reports", "minutes", "seconds", "hours", "days", "englishTranslations", "use", "init", "resources", "ar", "en", "lng", "localStorage", "getItem", "fallbackLng", "interpolation", "escapeValue", "react", "useSuspense"], "sources": ["D:/برمجة/tste 1/client/src/utils/i18n.js"], "sourcesContent": ["import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\n\n// ترجمات العربية\nconst arabicTranslations = {\n  translation: {\n    // الصفحة الرئيسية\n    welcome: 'أهلاً وسهلاً بكم',\n    companyName: 'نظام إدارة الشاشات',\n    selectLanguage: 'اختر اللغة',\n    startTransaction: 'ابدأ المعاملة',\n    \n    // اختيار الشاشة\n    selectDisplay: 'اختر الشاشة',\n    displayNumber: 'شاشة {{number}}',\n    available: 'متاحة',\n    occupied: 'مشغولة',\n    reserved: 'محجوزة',\n    maintenance: 'صيانة',\n    timeRemaining: 'الوقت المتبقي: {{time}}',\n    \n    // تسجيل الدخول\n    phoneNumber: 'رقم الهاتف',\n    email: 'البريد الإلكتروني',\n    enterPhoneNumber: 'أدخل رقم الهاتف',\n    enterEmail: 'أدخل البريد الإلكتروني',\n    sendOTP: 'إرسال رمز التحقق',\n    otpCode: 'رمز التحقق',\n    enterOTP: 'أدخل رمز التحقق',\n    verifyOTP: 'تحقق من الرمز',\n    otpSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',\n    \n    // إدخال الاسم\n    customerName: 'اسم العميل',\n    enterName: 'أدخل اسمك',\n    displayPrice: 'السعر: {{price}} ريال',\n    displayDuration: 'مدة العرض: {{duration}} دقيقة',\n    proceedToPayment: 'المتابعة للدفع',\n    \n    // الدفع\n    payment: 'الدفع',\n    paymentMethod: 'طريقة الدفع',\n    nfcPayment: 'دفع بالبطاقة اللاسلكية',\n    cardPayment: 'دفع بالبطاقة',\n    placeCardOnReader: 'ضع بطاقتك على القارئ',\n    processing: 'جاري المعالجة...',\n    paymentSuccessful: 'تم الدفع بنجاح',\n    paymentFailed: 'فشل في الدفع',\n    \n    // المعاملة\n    transactionNumber: 'رقم المعاملة',\n    transactionDetails: 'تفاصيل المعاملة',\n    amount: 'المبلغ',\n    duration: 'المدة',\n    startTime: 'وقت البداية',\n    endTime: 'وقت الانتهاء',\n    \n    // أزرار عامة\n    next: 'التالي',\n    back: 'السابق',\n    cancel: 'إلغاء',\n    confirm: 'تأكيد',\n    retry: 'إعادة المحاولة',\n    close: 'إغلاق',\n    \n    // رسائل الخطأ\n    error: 'خطأ',\n    invalidPhoneNumber: 'رقم الهاتف غير صحيح',\n    invalidEmail: 'البريد الإلكتروني غير صحيح',\n    invalidOTP: 'رمز التحقق غير صحيح',\n    networkError: 'خطأ في الشبكة',\n    serverError: 'خطأ في الخادم',\n    \n    // واجهة المالك\n    ownerDashboard: 'لوحة تحكم المالك',\n    statistics: 'الإحصائيات',\n    todayTransactions: 'معاملات اليوم',\n    todayRevenue: 'إيرادات اليوم',\n    totalTransactions: 'إجمالي المعاملات',\n    totalRevenue: 'إجمالي الإيرادات',\n    activeTransactions: 'المعاملات النشطة',\n    displayStatus: 'حالة الشاشات',\n    settings: 'الإعدادات',\n    reports: 'التقارير',\n    \n    // الوقت\n    minutes: 'دقيقة',\n    seconds: 'ثانية',\n    hours: 'ساعة',\n    days: 'يوم',\n  }\n};\n\n// ترجمات الإنجليزية\nconst englishTranslations = {\n  translation: {\n    // Home page\n    welcome: 'Welcome',\n    companyName: 'Display Management System',\n    selectLanguage: 'Select Language',\n    startTransaction: 'Start Transaction',\n    \n    // Display selection\n    selectDisplay: 'Select Display',\n    displayNumber: 'Display {{number}}',\n    available: 'Available',\n    occupied: 'Occupied',\n    reserved: 'Reserved',\n    maintenance: 'Maintenance',\n    timeRemaining: 'Time remaining: {{time}}',\n    \n    // Login\n    phoneNumber: 'Phone Number',\n    email: 'Email',\n    enterPhoneNumber: 'Enter phone number',\n    enterEmail: 'Enter email address',\n    sendOTP: 'Send Verification Code',\n    otpCode: 'Verification Code',\n    enterOTP: 'Enter verification code',\n    verifyOTP: 'Verify Code',\n    otpSent: 'Verification code sent to your email',\n    \n    // Name input\n    customerName: 'Customer Name',\n    enterName: 'Enter your name',\n    displayPrice: 'Price: {{price}} SAR',\n    displayDuration: 'Duration: {{duration}} minutes',\n    proceedToPayment: 'Proceed to Payment',\n    \n    // Payment\n    payment: 'Payment',\n    paymentMethod: 'Payment Method',\n    nfcPayment: 'NFC Card Payment',\n    cardPayment: 'Card Payment',\n    placeCardOnReader: 'Place your card on the reader',\n    processing: 'Processing...',\n    paymentSuccessful: 'Payment Successful',\n    paymentFailed: 'Payment Failed',\n    \n    // Transaction\n    transactionNumber: 'Transaction Number',\n    transactionDetails: 'Transaction Details',\n    amount: 'Amount',\n    duration: 'Duration',\n    startTime: 'Start Time',\n    endTime: 'End Time',\n    \n    // General buttons\n    next: 'Next',\n    back: 'Back',\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    retry: 'Retry',\n    close: 'Close',\n    \n    // Error messages\n    error: 'Error',\n    invalidPhoneNumber: 'Invalid phone number',\n    invalidEmail: 'Invalid email address',\n    invalidOTP: 'Invalid verification code',\n    networkError: 'Network error',\n    serverError: 'Server error',\n    \n    // Owner interface\n    ownerDashboard: 'Owner Dashboard',\n    statistics: 'Statistics',\n    todayTransactions: \"Today's Transactions\",\n    todayRevenue: \"Today's Revenue\",\n    totalTransactions: 'Total Transactions',\n    totalRevenue: 'Total Revenue',\n    activeTransactions: 'Active Transactions',\n    displayStatus: 'Display Status',\n    settings: 'Settings',\n    reports: 'Reports',\n    \n    // Time\n    minutes: 'minutes',\n    seconds: 'seconds',\n    hours: 'hours',\n    days: 'days',\n  }\n};\n\n// إعداد i18n\ni18n\n  .use(initReactI18next)\n  .init({\n    resources: {\n      ar: arabicTranslations,\n      en: englishTranslations,\n    },\n    lng: localStorage.getItem('language') || 'ar', // اللغة الافتراضية\n    fallbackLng: 'ar',\n    \n    interpolation: {\n      escapeValue: false, // React already escapes values\n    },\n    \n    react: {\n      useSuspense: false,\n    },\n  });\n\nexport default i18n;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,SAASC,gBAAgB,QAAQ,eAAe;;AAEhD;AACA,MAAMC,kBAAkB,GAAG;EACzBC,WAAW,EAAE;IACX;IACAC,OAAO,EAAE,kBAAkB;IAC3BC,WAAW,EAAE,oBAAoB;IACjCC,cAAc,EAAE,YAAY;IAC5BC,gBAAgB,EAAE,eAAe;IAEjC;IACAC,aAAa,EAAE,aAAa;IAC5BC,aAAa,EAAE,iBAAiB;IAChCC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,yBAAyB;IAExC;IACAC,WAAW,EAAE,YAAY;IACzBC,KAAK,EAAE,mBAAmB;IAC1BC,gBAAgB,EAAE,iBAAiB;IACnCC,UAAU,EAAE,wBAAwB;IACpCC,OAAO,EAAE,kBAAkB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,0CAA0C;IAEnD;IACAC,YAAY,EAAE,YAAY;IAC1BC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,uBAAuB;IACrCC,eAAe,EAAE,+BAA+B;IAChDC,gBAAgB,EAAE,gBAAgB;IAElC;IACAC,OAAO,EAAE,OAAO;IAChBC,aAAa,EAAE,aAAa;IAC5BC,UAAU,EAAE,wBAAwB;IACpCC,WAAW,EAAE,cAAc;IAC3BC,iBAAiB,EAAE,sBAAsB;IACzCC,UAAU,EAAE,kBAAkB;IAC9BC,iBAAiB,EAAE,gBAAgB;IACnCC,aAAa,EAAE,cAAc;IAE7B;IACAC,iBAAiB,EAAE,cAAc;IACjCC,kBAAkB,EAAE,iBAAiB;IACrCC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,cAAc;IAEvB;IACAC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,OAAO;IAEd;IACAC,KAAK,EAAE,KAAK;IACZC,kBAAkB,EAAE,qBAAqB;IACzCC,YAAY,EAAE,4BAA4B;IAC1CC,UAAU,EAAE,qBAAqB;IACjCC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,eAAe;IAE5B;IACAC,cAAc,EAAE,kBAAkB;IAClCC,UAAU,EAAE,YAAY;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,YAAY,EAAE,eAAe;IAC7BC,iBAAiB,EAAE,kBAAkB;IACrCC,YAAY,EAAE,kBAAkB;IAChCC,kBAAkB,EAAE,kBAAkB;IACtCC,aAAa,EAAE,cAAc;IAC7BC,QAAQ,EAAE,WAAW;IACrBC,OAAO,EAAE,UAAU;IAEnB;IACAC,OAAO,EAAE,OAAO;IAChBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR;AACF,CAAC;;AAED;AACA,MAAMC,mBAAmB,GAAG;EAC1BlE,WAAW,EAAE;IACX;IACAC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,2BAA2B;IACxCC,cAAc,EAAE,iBAAiB;IACjCC,gBAAgB,EAAE,mBAAmB;IAErC;IACAC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAE,oBAAoB;IACnCC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,aAAa,EAAE,0BAA0B;IAEzC;IACAC,WAAW,EAAE,cAAc;IAC3BC,KAAK,EAAE,OAAO;IACdC,gBAAgB,EAAE,oBAAoB;IACtCC,UAAU,EAAE,qBAAqB;IACjCC,OAAO,EAAE,wBAAwB;IACjCC,OAAO,EAAE,mBAAmB;IAC5BC,QAAQ,EAAE,yBAAyB;IACnCC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,sCAAsC;IAE/C;IACAC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,iBAAiB;IAC5BC,YAAY,EAAE,sBAAsB;IACpCC,eAAe,EAAE,gCAAgC;IACjDC,gBAAgB,EAAE,oBAAoB;IAEtC;IACAC,OAAO,EAAE,SAAS;IAClBC,aAAa,EAAE,gBAAgB;IAC/BC,UAAU,EAAE,kBAAkB;IAC9BC,WAAW,EAAE,cAAc;IAC3BC,iBAAiB,EAAE,+BAA+B;IAClDC,UAAU,EAAE,eAAe;IAC3BC,iBAAiB,EAAE,oBAAoB;IACvCC,aAAa,EAAE,gBAAgB;IAE/B;IACAC,iBAAiB,EAAE,oBAAoB;IACvCC,kBAAkB,EAAE,qBAAqB;IACzCC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,UAAU;IAEnB;IACAC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,OAAO;IAEd;IACAC,KAAK,EAAE,OAAO;IACdC,kBAAkB,EAAE,sBAAsB;IAC1CC,YAAY,EAAE,uBAAuB;IACrCC,UAAU,EAAE,2BAA2B;IACvCC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,cAAc;IAE3B;IACAC,cAAc,EAAE,iBAAiB;IACjCC,UAAU,EAAE,YAAY;IACxBC,iBAAiB,EAAE,sBAAsB;IACzCC,YAAY,EAAE,iBAAiB;IAC/BC,iBAAiB,EAAE,oBAAoB;IACvCC,YAAY,EAAE,eAAe;IAC7BC,kBAAkB,EAAE,qBAAqB;IACzCC,aAAa,EAAE,gBAAgB;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAElB;IACAC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR;AACF,CAAC;;AAED;AACApE,IAAI,CACDsE,GAAG,CAACrE,gBAAgB,CAAC,CACrBsE,IAAI,CAAC;EACJC,SAAS,EAAE;IACTC,EAAE,EAAEvE,kBAAkB;IACtBwE,EAAE,EAAEL;EACN,CAAC;EACDM,GAAG,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;EAAE;EAC/CC,WAAW,EAAE,IAAI;EAEjBC,aAAa,EAAE;IACbC,WAAW,EAAE,KAAK,CAAE;EACtB,CAAC;EAEDC,KAAK,EAAE;IACLC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AAEJ,eAAelF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}