# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_PATH=./database/app.db

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here_change_in_production
JWT_EXPIRES_IN=24h

# Email Configuration (NodeMailer)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=Display Management System <<EMAIL>>

# Stripe Configuration (Sandbox)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Application Settings
DEFAULT_DISPLAY_PRICE=50
DEFAULT_DISPLAY_DURATION=300
MAX_DISPLAYS=5
OTP_EXPIRY_MINUTES=5
OWNER_PASSWORD=admin123

# SMS Configuration (Optional - for future use)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
