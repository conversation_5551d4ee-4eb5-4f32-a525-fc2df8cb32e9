{"ast": null, "code": "import axios from 'axios';\n\n// إعداد الـ API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// إضافة interceptor للطلبات لإضافة token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// إضافة interceptor للاستجابات لمعالجة الأخطاء\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response, _error$response2;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // إزالة token منتهي الصلاحية\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userInfo');\n    window.location.href = '/';\n  }\n  return Promise.reject(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n});\n\n// خدمات المصادقة\nexport const authService = {\n  // إرسال OTP\n  sendOTP: (phoneNumber, email, language = 'ar') => {\n    return api.post('/auth/send-otp', {\n      phoneNumber,\n      email,\n      language\n    });\n  },\n  // التحقق من OTP\n  verifyOTP: (phoneNumber, otpCode) => {\n    return api.post('/auth/verify-otp', {\n      phoneNumber,\n      otpCode\n    });\n  },\n  // تسجيل دخول المالك\n  ownerLogin: password => {\n    return api.post('/auth/owner-login', {\n      password\n    });\n  },\n  // التحقق من صحة الرمز المميز\n  verifyToken: () => {\n    return api.get('/auth/verify-token');\n  }\n};\n\n// خدمات الشاشات\nexport const displayService = {\n  // الحصول على جميع الشاشات\n  getAllDisplays: () => {\n    return api.get('/displays');\n  },\n  // الحصول على شاشة محددة\n  getDisplay: displayId => {\n    return api.get(`/displays/${displayId}`);\n  },\n  // حجز شاشة\n  reserveDisplay: (displayId, customerName) => {\n    return api.post(`/displays/${displayId}/reserve`, {\n      customerName\n    });\n  },\n  // إلغاء حجز الشاشة\n  cancelReservation: displayId => {\n    return api.post(`/displays/${displayId}/cancel-reservation`);\n  },\n  // تحديث إعدادات الشاشة (للمالك)\n  updateDisplay: (displayId, data) => {\n    return api.put(`/displays/${displayId}`, data);\n  }\n};\n\n// خدمات المعاملات\nexport const transactionService = {\n  // إنشاء معاملة جديدة\n  createTransaction: (displayId, customerName, amount, duration) => {\n    return api.post('/transactions', {\n      displayId,\n      customerName,\n      amount,\n      duration\n    });\n  },\n  // تأكيد المعاملة بعد الدفع\n  confirmTransaction: (transactionId, paymentIntentId) => {\n    return api.post(`/transactions/${transactionId}/confirm`, {\n      paymentIntentId\n    });\n  },\n  // الحصول على معاملات العميل\n  getMyTransactions: () => {\n    return api.get('/transactions/my-transactions');\n  },\n  // الحصول على جميع المعاملات (للمالك)\n  getAllTransactions: (params = {}) => {\n    return api.get('/transactions/all', {\n      params\n    });\n  }\n};\n\n// خدمات الدفع\nexport const paymentService = {\n  // الحصول على إعدادات الدفع\n  getPaymentConfig: () => {\n    return api.get('/payment/config');\n  },\n  // إنشاء Payment Intent\n  createPaymentIntent: (amount, transactionId, currency = 'sar') => {\n    return api.post('/payment/create-payment-intent', {\n      amount,\n      transactionId,\n      currency\n    });\n  },\n  // التحقق من حالة الدفع\n  getPaymentStatus: paymentIntentId => {\n    return api.get(`/payment/payment-status/${paymentIntentId}`);\n  },\n  // محاكاة دفع NFC\n  simulateNFCPayment: (transactionId, cardNumber = '****************') => {\n    return api.post('/payment/simulate-nfc-payment', {\n      transactionId,\n      cardNumber\n    });\n  }\n};\n\n// خدمات المالك\nexport const ownerService = {\n  // الحصول على إحصائيات لوحة التحكم\n  getDashboardStats: () => {\n    return api.get('/owner/dashboard-stats');\n  },\n  // الحصول على الإعدادات\n  getSettings: () => {\n    return api.get('/owner/settings');\n  },\n  // تحديث الإعدادات\n  updateSettings: settings => {\n    return api.put('/owner/settings', {\n      settings\n    });\n  },\n  // الحصول على تقرير الإيرادات\n  getRevenueReport: (params = {}) => {\n    return api.get('/owner/revenue-report', {\n      params\n    });\n  },\n  // الحصول على تقرير استخدام الشاشات\n  getDisplayUsageReport: (params = {}) => {\n    return api.get('/owner/display-usage-report', {\n      params\n    });\n  },\n  // إنهاء معاملة يدوياً\n  endTransaction: (transactionId, reason) => {\n    return api.post(`/owner/end-transaction/${transactionId}`, {\n      reason\n    });\n  }\n};\n\n// خدمات عامة\nexport const generalService = {\n  // اختبار الاتصال\n  ping: () => {\n    return api.get('/ping');\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response", "_error$response2", "status", "removeItem", "window", "location", "href", "message", "authService", "sendOTP", "phoneNumber", "email", "language", "post", "verifyOTP", "otpCode", "owner<PERSON><PERSON><PERSON>", "password", "verifyToken", "get", "displayService", "getAllDisplays", "getDisplay", "displayId", "reserveDisplay", "customerName", "cancelReservation", "updateDisplay", "put", "transactionService", "createTransaction", "amount", "duration", "confirmTransaction", "transactionId", "paymentIntentId", "getMyTransactions", "getAllTransactions", "params", "paymentService", "getPaymentConfig", "createPaymentIntent", "currency", "getPaymentStatus", "simulateNFCPayment", "cardNumber", "ownerService", "getDashboardStats", "getSettings", "updateSettings", "settings", "getRevenueReport", "getDisplayUsageReport", "endTransaction", "reason", "generalService", "ping"], "sources": ["D:/برمجة/tste 1/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// إعداد الـ API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// إضافة interceptor للطلبات لإضافة token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// إضافة interceptor للاستجابات لمعالجة الأخطاء\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // إزالة token منتهي الصلاحية\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userInfo');\n      window.location.href = '/';\n    }\n    return Promise.reject(error.response?.data || error.message);\n  }\n);\n\n// خدمات المصادقة\nexport const authService = {\n  // إرسال OTP\n  sendOTP: (phoneNumber, email, language = 'ar') => {\n    return api.post('/auth/send-otp', { phoneNumber, email, language });\n  },\n\n  // التحقق من OTP\n  verifyOTP: (phoneNumber, otpCode) => {\n    return api.post('/auth/verify-otp', { phoneNumber, otpCode });\n  },\n\n  // تسجيل دخول المالك\n  ownerLogin: (password) => {\n    return api.post('/auth/owner-login', { password });\n  },\n\n  // التحقق من صحة الرمز المميز\n  verifyToken: () => {\n    return api.get('/auth/verify-token');\n  },\n};\n\n// خدمات الشاشات\nexport const displayService = {\n  // الحصول على جميع الشاشات\n  getAllDisplays: () => {\n    return api.get('/displays');\n  },\n\n  // الحصول على شاشة محددة\n  getDisplay: (displayId) => {\n    return api.get(`/displays/${displayId}`);\n  },\n\n  // حجز شاشة\n  reserveDisplay: (displayId, customerName) => {\n    return api.post(`/displays/${displayId}/reserve`, { customerName });\n  },\n\n  // إلغاء حجز الشاشة\n  cancelReservation: (displayId) => {\n    return api.post(`/displays/${displayId}/cancel-reservation`);\n  },\n\n  // تحديث إعدادات الشاشة (للمالك)\n  updateDisplay: (displayId, data) => {\n    return api.put(`/displays/${displayId}`, data);\n  },\n};\n\n// خدمات المعاملات\nexport const transactionService = {\n  // إنشاء معاملة جديدة\n  createTransaction: (displayId, customerName, amount, duration) => {\n    return api.post('/transactions', { displayId, customerName, amount, duration });\n  },\n\n  // تأكيد المعاملة بعد الدفع\n  confirmTransaction: (transactionId, paymentIntentId) => {\n    return api.post(`/transactions/${transactionId}/confirm`, { paymentIntentId });\n  },\n\n  // الحصول على معاملات العميل\n  getMyTransactions: () => {\n    return api.get('/transactions/my-transactions');\n  },\n\n  // الحصول على جميع المعاملات (للمالك)\n  getAllTransactions: (params = {}) => {\n    return api.get('/transactions/all', { params });\n  },\n};\n\n// خدمات الدفع\nexport const paymentService = {\n  // الحصول على إعدادات الدفع\n  getPaymentConfig: () => {\n    return api.get('/payment/config');\n  },\n\n  // إنشاء Payment Intent\n  createPaymentIntent: (amount, transactionId, currency = 'sar') => {\n    return api.post('/payment/create-payment-intent', { amount, transactionId, currency });\n  },\n\n  // التحقق من حالة الدفع\n  getPaymentStatus: (paymentIntentId) => {\n    return api.get(`/payment/payment-status/${paymentIntentId}`);\n  },\n\n  // محاكاة دفع NFC\n  simulateNFCPayment: (transactionId, cardNumber = '****************') => {\n    return api.post('/payment/simulate-nfc-payment', { transactionId, cardNumber });\n  },\n};\n\n// خدمات المالك\nexport const ownerService = {\n  // الحصول على إحصائيات لوحة التحكم\n  getDashboardStats: () => {\n    return api.get('/owner/dashboard-stats');\n  },\n\n  // الحصول على الإعدادات\n  getSettings: () => {\n    return api.get('/owner/settings');\n  },\n\n  // تحديث الإعدادات\n  updateSettings: (settings) => {\n    return api.put('/owner/settings', { settings });\n  },\n\n  // الحصول على تقرير الإيرادات\n  getRevenueReport: (params = {}) => {\n    return api.get('/owner/revenue-report', { params });\n  },\n\n  // الحصول على تقرير استخدام الشاشات\n  getDisplayUsageReport: (params = {}) => {\n    return api.get('/owner/display-usage-report', { params });\n  },\n\n  // إنهاء معاملة يدوياً\n  endTransaction: (transactionId, reason) => {\n    return api.post(`/owner/end-transaction/${transactionId}`, { reason });\n  },\n};\n\n// خدمات عامة\nexport const generalService = {\n  // اختبار الاتصال\n  ping: () => {\n    return api.get('/ping');\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,gBAAA;EACT,IAAI,EAAAD,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;IAClC;IACAV,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;IACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;IACnCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;EAC5B;EACA,OAAOV,OAAO,CAACC,MAAM,CAAC,EAAAI,gBAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBF,IAAI,KAAIJ,KAAK,CAACY,OAAO,CAAC;AAC9D,CACF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,OAAO,EAAEA,CAACC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAChD,OAAO9B,GAAG,CAAC+B,IAAI,CAAC,gBAAgB,EAAE;MAAEH,WAAW;MAAEC,KAAK;MAAEC;IAAS,CAAC,CAAC;EACrE,CAAC;EAED;EACAE,SAAS,EAAEA,CAACJ,WAAW,EAAEK,OAAO,KAAK;IACnC,OAAOjC,GAAG,CAAC+B,IAAI,CAAC,kBAAkB,EAAE;MAAEH,WAAW;MAAEK;IAAQ,CAAC,CAAC;EAC/D,CAAC;EAED;EACAC,UAAU,EAAGC,QAAQ,IAAK;IACxB,OAAOnC,GAAG,CAAC+B,IAAI,CAAC,mBAAmB,EAAE;MAAEI;IAAS,CAAC,CAAC;EACpD,CAAC;EAED;EACAC,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOpC,GAAG,CAACqC,GAAG,CAAC,oBAAoB,CAAC;EACtC;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,cAAc,EAAEA,CAAA,KAAM;IACpB,OAAOvC,GAAG,CAACqC,GAAG,CAAC,WAAW,CAAC;EAC7B,CAAC;EAED;EACAG,UAAU,EAAGC,SAAS,IAAK;IACzB,OAAOzC,GAAG,CAACqC,GAAG,CAAC,aAAaI,SAAS,EAAE,CAAC;EAC1C,CAAC;EAED;EACAC,cAAc,EAAEA,CAACD,SAAS,EAAEE,YAAY,KAAK;IAC3C,OAAO3C,GAAG,CAAC+B,IAAI,CAAC,aAAaU,SAAS,UAAU,EAAE;MAAEE;IAAa,CAAC,CAAC;EACrE,CAAC;EAED;EACAC,iBAAiB,EAAGH,SAAS,IAAK;IAChC,OAAOzC,GAAG,CAAC+B,IAAI,CAAC,aAAaU,SAAS,qBAAqB,CAAC;EAC9D,CAAC;EAED;EACAI,aAAa,EAAEA,CAACJ,SAAS,EAAExB,IAAI,KAAK;IAClC,OAAOjB,GAAG,CAAC8C,GAAG,CAAC,aAAaL,SAAS,EAAE,EAAExB,IAAI,CAAC;EAChD;AACF,CAAC;;AAED;AACA,OAAO,MAAM8B,kBAAkB,GAAG;EAChC;EACAC,iBAAiB,EAAEA,CAACP,SAAS,EAAEE,YAAY,EAAEM,MAAM,EAAEC,QAAQ,KAAK;IAChE,OAAOlD,GAAG,CAAC+B,IAAI,CAAC,eAAe,EAAE;MAAEU,SAAS;MAAEE,YAAY;MAAEM,MAAM;MAAEC;IAAS,CAAC,CAAC;EACjF,CAAC;EAED;EACAC,kBAAkB,EAAEA,CAACC,aAAa,EAAEC,eAAe,KAAK;IACtD,OAAOrD,GAAG,CAAC+B,IAAI,CAAC,iBAAiBqB,aAAa,UAAU,EAAE;MAAEC;IAAgB,CAAC,CAAC;EAChF,CAAC;EAED;EACAC,iBAAiB,EAAEA,CAAA,KAAM;IACvB,OAAOtD,GAAG,CAACqC,GAAG,CAAC,+BAA+B,CAAC;EACjD,CAAC;EAED;EACAkB,kBAAkB,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IACnC,OAAOxD,GAAG,CAACqC,GAAG,CAAC,mBAAmB,EAAE;MAAEmB;IAAO,CAAC,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,gBAAgB,EAAEA,CAAA,KAAM;IACtB,OAAO1D,GAAG,CAACqC,GAAG,CAAC,iBAAiB,CAAC;EACnC,CAAC;EAED;EACAsB,mBAAmB,EAAEA,CAACV,MAAM,EAAEG,aAAa,EAAEQ,QAAQ,GAAG,KAAK,KAAK;IAChE,OAAO5D,GAAG,CAAC+B,IAAI,CAAC,gCAAgC,EAAE;MAAEkB,MAAM;MAAEG,aAAa;MAAEQ;IAAS,CAAC,CAAC;EACxF,CAAC;EAED;EACAC,gBAAgB,EAAGR,eAAe,IAAK;IACrC,OAAOrD,GAAG,CAACqC,GAAG,CAAC,2BAA2BgB,eAAe,EAAE,CAAC;EAC9D,CAAC;EAED;EACAS,kBAAkB,EAAEA,CAACV,aAAa,EAAEW,UAAU,GAAG,kBAAkB,KAAK;IACtE,OAAO/D,GAAG,CAAC+B,IAAI,CAAC,+BAA+B,EAAE;MAAEqB,aAAa;MAAEW;IAAW,CAAC,CAAC;EACjF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1B;EACAC,iBAAiB,EAAEA,CAAA,KAAM;IACvB,OAAOjE,GAAG,CAACqC,GAAG,CAAC,wBAAwB,CAAC;EAC1C,CAAC;EAED;EACA6B,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOlE,GAAG,CAACqC,GAAG,CAAC,iBAAiB,CAAC;EACnC,CAAC;EAED;EACA8B,cAAc,EAAGC,QAAQ,IAAK;IAC5B,OAAOpE,GAAG,CAAC8C,GAAG,CAAC,iBAAiB,EAAE;MAAEsB;IAAS,CAAC,CAAC;EACjD,CAAC;EAED;EACAC,gBAAgB,EAAEA,CAACb,MAAM,GAAG,CAAC,CAAC,KAAK;IACjC,OAAOxD,GAAG,CAACqC,GAAG,CAAC,uBAAuB,EAAE;MAAEmB;IAAO,CAAC,CAAC;EACrD,CAAC;EAED;EACAc,qBAAqB,EAAEA,CAACd,MAAM,GAAG,CAAC,CAAC,KAAK;IACtC,OAAOxD,GAAG,CAACqC,GAAG,CAAC,6BAA6B,EAAE;MAAEmB;IAAO,CAAC,CAAC;EAC3D,CAAC;EAED;EACAe,cAAc,EAAEA,CAACnB,aAAa,EAAEoB,MAAM,KAAK;IACzC,OAAOxE,GAAG,CAAC+B,IAAI,CAAC,0BAA0BqB,aAAa,EAAE,EAAE;MAAEoB;IAAO,CAAC,CAAC;EACxE;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,IAAI,EAAEA,CAAA,KAAM;IACV,OAAO1E,GAAG,CAACqC,GAAG,CAAC,OAAO,CAAC;EACzB;AACF,CAAC;AAED,eAAerC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}