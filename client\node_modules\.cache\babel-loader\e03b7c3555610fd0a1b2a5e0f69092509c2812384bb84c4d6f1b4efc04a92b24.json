{"ast": null, "code": "import React,{useState}from'react';import{useTranslation}from'react-i18next';import{useNavigate}from'react-router-dom';import styled from'styled-components';import{useOwnerAuth}from'../hooks/useAuth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Container=styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Card=styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 450px;\n  width: 100%;\n`;const Header=styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;const Logo=styled.div`\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  margin: 0 auto 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  color: white;\n`;const Title=styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;const Subtitle=styled.p`\n  font-size: 1rem;\n  color: #666;\n`;const Form=styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;const InputGroup=styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;const Label=styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1rem;\n`;const Input=styled.input`\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n\n  &:invalid {\n    border-color: #dc3545;\n  }\n`;const Button=styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props=>props.disabled?0.6:1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;const BackButton=styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: 100%;\n  margin-top: 10px;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;const InfoBox=styled.div`\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n  text-align: center;\n`;const OwnerLoginPage=()=>{const{t,i18n}=useTranslation();const navigate=useNavigate();const{loading,error,ownerLogin}=useOwnerAuth();const[password,setPassword]=useState('');const isRTL=i18n.language==='ar';const handleSubmit=async e=>{e.preventDefault();if(!password.trim()){return;}const response=await ownerLogin(password);if(response){// الانتقال للوحة تحكم المالك\nnavigate('/owner-dashboard');}};const handleBack=()=>{navigate('/');};return/*#__PURE__*/_jsx(Container,{isRTL:isRTL,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Logo,{children:\"\\uD83D\\uDC64\"}),/*#__PURE__*/_jsx(Title,{children:isRTL?'دخول المالك':'Owner Login'}),/*#__PURE__*/_jsx(Subtitle,{children:isRTL?'لوحة تحكم النظام':'System Control Panel'})]}),/*#__PURE__*/_jsx(InfoBox,{children:isRTL?'كلمة المرور الافتراضية: admin123':'Default password: admin123'}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(Label,{children:isRTL?'كلمة المرور':'Password'}),/*#__PURE__*/_jsx(Input,{type:\"password\",value:password,onChange:e=>setPassword(e.target.value),placeholder:isRTL?'أدخل كلمة المرور':'Enter password',required:true})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading||!password.trim(),children:loading?isRTL?'جاري التحقق...':'Verifying...':isRTL?'دخول':'Login'})]}),/*#__PURE__*/_jsx(BackButton,{onClick:handleBack,children:t('back')})]})});};export default OwnerLoginPage;", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useNavigate", "styled", "useOwnerAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Container", "div", "props", "isRTL", "Card", "Header", "Logo", "Title", "h1", "Subtitle", "p", "Form", "form", "InputGroup", "Label", "label", "Input", "input", "<PERSON><PERSON>", "button", "disabled", "BackButton", "ErrorMessage", "InfoBox", "OwnerLoginPage", "t", "i18n", "navigate", "loading", "error", "owner<PERSON><PERSON><PERSON>", "password", "setPassword", "language", "handleSubmit", "e", "preventDefault", "trim", "response", "handleBack", "children", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "onClick"], "sources": ["D:/برمجة/tste 1/client/src/pages/OwnerLoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useOwnerAuth } from '../hooks/useAuth';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 450px;\n  width: 100%;\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst Logo = styled.div`\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  margin: 0 auto 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  color: white;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1rem;\n  color: #666;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1rem;\n`;\n\nconst Input = styled.input`\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n\n  &:invalid {\n    border-color: #dc3545;\n  }\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: 100%;\n  margin-top: 10px;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst InfoBox = styled.div`\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n  text-align: center;\n`;\n\nconst OwnerLoginPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { loading, error, ownerLogin } = useOwnerAuth();\n  \n  const [password, setPassword] = useState('');\n\n  const isRTL = i18n.language === 'ar';\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!password.trim()) {\n      return;\n    }\n\n    const response = await ownerLogin(password);\n    \n    if (response) {\n      // الانتقال للوحة تحكم المالك\n      navigate('/owner-dashboard');\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/');\n  };\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Header>\n          <Logo>👤</Logo>\n          <Title>\n            {isRTL ? 'دخول المالك' : 'Owner Login'}\n          </Title>\n          <Subtitle>\n            {isRTL ? 'لوحة تحكم النظام' : 'System Control Panel'}\n          </Subtitle>\n        </Header>\n\n        <InfoBox>\n          {isRTL ? \n            'كلمة المرور الافتراضية: admin123' :\n            'Default password: admin123'\n          }\n        </InfoBox>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <Label>\n              {isRTL ? 'كلمة المرور' : 'Password'}\n            </Label>\n            <Input\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder={isRTL ? 'أدخل كلمة المرور' : 'Enter password'}\n              required\n            />\n          </InputGroup>\n\n          <Button type=\"submit\" disabled={loading || !password.trim()}>\n            {loading ? \n              (isRTL ? 'جاري التحقق...' : 'Verifying...') : \n              (isRTL ? 'دخول' : 'Login')\n            }\n          </Button>\n        </Form>\n\n        <BackButton onClick={handleBack}>\n          {t('back')}\n        </BackButton>\n      </Card>\n    </Container>\n  );\n};\n\nexport default OwnerLoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,YAAY,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,SAAS,CAAGN,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGV,MAAM,CAACO,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,MAAM,CAAGX,MAAM,CAACO,GAAG;AACzB;AACA;AACA,CAAC,CAED,KAAM,CAAAK,IAAI,CAAGZ,MAAM,CAACO,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAM,KAAK,CAAGb,MAAM,CAACc,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGf,MAAM,CAACgB,CAAC;AACzB;AACA;AACA,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGjB,MAAM,CAACkB,IAAI;AACxB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGnB,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,KAAK,CAAGpB,MAAM,CAACqB,KAAK;AAC1B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,KAAK,CAAGtB,MAAM,CAACuB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGxB,MAAM,CAACyB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAajB,KAAK,EAAIA,KAAK,CAACkB,QAAQ,CAAG,GAAG,CAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG3B,MAAM,CAACyB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG5B,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAsB,OAAO,CAAG7B,MAAM,CAACO,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAuB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGlC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAmC,QAAQ,CAAGlC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmC,OAAO,CAAEC,KAAK,CAAEC,UAAW,CAAC,CAAGnC,YAAY,CAAC,CAAC,CAErD,KAAM,CAACoC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAE5C,KAAM,CAAAY,KAAK,CAAGuB,IAAI,CAACO,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAE,CACpB,OACF,CAEA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAR,UAAU,CAACC,QAAQ,CAAC,CAE3C,GAAIO,QAAQ,CAAE,CACZ;AACAX,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAY,UAAU,CAAGA,CAAA,GAAM,CACvBZ,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,mBACE9B,IAAA,CAACG,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAAqC,QAAA,cACtBzC,KAAA,CAACK,IAAI,EAAAoC,QAAA,eACHzC,KAAA,CAACM,MAAM,EAAAmC,QAAA,eACL3C,IAAA,CAACS,IAAI,EAAAkC,QAAA,CAAC,cAAE,CAAM,CAAC,cACf3C,IAAA,CAACU,KAAK,EAAAiC,QAAA,CACHrC,KAAK,CAAG,aAAa,CAAG,aAAa,CACjC,CAAC,cACRN,IAAA,CAACY,QAAQ,EAAA+B,QAAA,CACNrC,KAAK,CAAG,kBAAkB,CAAG,sBAAsB,CAC5C,CAAC,EACL,CAAC,cAETN,IAAA,CAAC0B,OAAO,EAAAiB,QAAA,CACLrC,KAAK,CACJ,kCAAkC,CAClC,4BAA4B,CAEvB,CAAC,CAET0B,KAAK,eAAIhC,IAAA,CAACyB,YAAY,EAAAkB,QAAA,CAAEX,KAAK,CAAe,CAAC,cAE9C9B,KAAA,CAACY,IAAI,EAAC8B,QAAQ,CAAEP,YAAa,CAAAM,QAAA,eAC3BzC,KAAA,CAACc,UAAU,EAAA2B,QAAA,eACT3C,IAAA,CAACiB,KAAK,EAAA0B,QAAA,CACHrC,KAAK,CAAG,aAAa,CAAG,UAAU,CAC9B,CAAC,cACRN,IAAA,CAACmB,KAAK,EACJ0B,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEZ,QAAS,CAChBa,QAAQ,CAAGT,CAAC,EAAKH,WAAW,CAACG,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE,CAC7CG,WAAW,CAAE3C,KAAK,CAAG,kBAAkB,CAAG,gBAAiB,CAC3D4C,QAAQ,MACT,CAAC,EACQ,CAAC,cAEblD,IAAA,CAACqB,MAAM,EAACwB,IAAI,CAAC,QAAQ,CAACtB,QAAQ,CAAEQ,OAAO,EAAI,CAACG,QAAQ,CAACM,IAAI,CAAC,CAAE,CAAAG,QAAA,CACzDZ,OAAO,CACLzB,KAAK,CAAG,gBAAgB,CAAG,cAAc,CACzCA,KAAK,CAAG,MAAM,CAAG,OAAQ,CAEtB,CAAC,EACL,CAAC,cAEPN,IAAA,CAACwB,UAAU,EAAC2B,OAAO,CAAET,UAAW,CAAAC,QAAA,CAC7Bf,CAAC,CAAC,MAAM,CAAC,CACA,CAAC,EACT,CAAC,CACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}