{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\professional-display-system\\\\src\\\\components\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'medium',\n  color = '#667eea',\n  text,\n  fullScreen = false\n}) => {\n  const getSizeClass = () => {\n    switch (size) {\n      case 'small':\n        return 'spinner-small';\n      case 'large':\n        return 'spinner-large';\n      default:\n        return 'spinner-medium';\n    }\n  };\n  const spinner = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `loading-spinner ${getSizeClass()}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"spinner-circle\",\n      style: {\n        borderTopColor: color\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"spinner-text\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 16\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n  if (fullScreen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-overlay\",\n      children: spinner\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n  return spinner;\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "color", "text", "fullScreen", "getSizeClass", "spinner", "className", "children", "style", "borderTopColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/professional-display-system/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'small' | 'medium' | 'large';\n  color?: string;\n  text?: string;\n  fullScreen?: boolean;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'medium', \n  color = '#667eea',\n  text,\n  fullScreen = false \n}) => {\n  const getSizeClass = () => {\n    switch (size) {\n      case 'small': return 'spinner-small';\n      case 'large': return 'spinner-large';\n      default: return 'spinner-medium';\n    }\n  };\n\n  const spinner = (\n    <div className={`loading-spinner ${getSizeClass()}`}>\n      <div \n        className=\"spinner-circle\"\n        style={{ borderTopColor: color }}\n      ></div>\n      {text && <p className=\"spinner-text\">{text}</p>}\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"loading-overlay\">\n        {spinner}\n      </div>\n    );\n  }\n\n  return spinner;\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS1B,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,IAAI,GAAG,QAAQ;EACfC,KAAK,GAAG,SAAS;EACjBC,IAAI;EACJC,UAAU,GAAG;AACf,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQJ,IAAI;MACV,KAAK,OAAO;QAAE,OAAO,eAAe;MACpC,KAAK,OAAO;QAAE,OAAO,eAAe;MACpC;QAAS,OAAO,gBAAgB;IAClC;EACF,CAAC;EAED,MAAMK,OAAO,gBACXP,OAAA;IAAKQ,SAAS,EAAE,mBAAmBF,YAAY,CAAC,CAAC,EAAG;IAAAG,QAAA,gBAClDT,OAAA;MACEQ,SAAS,EAAC,gBAAgB;MAC1BE,KAAK,EAAE;QAAEC,cAAc,EAAER;MAAM;IAAE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,EACNX,IAAI,iBAAIJ,OAAA;MAAGQ,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAEL;IAAI;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CACN;EAED,IAAIV,UAAU,EAAE;IACd,oBACEL,OAAA;MAAKQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BF;IAAO;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,OAAOR,OAAO;AAChB,CAAC;AAACS,EAAA,GAjCIf,cAA6C;AAmCnD,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}