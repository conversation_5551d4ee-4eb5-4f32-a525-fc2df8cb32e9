{"ast": null, "code": "import React,{useState}from'react';import{useTranslation}from'react-i18next';import{useNavigate,useLocation}from'react-router-dom';import styled from'styled-components';import{useOTP,useAuth}from'../hooks/useAuth';import{validateSaudiPhoneNumber,validateEmail,formatSaudiPhoneNumber}from'../utils/helpers';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Container=styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Card=styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n`;const Header=styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;const Title=styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;const DisplayInfo=styled.div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  text-align: center;\n`;const Form=styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;const InputGroup=styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;const Label=styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1rem;\n`;const Input=styled.input`\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  direction: ${props=>props.type==='tel'?'ltr':'inherit'};\n  text-align: ${props=>props.type==='tel'?'left':'inherit'};\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n\n  &:invalid {\n    border-color: #dc3545;\n  }\n`;const Button=styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props=>props.disabled?0.6:1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;const BackButton=styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;const SuccessMessage=styled.div`\n  background: #28a745;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;const OTPInfo=styled.div`\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n`;const LoginPage=()=>{var _location$state;const{t,i18n}=useTranslation();const navigate=useNavigate();const location=useLocation();const{login}=useAuth();const{loading,error,otpSent,sendOTP,verifyOTP,resetOTP}=useOTP();const[step,setStep]=useState('phone');// 'phone' or 'otp'\nconst[formData,setFormData]=useState({phoneNumber:'',email:'',otpCode:''});const[validationErrors,setValidationErrors]=useState({});const isRTL=i18n.language==='ar';const selectedDisplay=(_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.selectedDisplay;const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>({...prev,[name]:value}));// إزالة رسالة الخطأ عند التعديل\nif(validationErrors[name]){setValidationErrors(prev=>({...prev,[name]:''}));}};const validateForm=()=>{const errors={};if(step==='phone'){if(!formData.phoneNumber){errors.phoneNumber=t('enterPhoneNumber');}else if(!validateSaudiPhoneNumber(formData.phoneNumber)){errors.phoneNumber=t('invalidPhoneNumber');}if(!formData.email){errors.email=t('enterEmail');}else if(!validateEmail(formData.email)){errors.email=t('invalidEmail');}}else if(step==='otp'){if(!formData.otpCode){errors.otpCode=t('enterOTP');}else if(formData.otpCode.length!==6){errors.otpCode=t('invalidOTP');}}setValidationErrors(errors);return Object.keys(errors).length===0;};const handleSendOTP=async e=>{e.preventDefault();if(!validateForm())return;const formattedPhone=formatSaudiPhoneNumber(formData.phoneNumber);const response=await sendOTP(formattedPhone,formData.email,i18n.language);if(response){setStep('otp');}};const handleVerifyOTP=async e=>{e.preventDefault();if(!validateForm())return;const formattedPhone=formatSaudiPhoneNumber(formData.phoneNumber);const response=await verifyOTP(formattedPhone,formData.otpCode);if(response){// تسجيل الدخول\nlogin(response.data.customer,response.data.token);// الانتقال لصفحة إدخال الاسم\nnavigate('/enter-name',{state:{selectedDisplay,customer:response.data.customer}});}};const handleBack=()=>{if(step==='otp'){setStep('phone');resetOTP();}else{navigate('/select-display');}};const handleResendOTP=async()=>{const formattedPhone=formatSaudiPhoneNumber(formData.phoneNumber);await sendOTP(formattedPhone,formData.email,i18n.language);};return/*#__PURE__*/_jsx(Container,{isRTL:isRTL,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Title,{children:step==='phone'?isRTL?'تسجيل الدخول':'Login':isRTL?'رمز التحقق':'Verification Code'}),selectedDisplay&&/*#__PURE__*/_jsxs(DisplayInfo,{children:[/*#__PURE__*/_jsx(\"strong\",{children:t('displayNumber',{number:selectedDisplay.displayNumber})}),/*#__PURE__*/_jsx(\"br\",{}),selectedDisplay.name]})]}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),otpSent&&step==='otp'&&/*#__PURE__*/_jsx(SuccessMessage,{children:t('otpSent')}),step==='phone'?/*#__PURE__*/_jsxs(Form,{onSubmit:handleSendOTP,children:[/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(Label,{children:t('phoneNumber')}),/*#__PURE__*/_jsx(Input,{type:\"tel\",name:\"phoneNumber\",value:formData.phoneNumber,onChange:handleInputChange,placeholder:t('enterPhoneNumber'),required:true}),validationErrors.phoneNumber&&/*#__PURE__*/_jsx(ErrorMessage,{children:validationErrors.phoneNumber})]}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(Label,{children:t('email')}),/*#__PURE__*/_jsx(Input,{type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,placeholder:t('enterEmail'),required:true}),validationErrors.email&&/*#__PURE__*/_jsx(ErrorMessage,{children:validationErrors.email})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading,children:loading?t('processing'):t('sendOTP')})]}):/*#__PURE__*/_jsxs(Form,{onSubmit:handleVerifyOTP,children:[/*#__PURE__*/_jsx(OTPInfo,{children:isRTL?`تم إرسال رمز التحقق إلى: ${formData.email}`:`Verification code sent to: ${formData.email}`}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(Label,{children:t('otpCode')}),/*#__PURE__*/_jsx(Input,{type:\"text\",name:\"otpCode\",value:formData.otpCode,onChange:handleInputChange,placeholder:t('enterOTP'),maxLength:\"6\",required:true}),validationErrors.otpCode&&/*#__PURE__*/_jsx(ErrorMessage,{children:validationErrors.otpCode})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading,children:loading?t('processing'):t('verifyOTP')}),/*#__PURE__*/_jsx(Button,{type:\"button\",onClick:handleResendOTP,disabled:loading,style:{background:'#6c757d'},children:isRTL?'إعادة إرسال الرمز':'Resend Code'})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'20px',textAlign:'center'},children:/*#__PURE__*/_jsx(BackButton,{onClick:handleBack,children:t('back')})})]})});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useNavigate", "useLocation", "styled", "useOTP", "useAuth", "validateSaudiPhoneNumber", "validateEmail", "formatSaudiPhoneNumber", "jsx", "_jsx", "jsxs", "_jsxs", "Container", "div", "props", "isRTL", "Card", "Header", "Title", "h1", "DisplayInfo", "Form", "form", "InputGroup", "Label", "label", "Input", "input", "type", "<PERSON><PERSON>", "button", "disabled", "BackButton", "ErrorMessage", "SuccessMessage", "OTPInfo", "LoginPage", "_location$state", "t", "i18n", "navigate", "location", "login", "loading", "error", "otpSent", "sendOTP", "verifyOTP", "resetOTP", "step", "setStep", "formData", "setFormData", "phoneNumber", "email", "otpCode", "validationErrors", "setValidationErrors", "language", "selectedDisplay", "state", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "errors", "length", "Object", "keys", "handleSendOTP", "preventDefault", "formattedPhone", "response", "handleVerifyOTP", "data", "customer", "token", "handleBack", "handleResendOTP", "children", "number", "displayNumber", "onSubmit", "onChange", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "onClick", "style", "background", "marginTop", "textAlign"], "sources": ["D:/برمجة/tste 1/client/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useOTP, useAuth } from '../hooks/useAuth';\nimport { validateSaudiPhoneNumber, validateEmail, formatSaudiPhoneNumber } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst DisplayInfo = styled.div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  text-align: center;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1rem;\n`;\n\nconst Input = styled.input`\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  direction: ${props => props.type === 'tel' ? 'ltr' : 'inherit'};\n  text-align: ${props => props.type === 'tel' ? 'left' : 'inherit'};\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n\n  &:invalid {\n    border-color: #dc3545;\n  }\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst SuccessMessage = styled.div`\n  background: #28a745;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst OTPInfo = styled.div`\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n`;\n\nconst LoginPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login } = useAuth();\n  const { loading, error, otpSent, sendOTP, verifyOTP, resetOTP } = useOTP();\n\n  const [step, setStep] = useState('phone'); // 'phone' or 'otp'\n  const [formData, setFormData] = useState({\n    phoneNumber: '',\n    email: '',\n    otpCode: ''\n  });\n  const [validationErrors, setValidationErrors] = useState({});\n\n  const isRTL = i18n.language === 'ar';\n  const selectedDisplay = location.state?.selectedDisplay;\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // إزالة رسالة الخطأ عند التعديل\n    if (validationErrors[name]) {\n      setValidationErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (step === 'phone') {\n      if (!formData.phoneNumber) {\n        errors.phoneNumber = t('enterPhoneNumber');\n      } else if (!validateSaudiPhoneNumber(formData.phoneNumber)) {\n        errors.phoneNumber = t('invalidPhoneNumber');\n      }\n\n      if (!formData.email) {\n        errors.email = t('enterEmail');\n      } else if (!validateEmail(formData.email)) {\n        errors.email = t('invalidEmail');\n      }\n    } else if (step === 'otp') {\n      if (!formData.otpCode) {\n        errors.otpCode = t('enterOTP');\n      } else if (formData.otpCode.length !== 6) {\n        errors.otpCode = t('invalidOTP');\n      }\n    }\n\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSendOTP = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    const response = await sendOTP(formattedPhone, formData.email, i18n.language);\n    \n    if (response) {\n      setStep('otp');\n    }\n  };\n\n  const handleVerifyOTP = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    const response = await verifyOTP(formattedPhone, formData.otpCode);\n    \n    if (response) {\n      // تسجيل الدخول\n      login(response.data.customer, response.data.token);\n      \n      // الانتقال لصفحة إدخال الاسم\n      navigate('/enter-name', {\n        state: {\n          selectedDisplay,\n          customer: response.data.customer\n        }\n      });\n    }\n  };\n\n  const handleBack = () => {\n    if (step === 'otp') {\n      setStep('phone');\n      resetOTP();\n    } else {\n      navigate('/select-display');\n    }\n  };\n\n  const handleResendOTP = async () => {\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    await sendOTP(formattedPhone, formData.email, i18n.language);\n  };\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Header>\n          <Title>\n            {step === 'phone' ? \n              (isRTL ? 'تسجيل الدخول' : 'Login') : \n              (isRTL ? 'رمز التحقق' : 'Verification Code')\n            }\n          </Title>\n          \n          {selectedDisplay && (\n            <DisplayInfo>\n              <strong>{t('displayNumber', { number: selectedDisplay.displayNumber })}</strong>\n              <br />\n              {selectedDisplay.name}\n            </DisplayInfo>\n          )}\n        </Header>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        \n        {otpSent && step === 'otp' && (\n          <SuccessMessage>\n            {t('otpSent')}\n          </SuccessMessage>\n        )}\n\n        {step === 'phone' ? (\n          <Form onSubmit={handleSendOTP}>\n            <InputGroup>\n              <Label>{t('phoneNumber')}</Label>\n              <Input\n                type=\"tel\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                placeholder={t('enterPhoneNumber')}\n                required\n              />\n              {validationErrors.phoneNumber && (\n                <ErrorMessage>{validationErrors.phoneNumber}</ErrorMessage>\n              )}\n            </InputGroup>\n\n            <InputGroup>\n              <Label>{t('email')}</Label>\n              <Input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                placeholder={t('enterEmail')}\n                required\n              />\n              {validationErrors.email && (\n                <ErrorMessage>{validationErrors.email}</ErrorMessage>\n              )}\n            </InputGroup>\n\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? t('processing') : t('sendOTP')}\n            </Button>\n          </Form>\n        ) : (\n          <Form onSubmit={handleVerifyOTP}>\n            <OTPInfo>\n              {isRTL ? \n                `تم إرسال رمز التحقق إلى: ${formData.email}` :\n                `Verification code sent to: ${formData.email}`\n              }\n            </OTPInfo>\n\n            <InputGroup>\n              <Label>{t('otpCode')}</Label>\n              <Input\n                type=\"text\"\n                name=\"otpCode\"\n                value={formData.otpCode}\n                onChange={handleInputChange}\n                placeholder={t('enterOTP')}\n                maxLength=\"6\"\n                required\n              />\n              {validationErrors.otpCode && (\n                <ErrorMessage>{validationErrors.otpCode}</ErrorMessage>\n              )}\n            </InputGroup>\n\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? t('processing') : t('verifyOTP')}\n            </Button>\n\n            <Button \n              type=\"button\" \n              onClick={handleResendOTP}\n              disabled={loading}\n              style={{ background: '#6c757d' }}\n            >\n              {isRTL ? 'إعادة إرسال الرمز' : 'Resend Code'}\n            </Button>\n          </Form>\n        )}\n\n        <div style={{ marginTop: '20px', textAlign: 'center' }}>\n          <BackButton onClick={handleBack}>\n            {t('back')}\n          </BackButton>\n        </div>\n      </Card>\n    </Container>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,MAAM,CAAEC,OAAO,KAAQ,kBAAkB,CAClD,OAASC,wBAAwB,CAAEC,aAAa,CAAEC,sBAAsB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnG,KAAM,CAAAC,SAAS,CAAGV,MAAM,CAACW,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGd,MAAM,CAACW,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,MAAM,CAAGf,MAAM,CAACW,GAAG;AACzB;AACA;AACA,CAAC,CAED,KAAM,CAAAK,KAAK,CAAGhB,MAAM,CAACiB,EAAE;AACvB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGlB,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,IAAI,CAAGnB,MAAM,CAACoB,IAAI;AACxB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGrB,MAAM,CAACW,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,KAAK,CAAGtB,MAAM,CAACuB,KAAK;AAC1B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,KAAK,CAAGxB,MAAM,CAACyB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA,eAAeb,KAAK,EAAIA,KAAK,CAACc,IAAI,GAAK,KAAK,CAAG,KAAK,CAAG,SAAS;AAChE,gBAAgBd,KAAK,EAAIA,KAAK,CAACc,IAAI,GAAK,KAAK,CAAG,MAAM,CAAG,SAAS;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,MAAM,CAAG3B,MAAM,CAAC4B,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAahB,KAAK,EAAIA,KAAK,CAACiB,QAAQ,CAAG,GAAG,CAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG9B,MAAM,CAAC4B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG/B,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAqB,cAAc,CAAGhC,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAsB,OAAO,CAAGjC,MAAM,CAACW,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAuB,SAAS,CAAGA,CAAA,GAAM,KAAAC,eAAA,CACtB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGxC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAyC,QAAQ,CAAGxC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAyC,QAAQ,CAAGxC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEyC,KAAM,CAAC,CAAGtC,OAAO,CAAC,CAAC,CAC3B,KAAM,CAAEuC,OAAO,CAAEC,KAAK,CAAEC,OAAO,CAAEC,OAAO,CAAEC,SAAS,CAAEC,QAAS,CAAC,CAAG7C,MAAM,CAAC,CAAC,CAE1E,KAAM,CAAC8C,IAAI,CAAEC,OAAO,CAAC,CAAGpD,QAAQ,CAAC,OAAO,CAAC,CAAE;AAC3C,KAAM,CAACqD,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,CACvCuD,WAAW,CAAE,EAAE,CACfC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE5D,KAAM,CAAAiB,KAAK,CAAGwB,IAAI,CAACmB,QAAQ,GAAK,IAAI,CACpC,KAAM,CAAAC,eAAe,EAAAtB,eAAA,CAAGI,QAAQ,CAACmB,KAAK,UAAAvB,eAAA,iBAAdA,eAAA,CAAgBsB,eAAe,CAEvD,KAAM,CAAAE,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCb,WAAW,CAACc,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACH,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CAEH;AACA,GAAIR,gBAAgB,CAACO,IAAI,CAAC,CAAE,CAC1BN,mBAAmB,CAACS,IAAI,GAAK,CAC3B,GAAGA,IAAI,CACP,CAACH,IAAI,EAAG,EACV,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,MAAM,CAAG,CAAC,CAAC,CAEjB,GAAInB,IAAI,GAAK,OAAO,CAAE,CACpB,GAAI,CAACE,QAAQ,CAACE,WAAW,CAAE,CACzBe,MAAM,CAACf,WAAW,CAAGf,CAAC,CAAC,kBAAkB,CAAC,CAC5C,CAAC,IAAM,IAAI,CAACjC,wBAAwB,CAAC8C,QAAQ,CAACE,WAAW,CAAC,CAAE,CAC1De,MAAM,CAACf,WAAW,CAAGf,CAAC,CAAC,oBAAoB,CAAC,CAC9C,CAEA,GAAI,CAACa,QAAQ,CAACG,KAAK,CAAE,CACnBc,MAAM,CAACd,KAAK,CAAGhB,CAAC,CAAC,YAAY,CAAC,CAChC,CAAC,IAAM,IAAI,CAAChC,aAAa,CAAC6C,QAAQ,CAACG,KAAK,CAAC,CAAE,CACzCc,MAAM,CAACd,KAAK,CAAGhB,CAAC,CAAC,cAAc,CAAC,CAClC,CACF,CAAC,IAAM,IAAIW,IAAI,GAAK,KAAK,CAAE,CACzB,GAAI,CAACE,QAAQ,CAACI,OAAO,CAAE,CACrBa,MAAM,CAACb,OAAO,CAAGjB,CAAC,CAAC,UAAU,CAAC,CAChC,CAAC,IAAM,IAAIa,QAAQ,CAACI,OAAO,CAACc,MAAM,GAAK,CAAC,CAAE,CACxCD,MAAM,CAACb,OAAO,CAAGjB,CAAC,CAAC,YAAY,CAAC,CAClC,CACF,CAEAmB,mBAAmB,CAACW,MAAM,CAAC,CAC3B,MAAO,CAAAE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACC,MAAM,GAAK,CAAC,CACzC,CAAC,CAED,KAAM,CAAAG,aAAa,CAAG,KAAO,CAAAV,CAAC,EAAK,CACjCA,CAAC,CAACW,cAAc,CAAC,CAAC,CAElB,GAAI,CAACN,YAAY,CAAC,CAAC,CAAE,OAErB,KAAM,CAAAO,cAAc,CAAGnE,sBAAsB,CAAC4C,QAAQ,CAACE,WAAW,CAAC,CACnE,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAA7B,OAAO,CAAC4B,cAAc,CAAEvB,QAAQ,CAACG,KAAK,CAAEf,IAAI,CAACmB,QAAQ,CAAC,CAE7E,GAAIiB,QAAQ,CAAE,CACZzB,OAAO,CAAC,KAAK,CAAC,CAChB,CACF,CAAC,CAED,KAAM,CAAA0B,eAAe,CAAG,KAAO,CAAAd,CAAC,EAAK,CACnCA,CAAC,CAACW,cAAc,CAAC,CAAC,CAElB,GAAI,CAACN,YAAY,CAAC,CAAC,CAAE,OAErB,KAAM,CAAAO,cAAc,CAAGnE,sBAAsB,CAAC4C,QAAQ,CAACE,WAAW,CAAC,CACnE,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAA5B,SAAS,CAAC2B,cAAc,CAAEvB,QAAQ,CAACI,OAAO,CAAC,CAElE,GAAIoB,QAAQ,CAAE,CACZ;AACAjC,KAAK,CAACiC,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAEH,QAAQ,CAACE,IAAI,CAACE,KAAK,CAAC,CAElD;AACAvC,QAAQ,CAAC,aAAa,CAAE,CACtBoB,KAAK,CAAE,CACLD,eAAe,CACfmB,QAAQ,CAAEH,QAAQ,CAACE,IAAI,CAACC,QAC1B,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAI/B,IAAI,GAAK,KAAK,CAAE,CAClBC,OAAO,CAAC,OAAO,CAAC,CAChBF,QAAQ,CAAC,CAAC,CACZ,CAAC,IAAM,CACLR,QAAQ,CAAC,iBAAiB,CAAC,CAC7B,CACF,CAAC,CAED,KAAM,CAAAyC,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAP,cAAc,CAAGnE,sBAAsB,CAAC4C,QAAQ,CAACE,WAAW,CAAC,CACnE,KAAM,CAAAP,OAAO,CAAC4B,cAAc,CAAEvB,QAAQ,CAACG,KAAK,CAAEf,IAAI,CAACmB,QAAQ,CAAC,CAC9D,CAAC,CAED,mBACEjD,IAAA,CAACG,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAAmE,QAAA,cACtBvE,KAAA,CAACK,IAAI,EAAAkE,QAAA,eACHvE,KAAA,CAACM,MAAM,EAAAiE,QAAA,eACLzE,IAAA,CAACS,KAAK,EAAAgE,QAAA,CACHjC,IAAI,GAAK,OAAO,CACdlC,KAAK,CAAG,cAAc,CAAG,OAAO,CAChCA,KAAK,CAAG,YAAY,CAAG,mBAAoB,CAEzC,CAAC,CAEP4C,eAAe,eACdhD,KAAA,CAACS,WAAW,EAAA8D,QAAA,eACVzE,IAAA,WAAAyE,QAAA,CAAS5C,CAAC,CAAC,eAAe,CAAE,CAAE6C,MAAM,CAAExB,eAAe,CAACyB,aAAc,CAAC,CAAC,CAAS,CAAC,cAChF3E,IAAA,QAAK,CAAC,CACLkD,eAAe,CAACI,IAAI,EACV,CACd,EACK,CAAC,CAERnB,KAAK,eAAInC,IAAA,CAACwB,YAAY,EAAAiD,QAAA,CAAEtC,KAAK,CAAe,CAAC,CAE7CC,OAAO,EAAII,IAAI,GAAK,KAAK,eACxBxC,IAAA,CAACyB,cAAc,EAAAgD,QAAA,CACZ5C,CAAC,CAAC,SAAS,CAAC,CACC,CACjB,CAEAW,IAAI,GAAK,OAAO,cACftC,KAAA,CAACU,IAAI,EAACgE,QAAQ,CAAEb,aAAc,CAAAU,QAAA,eAC5BvE,KAAA,CAACY,UAAU,EAAA2D,QAAA,eACTzE,IAAA,CAACe,KAAK,EAAA0D,QAAA,CAAE5C,CAAC,CAAC,aAAa,CAAC,CAAQ,CAAC,cACjC7B,IAAA,CAACiB,KAAK,EACJE,IAAI,CAAC,KAAK,CACVmC,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAEb,QAAQ,CAACE,WAAY,CAC5BiC,QAAQ,CAAEzB,iBAAkB,CAC5B0B,WAAW,CAAEjD,CAAC,CAAC,kBAAkB,CAAE,CACnCkD,QAAQ,MACT,CAAC,CACDhC,gBAAgB,CAACH,WAAW,eAC3B5C,IAAA,CAACwB,YAAY,EAAAiD,QAAA,CAAE1B,gBAAgB,CAACH,WAAW,CAAe,CAC3D,EACS,CAAC,cAEb1C,KAAA,CAACY,UAAU,EAAA2D,QAAA,eACTzE,IAAA,CAACe,KAAK,EAAA0D,QAAA,CAAE5C,CAAC,CAAC,OAAO,CAAC,CAAQ,CAAC,cAC3B7B,IAAA,CAACiB,KAAK,EACJE,IAAI,CAAC,OAAO,CACZmC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEb,QAAQ,CAACG,KAAM,CACtBgC,QAAQ,CAAEzB,iBAAkB,CAC5B0B,WAAW,CAAEjD,CAAC,CAAC,YAAY,CAAE,CAC7BkD,QAAQ,MACT,CAAC,CACDhC,gBAAgB,CAACF,KAAK,eACrB7C,IAAA,CAACwB,YAAY,EAAAiD,QAAA,CAAE1B,gBAAgB,CAACF,KAAK,CAAe,CACrD,EACS,CAAC,cAEb7C,IAAA,CAACoB,MAAM,EAACD,IAAI,CAAC,QAAQ,CAACG,QAAQ,CAAEY,OAAQ,CAAAuC,QAAA,CACrCvC,OAAO,CAAGL,CAAC,CAAC,YAAY,CAAC,CAAGA,CAAC,CAAC,SAAS,CAAC,CACnC,CAAC,EACL,CAAC,cAEP3B,KAAA,CAACU,IAAI,EAACgE,QAAQ,CAAET,eAAgB,CAAAM,QAAA,eAC9BzE,IAAA,CAAC0B,OAAO,EAAA+C,QAAA,CACLnE,KAAK,CACJ,4BAA4BoC,QAAQ,CAACG,KAAK,EAAE,CAC5C,8BAA8BH,QAAQ,CAACG,KAAK,EAAE,CAEzC,CAAC,cAEV3C,KAAA,CAACY,UAAU,EAAA2D,QAAA,eACTzE,IAAA,CAACe,KAAK,EAAA0D,QAAA,CAAE5C,CAAC,CAAC,SAAS,CAAC,CAAQ,CAAC,cAC7B7B,IAAA,CAACiB,KAAK,EACJE,IAAI,CAAC,MAAM,CACXmC,IAAI,CAAC,SAAS,CACdC,KAAK,CAAEb,QAAQ,CAACI,OAAQ,CACxB+B,QAAQ,CAAEzB,iBAAkB,CAC5B0B,WAAW,CAAEjD,CAAC,CAAC,UAAU,CAAE,CAC3BmD,SAAS,CAAC,GAAG,CACbD,QAAQ,MACT,CAAC,CACDhC,gBAAgB,CAACD,OAAO,eACvB9C,IAAA,CAACwB,YAAY,EAAAiD,QAAA,CAAE1B,gBAAgB,CAACD,OAAO,CAAe,CACvD,EACS,CAAC,cAEb9C,IAAA,CAACoB,MAAM,EAACD,IAAI,CAAC,QAAQ,CAACG,QAAQ,CAAEY,OAAQ,CAAAuC,QAAA,CACrCvC,OAAO,CAAGL,CAAC,CAAC,YAAY,CAAC,CAAGA,CAAC,CAAC,WAAW,CAAC,CACrC,CAAC,cAET7B,IAAA,CAACoB,MAAM,EACLD,IAAI,CAAC,QAAQ,CACb8D,OAAO,CAAET,eAAgB,CACzBlD,QAAQ,CAAEY,OAAQ,CAClBgD,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAU,CAAE,CAAAV,QAAA,CAEhCnE,KAAK,CAAG,mBAAmB,CAAG,aAAa,CACtC,CAAC,EACL,CACP,cAEDN,IAAA,QAAKkF,KAAK,CAAE,CAAEE,SAAS,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAZ,QAAA,cACrDzE,IAAA,CAACuB,UAAU,EAAC0D,OAAO,CAAEV,UAAW,CAAAE,QAAA,CAC7B5C,CAAC,CAAC,MAAM,CAAC,CACA,CAAC,CACV,CAAC,EACF,CAAC,CACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}