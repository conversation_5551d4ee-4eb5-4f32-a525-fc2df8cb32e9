import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import styled, { keyframes } from 'styled-components';
import { displayService } from '../services/api';
import { formatTimeRemaining, calculateTimeRemaining } from '../utils/helpers';
import io from 'socket.io-client';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`;

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: ${pulse} 4s ease-in-out infinite;
  }
`;

const DisplayCard = styled.div`
  background: white;
  border-radius: 30px;
  padding: 60px;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 800px;
  width: 100%;
  position: relative;
  z-index: 1;
  animation: ${fadeIn} 1s ease-out;
`;

const DisplayNumber = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  background: #667eea;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 1.2rem;
`;

const CustomerName = styled.h1`
  font-size: 4rem;
  font-weight: bold;
  color: #333;
  margin: 40px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  word-break: break-word;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const WelcomeMessage = styled.div`
  font-size: 1.8rem;
  color: #666;
  margin-bottom: 30px;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 1.3rem;
  }
`;

const TimeRemaining = styled.div`
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin: 30px 0;
  border-left: 5px solid #667eea;
`;

const TimeLabel = styled.div`
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 10px;
`;

const TimeValue = styled.div`
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  font-family: 'Courier New', monospace;
`;

const StatusMessage = styled.div`
  background: ${props => {
    switch (props.type) {
      case 'waiting': return '#ffc107';
      case 'active': return '#28a745';
      case 'ending': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  color: white;
  padding: 20px;
  border-radius: 15px;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 20px 0;
`;

const LoadingSpinner = styled.div`
  width: 60px;
  height: 60px;
  border: 6px solid #f3f3f3;
  border-top: 6px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 40px auto;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  font-size: 1.2rem;
`;

const DisplayScreen = () => {
  const { displayId } = useParams();
  const [displayData, setDisplayData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    // إعداد Socket.IO للتحديثات المباشرة
    const newSocket = io(process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:3001');
    setSocket(newSocket);

    // الانضمام لغرفة الشاشة
    newSocket.emit('join-display', displayId);

    // الاستماع للتحديثات
    newSocket.on('display-activated', (data) => {
      fetchDisplayData();
    });

    newSocket.on('transaction-ended', () => {
      fetchDisplayData();
    });

    return () => {
      newSocket.disconnect();
    };
  }, [displayId]);

  useEffect(() => {
    fetchDisplayData();
    
    // تحديث البيانات كل 10 ثوان
    const interval = setInterval(fetchDisplayData, 10000);
    return () => clearInterval(interval);
  }, [displayId]);

  useEffect(() => {
    // تحديث العد التنازلي كل ثانية
    if (displayData && displayData.status === 'occupied' && displayData.endTime) {
      const timer = setInterval(() => {
        const remaining = calculateTimeRemaining(displayData.endTime);
        setTimeRemaining(remaining);
        
        if (remaining.expired) {
          fetchDisplayData();
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [displayData]);

  const fetchDisplayData = async () => {
    try {
      const response = await displayService.getDisplay(displayId);
      if (response.success) {
        setDisplayData(response.data);
        setError(null);
      } else {
        setError(response.message || 'فشل في تحميل بيانات الشاشة');
      }
    } catch (error) {
      setError('خطأ في الشبكة');
      console.error('Error fetching display data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container>
        <DisplayCard>
          <LoadingSpinner />
          <div style={{ fontSize: '1.2rem', color: '#666', marginTop: '20px' }}>
            جاري التحميل...
          </div>
        </DisplayCard>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <DisplayCard>
          <ErrorMessage>{error}</ErrorMessage>
        </DisplayCard>
      </Container>
    );
  }

  if (!displayData) {
    return (
      <Container>
        <DisplayCard>
          <ErrorMessage>الشاشة غير موجودة</ErrorMessage>
        </DisplayCard>
      </Container>
    );
  }

  return (
    <Container>
      <DisplayCard>
        <DisplayNumber>
          شاشة {displayData.displayNumber}
        </DisplayNumber>

        {displayData.status === 'available' && (
          <>
            <WelcomeMessage>أهلاً وسهلاً بكم</WelcomeMessage>
            <StatusMessage type="waiting">
              الشاشة متاحة للحجز
            </StatusMessage>
          </>
        )}

        {displayData.status === 'occupied' && displayData.customerName && (
          <>
            <WelcomeMessage>أهلاً وسهلاً</WelcomeMessage>
            <CustomerName>{displayData.customerName}</CustomerName>
            
            {timeRemaining && !timeRemaining.expired && (
              <TimeRemaining>
                <TimeLabel>الوقت المتبقي</TimeLabel>
                <TimeValue>{formatTimeRemaining(timeRemaining.remaining)}</TimeValue>
              </TimeRemaining>
            )}

            <StatusMessage type={timeRemaining && timeRemaining.remaining < 60 ? 'ending' : 'active'}>
              {timeRemaining && timeRemaining.remaining < 60 ? 
                'العرض على وشك الانتهاء' : 
                'العرض نشط الآن'
              }
            </StatusMessage>
          </>
        )}

        {displayData.status === 'reserved' && (
          <>
            <WelcomeMessage>جاري التحضير...</WelcomeMessage>
            <StatusMessage type="waiting">
              الشاشة محجوزة - في انتظار الدفع
            </StatusMessage>
          </>
        )}

        {displayData.status === 'maintenance' && (
          <>
            <WelcomeMessage>نعتذر للإزعاج</WelcomeMessage>
            <StatusMessage type="maintenance">
              الشاشة تحت الصيانة
            </StatusMessage>
          </>
        )}
      </DisplayCard>
    </Container>
  );
};

export default DisplayScreen;
