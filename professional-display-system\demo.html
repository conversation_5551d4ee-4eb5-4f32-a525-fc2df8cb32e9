<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الشاشات الاحترافي</title>
    <style>
        /* إعادة تعيين الأساسيات */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* المتغيرات */
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --white: #ffffff;
            --dark-color: #343a40;
            
            --border-radius: 12px;
            --border-radius-lg: 20px;
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
            --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.25);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* الأساسيات */
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .rtl {
            direction: rtl;
        }

        .ltr {
            direction: ltr;
        }

        /* الصفحة الرئيسية */
        .home-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .home-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 500px;
            padding: 20px;
        }

        .home-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-xl);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .home-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        /* الشعار */
        .home-logo {
            position: relative;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-icon {
            font-size: 4rem;
            z-index: 2;
            position: relative;
            animation: float 3s ease-in-out infinite;
        }

        .logo-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .pulse-ring {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            animation: pulse 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }

        .pulse-ring.delay-1 {
            animation-delay: 0.5s;
        }

        .pulse-ring.delay-2 {
            animation-delay: 1s;
        }

        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.5);
                opacity: 0;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* العنوان */
        .home-header {
            margin-bottom: 30px;
        }

        .home-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--white);
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .home-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        /* اختيار اللغة */
        .language-section {
            margin-bottom: 30px;
        }

        .language-label {
            color: var(--white);
            margin-bottom: 15px;
            font-weight: 600;
        }

        .language-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .language-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius);
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            cursor: pointer;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .language-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .language-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .language-flag {
            font-size: 18px;
        }

        /* الأزرار */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 16px 32px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 18px;
            font-weight: 700;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            user-select: none;
            outline: none;
            width: 100%;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--success-color), #20c997);
            color: var(--white);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .btn-icon {
            font-size: 1.2em;
            line-height: 1;
        }

        /* معلومات إضافية */
        .home-info {
            margin-bottom: 25px;
        }

        .info-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .info-icon {
            font-size: 16px;
        }

        /* إحصائيات سريعة */
        .home-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 25px;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--white);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.2;
        }

        /* تذييل */
        .home-footer {
            text-align: center;
        }

        .footer-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 13px;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .footer-version {
            color: rgba(255, 255, 255, 0.5);
            font-size: 11px;
            font-weight: 600;
        }

        /* الخلفية المتحركة */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            z-index: 1;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float-random 20s infinite linear;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 15%;
            animation-delay: 5s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation-delay: 10s;
        }

        .shape-4 {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 30%;
            animation-delay: 15s;
        }

        @keyframes float-random {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) rotate(90deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-40px) rotate(180deg);
                opacity: 0.3;
            }
            75% {
                transform: translateY(-20px) rotate(270deg);
                opacity: 0.6;
            }
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .home-card {
                padding: 30px 25px;
            }
            
            .home-title {
                font-size: 1.5rem;
            }
            
            .language-buttons {
                flex-direction: column;
            }
            
            .home-stats {
                flex-direction: column;
                gap: 15px;
            }
        }

        /* إخفاء العناصر */
        .hidden {
            display: none;
        }

        /* صفحة الشاشات */
        .displays-page {
            min-height: 100vh;
            padding: 20px;
        }

        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--white);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .displays-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .display-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: var(--border-radius-lg);
            padding: 25px;
            border: 3px solid;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .display-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
            background: rgba(255, 255, 255, 0.15);
        }

        .display-card.available {
            border-color: var(--success-color);
        }

        .display-card.occupied {
            border-color: var(--danger-color);
            cursor: not-allowed;
        }

        .display-card.reserved {
            border-color: var(--warning-color);
            cursor: not-allowed;
        }

        .display-card.selected {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.2);
        }

        .display-card.selected::after {
            content: '✓';
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--success-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            animation: checkmark-appear 0.3s ease;
        }

        @keyframes checkmark-appear {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .display-number {
            text-align: center;
            margin-bottom: 20px;
        }

        .number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--white);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .display-name {
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--white);
            margin-bottom: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .display-status {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 16px;
            border-radius: var(--border-radius);
            color: var(--white);
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 15px;
            text-align: center;
        }

        .customer-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: var(--border-radius);
            margin-bottom: 15px;
            text-align: center;
            color: var(--white);
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- الصفحة الرئيسية -->
        <div id="home-page" class="home-page rtl">
            <div class="home-container">
                <div class="home-card">
                    <!-- الشعار -->
                    <div class="home-logo">
                        <div class="logo-icon">📺</div>
                        <div class="logo-animation">
                            <div class="pulse-ring"></div>
                            <div class="pulse-ring delay-1"></div>
                            <div class="pulse-ring delay-2"></div>
                        </div>
                    </div>

                    <!-- العنوان -->
                    <div class="home-header">
                        <h1 class="home-title">نظام إدارة الشاشات الاحترافي</h1>
                        <p class="home-subtitle">أهلاً وسهلاً بكم</p>
                    </div>

                    <!-- اختيار اللغة -->
                    <div class="language-section">
                        <p class="language-label">اختر اللغة</p>
                        <div class="language-buttons">
                            <button class="language-btn active" id="btn-ar">
                                <span class="language-flag">🇸🇦</span>
                                العربية
                            </button>
                            <button class="language-btn" id="btn-en">
                                <span class="language-flag">🇺🇸</span>
                                English
                            </button>
                        </div>
                    </div>

                    <!-- الأزرار الرئيسية -->
                    <div class="home-actions">
                        <button class="btn btn-primary" id="btn-start-transaction">
                            <span class="btn-icon">🚀</span>
                            ابدأ المعاملة
                        </button>

                        <button class="btn btn-secondary" id="btn-owner-login">
                            <span class="btn-icon">👤</span>
                            دخول المالك
                        </button>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="home-info">
                        <div class="info-item">
                            <span class="info-icon">⚡</span>
                            <span class="info-text">سريع وآمن</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">💳</span>
                            <span class="info-text">دفع إلكتروني</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">📱</span>
                            <span class="info-text">سهل الاستخدام</span>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="home-stats">
                        <div class="stat-item">
                            <div class="stat-number">5</div>
                            <div class="stat-label">شاشة متاحة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50</div>
                            <div class="stat-label">ريال فقط</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">5</div>
                            <div class="stat-label">دقيقة عرض</div>
                        </div>
                    </div>

                    <!-- تذييل -->
                    <div class="home-footer">
                        <p class="footer-text">نظام احترافي لإدارة الشاشات التفاعلية</p>
                        <div class="footer-version">v1.0.0</div>
                    </div>
                </div>

                <!-- خلفية متحركة -->
                <div class="background-animation">
                    <div class="floating-shape shape-1"></div>
                    <div class="floating-shape shape-2"></div>
                    <div class="floating-shape shape-3"></div>
                    <div class="floating-shape shape-4"></div>
                </div>
            </div>
        </div>

        <!-- صفحة الشاشات -->
        <div id="displays-page" class="displays-page rtl hidden">
            <div class="page-header">
                <button class="btn btn-secondary" id="btn-back" style="width: auto; margin: 0;">
                    <span class="btn-icon">→</span>
                    العودة
                </button>

                <h1 class="page-title">اختر الشاشة</h1>

                <button id="btn-refresh" style="background: rgba(255, 255, 255, 0.2); border: 2px solid rgba(255, 255, 255, 0.3); border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; cursor: pointer;">
                    🔄
                </button>
            </div>

            <div class="displays-grid" id="displays-grid">
                <!-- سيتم إضافة الشاشات هنا بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // بيانات وهمية للشاشات
        const displays = [
            { id: 1, name: 'شاشة 1 - Display 1', status: 'available' },
            { id: 2, name: 'شاشة 2 - Display 2', status: 'occupied', customerName: 'أحمد محمد علي' },
            { id: 3, name: 'شاشة 3 - Display 3', status: 'available' },
            { id: 4, name: 'شاشة 4 - Display 4', status: 'reserved', customerName: 'فاطمة أحمد' },
            { id: 5, name: 'شاشة 5 - Display 5', status: 'available' }
        ];

        let currentLanguage = 'ar';

        // تغيير اللغة
        function setLanguage(lang) {
            currentLanguage = lang;
            const buttons = document.querySelectorAll('.language-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // إضافة active للزر المختار
            const targetButton = Array.from(buttons).find(btn =>
                (lang === 'ar' && btn.textContent.includes('العربية')) ||
                (lang === 'en' && btn.textContent.includes('English'))
            );
            if (targetButton) {
                targetButton.classList.add('active');
            }

            // تحديث النصوص حسب اللغة
            updateLanguage();
        }

        function updateLanguage() {
            const isArabic = currentLanguage === 'ar';
            document.documentElement.dir = isArabic ? 'rtl' : 'ltr';
            document.documentElement.lang = currentLanguage;

            // تحديث النصوص في الصفحة الرئيسية
            const homeTitle = document.querySelector('.home-title');
            const homeSubtitle = document.querySelector('.home-subtitle');
            const languageLabel = document.querySelector('.language-label');
            const startBtn = document.querySelector('.btn-primary');
            const ownerBtn = document.querySelector('.btn-secondary');

            if (homeTitle && homeSubtitle && languageLabel && startBtn && ownerBtn) {
                if (isArabic) {
                    homeTitle.textContent = 'نظام إدارة الشاشات الاحترافي';
                    homeSubtitle.textContent = 'أهلاً وسهلاً بكم';
                    languageLabel.textContent = 'اختر اللغة';
                    startBtn.innerHTML = '<span class="btn-icon">🚀</span>ابدأ المعاملة';
                    ownerBtn.innerHTML = '<span class="btn-icon">👤</span>دخول المالك';
                } else {
                    homeTitle.textContent = 'Professional Display Management System';
                    homeSubtitle.textContent = 'Welcome';
                    languageLabel.textContent = 'Select Language';
                    startBtn.innerHTML = '<span class="btn-icon">🚀</span>Start Transaction';
                    ownerBtn.innerHTML = '<span class="btn-icon">👤</span>Owner Login';
                }
            }

            // تحديث النصوص في صفحة الشاشات
            const pageTitle = document.querySelector('.page-title');
            const backBtn = document.querySelector('.btn-secondary');

            if (pageTitle && !document.getElementById('home-page').classList.contains('hidden')) {
                if (isArabic) {
                    pageTitle.textContent = 'اختر الشاشة';
                    if (backBtn) backBtn.innerHTML = '<span class="btn-icon">→</span>العودة';
                } else {
                    pageTitle.textContent = 'Select Display';
                    if (backBtn) backBtn.innerHTML = '<span class="btn-icon">←</span>Back';
                }
            }

            // تحديث الشاشات إذا كانت مفتوحة
            if (!document.getElementById('displays-page').classList.contains('hidden')) {
                loadDisplays();
            }
        }

        // عرض صفحة الشاشات
        function showDisplays() {
            console.log('🖥️ عرض صفحة الشاشات');
            const homePage = document.getElementById('home-page');
            const displaysPage = document.getElementById('displays-page');

            if (homePage && displaysPage) {
                homePage.classList.add('hidden');
                displaysPage.classList.remove('hidden');
                loadDisplays();
                console.log('✅ تم الانتقال لصفحة الشاشات');
            } else {
                console.error('❌ لم يتم العثور على عناصر الصفحة');
            }
        }

        // العودة للصفحة الرئيسية
        function showHome() {
            console.log('🏠 العودة للصفحة الرئيسية');
            const homePage = document.getElementById('home-page');
            const displaysPage = document.getElementById('displays-page');

            if (homePage && displaysPage) {
                displaysPage.classList.add('hidden');
                homePage.classList.remove('hidden');
                console.log('✅ تم الانتقال للصفحة الرئيسية');
            } else {
                console.error('❌ لم يتم العثور على عناصر الصفحة');
            }
        }

        // تحميل الشاشات
        function loadDisplays() {
            const grid = document.getElementById('displays-grid');
            if (!grid) return;

            grid.innerHTML = '';

            displays.forEach(display => {
                const statusColors = {
                    available: '#28a745',
                    occupied: '#dc3545',
                    reserved: '#ffc107',
                    maintenance: '#6c757d'
                };

                const statusTexts = {
                    available: currentLanguage === 'ar' ? 'متاحة' : 'Available',
                    occupied: currentLanguage === 'ar' ? 'مشغولة' : 'Occupied',
                    reserved: currentLanguage === 'ar' ? 'محجوزة' : 'Reserved',
                    maintenance: currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'
                };

                const card = document.createElement('div');
                card.className = `display-card ${display.status}`;
                card.style.borderColor = statusColors[display.status];

                // إنشاء محتوى البطاقة
                let cardContent = `
                    <div class="display-number">
                        <span class="number">${display.id}</span>
                    </div>
                    <div class="display-name">${display.name}</div>
                    <div class="display-status" style="background-color: ${statusColors[display.status]}">
                        ${statusTexts[display.status]}
                    </div>
                `;

                // إضافة معلومات العميل إذا وجدت
                if (display.customerName) {
                    cardContent += `
                        <div class="customer-info">
                            <strong>${currentLanguage === 'ar' ? 'العميل:' : 'Customer:'}</strong> ${display.customerName}
                        </div>
                    `;
                }

                // إضافة زر الاختيار للشاشات المتاحة
                if (display.status === 'available') {
                    cardContent += `
                        <button class="btn btn-primary display-select-btn" data-display-id="${display.id}" style="margin: 0; width: 100%;">
                            <span class="btn-icon">✓</span>
                            ${currentLanguage === 'ar' ? 'اختر هذه الشاشة' : 'Select This Display'}
                        </button>
                    `;
                }

                card.innerHTML = cardContent;

                // إضافة حدث النقر للشاشات المتاحة
                if (display.status === 'available') {
                    card.style.cursor = 'pointer';
                    card.addEventListener('click', () => selectDisplay(display.id));
                } else {
                    card.style.cursor = 'not-allowed';
                }

                grid.appendChild(card);
            });

            // إضافة event listeners لأزرار اختيار الشاشات
            const selectButtons = document.querySelectorAll('.display-select-btn');
            selectButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const displayId = parseInt(this.getAttribute('data-display-id'));
                    console.log(`تم النقر على زر اختيار الشاشة ${displayId}`);
                    selectDisplay(displayId);
                });
            });

            console.log(`تم تحميل ${displays.length} شاشة بنجاح`);
        }

        // اختيار شاشة
        function selectDisplay(displayId) {
            const display = displays.find(d => d.id === displayId);
            if (display && display.status === 'available') {
                // تأثير بصري للاختيار
                const cards = document.querySelectorAll('.display-card');
                cards.forEach(card => card.classList.remove('selected'));

                const selectedCard = Array.from(cards).find(card =>
                    card.querySelector('.number').textContent == displayId
                );
                if (selectedCard) {
                    selectedCard.classList.add('selected');
                    selectedCard.style.transform = 'translateY(-5px) scale(1.02)';
                    selectedCard.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.25)';
                }

                // رسالة تأكيد
                const message = currentLanguage === 'ar'
                    ? `✅ تم اختيار ${display.name}\n\n🔄 سيتم الانتقال لصفحة تسجيل الدخول...\n\n💰 السعر: 50 ريال\n⏰ المدة: 5 دقائق`
                    : `✅ Selected ${display.name}\n\n🔄 Redirecting to login page...\n\n💰 Price: 50 SAR\n⏰ Duration: 5 minutes`;

                alert(message);

                // محاكاة الانتقال لصفحة تسجيل الدخول
                setTimeout(() => {
                    const loginMessage = currentLanguage === 'ar'
                        ? '📱 صفحة تسجيل الدخول\n\nسيتم إضافة هذه الصفحة في المرحلة التالية:\n\n• إدخال رقم الهاتف\n• إدخال البريد الإلكتروني\n• استقبال رمز OTP\n• التحقق من الرمز\n• إدخال اسم العميل\n• اختيار طريقة الدفع\n• تأكيد المعاملة'
                        : '📱 Login Page\n\nThis page will be added in the next phase:\n\n• Enter phone number\n• Enter email address\n• Receive OTP code\n• Verify code\n• Enter customer name\n• Choose payment method\n• Confirm transaction';

                    alert(loginMessage);
                }, 1000);
            } else {
                const errorMessage = currentLanguage === 'ar'
                    ? '❌ هذه الشاشة غير متاحة حالياً'
                    : '❌ This display is not available';
                alert(errorMessage);
            }
        }

        // دخول المالك
        function showOwnerLogin() {
            const password = prompt(
                currentLanguage === 'ar'
                    ? '🔐 دخول المالك\n\nأدخل كلمة المرور:\n(كلمة المرور الافتراضية: admin123)'
                    : '🔐 Owner Login\n\nEnter password:\n(Default password: admin123)'
            );

            if (password === null) {
                return; // المستخدم ألغى العملية
            }

            if (password === 'admin123') {
                const successMessage = currentLanguage === 'ar'
                    ? '✅ تم تسجيل الدخول بنجاح!\n\n📊 لوحة تحكم المالك:\n\n• إحصائيات اليوم: 5 معاملات، 250 ريال\n• إجمالي المعاملات: 150\n• إجمالي الإيرادات: 7500 ريال\n• الشاشات المتاحة: 3\n• الشاشات المشغولة: 1\n• الشاشات المحجوزة: 1\n\n🔧 سيتم إضافة لوحة التحكم الكاملة في المرحلة التالية'
                    : '✅ Login successful!\n\n📊 Owner Dashboard:\n\n• Today: 5 transactions, 250 SAR\n• Total transactions: 150\n• Total revenue: 7500 SAR\n• Available displays: 3\n• Occupied displays: 1\n• Reserved displays: 1\n\n🔧 Full dashboard will be added in the next phase';

                alert(successMessage);
            } else {
                const errorMessage = currentLanguage === 'ar'
                    ? '❌ كلمة المرور غير صحيحة!\n\nحاول مرة أخرى بكلمة المرور: admin123'
                    : '❌ Incorrect password!\n\nTry again with password: admin123';

                alert(errorMessage);
            }
        }

        // تحديث الوقت كل ثانية
        setInterval(() => {
            // تحديث الوقت المتبقي للشاشات المشغولة
            if (!document.getElementById('displays-page').classList.contains('hidden')) {
                updateDisplayTimers();
            }
        }, 1000);

        // تحديث عدادات الوقت للشاشات المشغولة
        function updateDisplayTimers() {
            displays.forEach((display, index) => {
                if (display.status === 'occupied' && display.endTime) {
                    const now = new Date();
                    const end = new Date(display.endTime);
                    const diff = end.getTime() - now.getTime();

                    if (diff <= 0) {
                        // انتهت المدة - تحويل الشاشة لمتاحة
                        displays[index].status = 'available';
                        displays[index].customerName = null;
                        displays[index].endTime = null;
                        loadDisplays(); // إعادة تحميل الشاشات
                    }
                }
            });
        }

        // إضافة أوقات انتهاء وهمية للشاشات المشغولة
        function initializeDisplayTimers() {
            displays.forEach((display, index) => {
                if (display.status === 'occupied') {
                    // إضافة وقت انتهاء بعد 3 دقائق من الآن
                    displays[index].endTime = new Date(Date.now() + 3 * 60 * 1000).toISOString();
                }
            });
        }

        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل نظام إدارة الشاشات الاحترافي');

            // التأكد من وجود العناصر
            const homePage = document.getElementById('home-page');
            const displaysPage = document.getElementById('displays-page');

            if (homePage && displaysPage) {
                console.log('✅ تم العثور على جميع عناصر الصفحة');
                initializeDisplayTimers();
                updateLanguage();
                setupEventListeners();

                // إظهار الصفحة الرئيسية وإخفاء صفحة الشاشات
                homePage.classList.remove('hidden');
                displaysPage.classList.add('hidden');

                console.log('✅ تم تهيئة النظام بنجاح');
            } else {
                console.error('❌ لم يتم العثور على عناصر الصفحة الأساسية');
            }
        });

        // تشغيل إضافي للتأكد
        window.addEventListener('load', function() {
            console.log('🔄 تحميل إضافي للنظام');
            if (typeof setupEventListeners === 'function') {
                setupEventListeners();
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            console.log('🔧 بدء إعداد مستمعي الأحداث...');

            // أزرار اللغة
            const btnAr = document.getElementById('btn-ar');
            const btnEn = document.getElementById('btn-en');

            if (btnAr) {
                // إزالة المستمع القديم إن وجد
                btnAr.removeEventListener('click', handleArabicClick);
                btnAr.addEventListener('click', handleArabicClick);
                console.log('✅ تم ربط زر العربية');
            } else {
                console.error('❌ لم يتم العثور على زر العربية');
            }

            if (btnEn) {
                // إزالة المستمع القديم إن وجد
                btnEn.removeEventListener('click', handleEnglishClick);
                btnEn.addEventListener('click', handleEnglishClick);
                console.log('✅ تم ربط زر الإنجليزية');
            } else {
                console.error('❌ لم يتم العثور على زر الإنجليزية');
            }

            // الأزرار الرئيسية
            const btnStartTransaction = document.getElementById('btn-start-transaction');
            const btnOwnerLogin = document.getElementById('btn-owner-login');

            if (btnStartTransaction) {
                btnStartTransaction.removeEventListener('click', handleStartTransaction);
                btnStartTransaction.addEventListener('click', handleStartTransaction);
                console.log('✅ تم ربط زر ابدأ المعاملة');
            } else {
                console.error('❌ لم يتم العثور على زر ابدأ المعاملة');
            }

            if (btnOwnerLogin) {
                btnOwnerLogin.removeEventListener('click', handleOwnerLogin);
                btnOwnerLogin.addEventListener('click', handleOwnerLogin);
                console.log('✅ تم ربط زر دخول المالك');
            } else {
                console.error('❌ لم يتم العثور على زر دخول المالك');
            }

            // أزرار صفحة الشاشات
            const btnBack = document.getElementById('btn-back');
            const btnRefresh = document.getElementById('btn-refresh');

            if (btnBack) {
                btnBack.removeEventListener('click', handleBackButton);
                btnBack.addEventListener('click', handleBackButton);
                console.log('✅ تم ربط زر العودة');
            } else {
                console.log('ℹ️ زر العودة غير موجود (طبيعي في الصفحة الرئيسية)');
            }

            if (btnRefresh) {
                btnRefresh.removeEventListener('click', handleRefreshButton);
                btnRefresh.addEventListener('click', handleRefreshButton);
                console.log('✅ تم ربط زر التحديث');
            } else {
                console.log('ℹ️ زر التحديث غير موجود (طبيعي في الصفحة الرئيسية)');
            }

            console.log('✅ تم إعداد جميع مستمعي الأحداث');
        }

        // دوال معالجة الأحداث
        function handleArabicClick() {
            console.log('🇸🇦 تم النقر على زر العربية');
            setLanguage('ar');
        }

        function handleEnglishClick() {
            console.log('🇺🇸 تم النقر على زر الإنجليزية');
            setLanguage('en');
        }

        function handleStartTransaction() {
            console.log('🚀 تم النقر على زر ابدأ المعاملة');
            showDisplays();
        }

        function handleOwnerLogin() {
            console.log('👤 تم النقر على زر دخول المالك');
            showOwnerLogin();
        }

        function handleBackButton() {
            console.log('⬅️ تم النقر على زر العودة');
            showHome();
        }

        function handleRefreshButton() {
            console.log('🔄 تم النقر على زر التحديث');
            loadDisplays();
        }

        // إضافة وظائف عامة للنقر المباشر (fallback)
        window.setLanguage = setLanguage;
        window.showDisplays = showDisplays;
        window.showHome = showHome;
        window.showOwnerLogin = showOwnerLogin;
        window.loadDisplays = loadDisplays;
        window.selectDisplay = selectDisplay;
    </script>
</body>
</html>
