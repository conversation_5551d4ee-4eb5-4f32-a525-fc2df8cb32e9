import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { displayService } from '../services/api';
import { sanitizeText, getFromLocalStorage } from '../utils/helpers';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Card = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const Title = styled.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
`;

const DisplayInfo = styled.div`
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 30px;
  text-align: center;
`;

const DisplayNumber = styled.div`
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10px;
`;

const PriceInfo = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 30px;
`;

const InfoCard = styled.div`
  background: #e3f2fd;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
`;

const InfoLabel = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
`;

const InfoValue = styled.div`
  font-size: 1.3rem;
  font-weight: bold;
  color: #1976d2;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
`;

const NameInput = styled.input`
  padding: 20px;
  border: 3px solid #ddd;
  border-radius: 15px;
  font-size: 1.3rem;
  text-align: center;
  transition: border-color 0.3s ease;
  font-weight: 600;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const KeyboardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  gap: 8px;
  margin-top: 20px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
`;

const KeyButton = styled.button`
  padding: 12px 8px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f8f9fa;
    border-color: #667eea;
  }

  &:active {
    background: #667eea;
    color: white;
  }
`;

const LanguageToggle = styled.button`
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 10px;
  align-self: center;

  &:hover {
    background: #5a6268;
  }
`;

const Button = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${props => props.disabled ? 0.6 : 1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`;

const BackButton = styled.button`
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #667eea;
    color: white;
  }
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`;

const NameInputPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [customerName, setCustomerName] = useState('');
  const [keyboardLanguage, setKeyboardLanguage] = useState('ar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [settings, setSettings] = useState({
    displayPrice: 50,
    displayDuration: 300
  });

  const isRTL = i18n.language === 'ar';
  const selectedDisplay = location.state?.selectedDisplay;
  const customer = location.state?.customer;

  // لوحة المفاتيح العربية
  const arabicKeys = [
    ['ض', 'ص', 'ث', 'ق', 'ف', 'غ', 'ع', 'ه', 'خ', 'ح', 'ج', 'د'],
    ['ش', 'س', 'ي', 'ب', 'ل', 'ا', 'ت', 'ن', 'م', 'ك', 'ط'],
    ['ئ', 'ء', 'ؤ', 'ر', 'لا', 'ى', 'ة', 'و', 'ز', 'ظ'],
    ['مسافة', 'حذف']
  ];

  // لوحة المفاتيح الإنجليزية
  const englishKeys = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
    ['Space', 'Delete']
  ];

  useEffect(() => {
    // تحميل الإعدادات من localStorage أو استخدام القيم الافتراضية
    const savedSettings = getFromLocalStorage('appSettings');
    if (savedSettings) {
      setSettings({
        displayPrice: savedSettings.display_price || 50,
        displayDuration: savedSettings.display_duration || 300
      });
    }

    // التحقق من وجود البيانات المطلوبة
    if (!selectedDisplay || !customer) {
      navigate('/select-display');
    }
  }, [selectedDisplay, customer, navigate]);

  const handleKeyPress = (key) => {
    if (key === 'مسافة' || key === 'Space') {
      setCustomerName(prev => prev + ' ');
    } else if (key === 'حذف' || key === 'Delete') {
      setCustomerName(prev => prev.slice(0, -1));
    } else {
      setCustomerName(prev => prev + key);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const trimmedName = customerName.trim();
    if (!trimmedName) {
      setError(t('enterName'));
      return;
    }

    if (trimmedName.length < 2) {
      setError(isRTL ? 'الاسم يجب أن يكون حرفين على الأقل' : 'Name must be at least 2 characters');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // حجز الشاشة
      const response = await displayService.reserveDisplay(selectedDisplay.id, sanitizeText(trimmedName));
      
      if (response.success) {
        // الانتقال لصفحة الدفع
        navigate('/payment', {
          state: {
            selectedDisplay,
            customer,
            customerName: sanitizeText(trimmedName),
            amount: settings.displayPrice,
            duration: settings.displayDuration
          }
        });
      } else {
        setError(response.message || 'فشل في حجز الشاشة');
      }
    } catch (error) {
      setError('خطأ في الشبكة');
      console.error('Error reserving display:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/select-display');
  };

  if (!selectedDisplay || !customer) {
    return null;
  }

  return (
    <Container isRTL={isRTL}>
      <Card>
        <Header>
          <Title>{t('enterName')}</Title>
        </Header>

        <DisplayInfo>
          <DisplayNumber>
            {t('displayNumber', { number: selectedDisplay.displayNumber })}
          </DisplayNumber>
          <div>{selectedDisplay.name}</div>
        </DisplayInfo>

        <PriceInfo>
          <InfoCard>
            <InfoLabel>{t('displayPrice', { price: '' })}</InfoLabel>
            <InfoValue>{settings.displayPrice} {isRTL ? 'ريال' : 'SAR'}</InfoValue>
          </InfoCard>
          <InfoCard>
            <InfoLabel>{t('displayDuration', { duration: '' })}</InfoLabel>
            <InfoValue>{Math.floor(settings.displayDuration / 60)} {t('minutes')}</InfoValue>
          </InfoCard>
        </PriceInfo>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <Form onSubmit={handleSubmit}>
          <InputGroup>
            <Label>{t('customerName')}</Label>
            <NameInput
              type="text"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder={t('enterName')}
              maxLength="50"
            />
          </InputGroup>

          <LanguageToggle
            type="button"
            onClick={() => setKeyboardLanguage(prev => prev === 'ar' ? 'en' : 'ar')}
          >
            {keyboardLanguage === 'ar' ? 'English' : 'العربية'}
          </LanguageToggle>

          <KeyboardContainer>
            {(keyboardLanguage === 'ar' ? arabicKeys : englishKeys).flat().map((key, index) => (
              <KeyButton
                key={index}
                type="button"
                onClick={() => handleKeyPress(key)}
                style={{
                  gridColumn: (key === 'مسافة' || key === 'Space') ? 'span 6' : 
                             (key === 'حذف' || key === 'Delete') ? 'span 3' : 'span 1'
                }}
              >
                {key}
              </KeyButton>
            ))}
          </KeyboardContainer>

          <Button type="submit" disabled={loading || !customerName.trim()}>
            {loading ? t('processing') : t('proceedToPayment')}
          </Button>
        </Form>

        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <BackButton onClick={handleBack}>
            {t('back')}
          </BackButton>
        </div>
      </Card>
    </Container>
  );
};

export default NameInputPage;
