{"ast": null, "code": "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n  let str = '';\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n  return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n  let qry = {};\n  let pairs = qs.split('&');\n  for (let i = 0, l = pairs.length; i < l; i++) {\n    let pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n}", "map": {"version": 3, "names": ["encode", "obj", "str", "i", "hasOwnProperty", "length", "encodeURIComponent", "decode", "qs", "qry", "pairs", "split", "l", "pair", "decodeURIComponent"], "sources": ["D:/برمجة/tste 1/node_modules/engine.io-client/build/esm/contrib/parseqs.js"], "sourcesContent": ["// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAACC,GAAG,EAAE;EACxB,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,CAAC,IAAIF,GAAG,EAAE;IACf,IAAIA,GAAG,CAACG,cAAc,CAACD,CAAC,CAAC,EAAE;MACvB,IAAID,GAAG,CAACG,MAAM,EACVH,GAAG,IAAI,GAAG;MACdA,GAAG,IAAII,kBAAkB,CAACH,CAAC,CAAC,GAAG,GAAG,GAAGG,kBAAkB,CAACL,GAAG,CAACE,CAAC,CAAC,CAAC;IACnE;EACJ;EACA,OAAOD,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,MAAMA,CAACC,EAAE,EAAE;EACvB,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAGF,EAAE,CAACG,KAAK,CAAC,GAAG,CAAC;EACzB,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAES,CAAC,GAAGF,KAAK,CAACL,MAAM,EAAEF,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAE;IAC1C,IAAIU,IAAI,GAAGH,KAAK,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,GAAG,CAAC;IAC9BF,GAAG,CAACK,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;EAClE;EACA,OAAOJ,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}