{"ast": null, "code": "// تنسيق رقم الهاتف السعودي\nexport const formatSaudiPhoneNumber = phoneNumber => {\n  // إزالة المسافات والرموز\n  const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)]/g, '');\n\n  // إزالة الرمز الدولي إذا كان موجوداً\n  let formattedNumber = cleanNumber;\n  if (formattedNumber.startsWith('+966')) {\n    formattedNumber = formattedNumber.substring(4);\n  } else if (formattedNumber.startsWith('966')) {\n    formattedNumber = formattedNumber.substring(3);\n  } else if (formattedNumber.startsWith('0')) {\n    formattedNumber = formattedNumber.substring(1);\n  }\n\n  // إضافة الرمز الدولي\n  return `+966${formattedNumber}`;\n};\n\n// التحقق من صحة رقم الهاتف السعودي\nexport const validateSaudiPhoneNumber = phoneNumber => {\n  const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)]/g, '');\n  const saudiPattern = /^(\\+966|966|0)?[5][0-9]{8}$/;\n  return saudiPattern.test(cleanNumber);\n};\n\n// التحقق من صحة البريد الإلكتروني\nexport const validateEmail = email => {\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\n\n// تنسيق الوقت المتبقي\nexport const formatTimeRemaining = seconds => {\n  if (seconds <= 0) return '00:00';\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor(seconds % 3600 / 60);\n  const remainingSeconds = seconds % 60;\n  if (hours > 0) {\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n};\n\n// تنسيق المبلغ\nexport const formatCurrency = (amount, currency = 'SAR', locale = 'ar-SA') => {\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 2\n  }).format(amount);\n};\n\n// تنسيق التاريخ\nexport const formatDate = (date, locale = 'ar-SA') => {\n  const options = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n    timeZone: 'Asia/Riyadh'\n  };\n  return new Intl.DateTimeFormat(locale, options).format(new Date(date));\n};\n\n// حساب الوقت المتبقي من تاريخ الانتهاء\nexport const calculateTimeRemaining = endTime => {\n  const now = new Date();\n  const end = new Date(endTime);\n  const diff = end - now;\n  if (diff <= 0) {\n    return {\n      expired: true,\n      remaining: 0\n    };\n  }\n  return {\n    expired: false,\n    remaining: Math.floor(diff / 1000),\n    minutes: Math.floor(diff / (1000 * 60)),\n    seconds: Math.floor(diff % (1000 * 60) / 1000)\n  };\n};\n\n// تحويل الثواني إلى دقائق\nexport const secondsToMinutes = seconds => {\n  return Math.floor(seconds / 60);\n};\n\n// تحويل الدقائق إلى ثواني\nexport const minutesToSeconds = minutes => {\n  return minutes * 60;\n};\n\n// تنظيف النص من الأحرف الخاصة\nexport const sanitizeText = text => {\n  return text.replace(/[<>\\\"'&]/g, '');\n};\n\n// حفظ البيانات في localStorage\nexport const saveToLocalStorage = (key, data) => {\n  try {\n    localStorage.setItem(key, JSON.stringify(data));\n    return true;\n  } catch (error) {\n    console.error('Error saving to localStorage:', error);\n    return false;\n  }\n};\n\n// استرجاع البيانات من localStorage\nexport const getFromLocalStorage = (key, defaultValue = null) => {\n  try {\n    const item = localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error('Error reading from localStorage:', error);\n    return defaultValue;\n  }\n};\n\n// إزالة البيانات من localStorage\nexport const removeFromLocalStorage = key => {\n  try {\n    localStorage.removeItem(key);\n    return true;\n  } catch (error) {\n    console.error('Error removing from localStorage:', error);\n    return false;\n  }\n};\n\n// تأخير التنفيذ\nexport const delay = ms => {\n  return new Promise(resolve => setTimeout(resolve, ms));\n};\n\n// إنشاء معرف فريد\nexport const generateUniqueId = () => {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\n\n// التحقق من كون الجهاز محمول\nexport const isMobile = () => {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n};\n\n// التحقق من كون الجهاز tablet\nexport const isTablet = () => {\n  return /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;\n};\n\n// الحصول على حجم الشاشة\nexport const getScreenSize = () => {\n  return {\n    width: window.innerWidth,\n    height: window.innerHeight\n  };\n};\n\n// تحويل الأرقام إلى العربية\nexport const toArabicNumbers = str => {\n  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];\n  return str.toString().replace(/[0-9]/g, digit => arabicNumbers[parseInt(digit)]);\n};\n\n// تحويل الأرقام إلى الإنجليزية\nexport const toEnglishNumbers = str => {\n  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];\n  let result = str.toString();\n  arabicNumbers.forEach((arabicNum, index) => {\n    result = result.replace(new RegExp(arabicNum, 'g'), index.toString());\n  });\n  return result;\n};\n\n// نسخ النص إلى الحافظة\nexport const copyToClipboard = async text => {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (error) {\n    // Fallback للمتصفحات القديمة\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (fallbackError) {\n      document.body.removeChild(textArea);\n      console.error('Failed to copy text:', fallbackError);\n      return false;\n    }\n  }\n};\n\n// إظهار إشعار\nexport const showNotification = (title, options = {}) => {\n  if ('Notification' in window && Notification.permission === 'granted') {\n    return new Notification(title, options);\n  } else if ('Notification' in window && Notification.permission !== 'denied') {\n    Notification.requestPermission().then(permission => {\n      if (permission === 'granted') {\n        return new Notification(title, options);\n      }\n    });\n  }\n  return null;\n};\n\n// طلب إذن الإشعارات\nexport const requestNotificationPermission = async () => {\n  if ('Notification' in window) {\n    const permission = await Notification.requestPermission();\n    return permission === 'granted';\n  }\n  return false;\n};\n\n// التحقق من الاتصال بالإنترنت\nexport const isOnline = () => {\n  return navigator.onLine;\n};\n\n// مراقبة حالة الاتصال\nexport const watchOnlineStatus = callback => {\n  const handleOnline = () => callback(true);\n  const handleOffline = () => callback(false);\n  window.addEventListener('online', handleOnline);\n  window.addEventListener('offline', handleOffline);\n\n  // إرجاع دالة لإلغاء المراقبة\n  return () => {\n    window.removeEventListener('online', handleOnline);\n    window.removeEventListener('offline', handleOffline);\n  };\n};", "map": {"version": 3, "names": ["formatSaudiPhoneNumber", "phoneNumber", "cleanNumber", "replace", "formattedNumber", "startsWith", "substring", "validateSaudiPhoneNumber", "saudiPattern", "test", "validateEmail", "email", "emailPattern", "formatTimeRemaining", "seconds", "hours", "Math", "floor", "minutes", "remainingSeconds", "toString", "padStart", "formatCurrency", "amount", "currency", "locale", "Intl", "NumberFormat", "style", "minimumFractionDigits", "format", "formatDate", "date", "options", "year", "month", "day", "hour", "minute", "timeZone", "DateTimeFormat", "Date", "calculateTimeRemaining", "endTime", "now", "end", "diff", "expired", "remaining", "secondsToMinutes", "minutesToSeconds", "sanitizeText", "text", "saveToLocalStorage", "key", "data", "localStorage", "setItem", "JSON", "stringify", "error", "console", "getFromLocalStorage", "defaultValue", "item", "getItem", "parse", "removeFromLocalStorage", "removeItem", "delay", "ms", "Promise", "resolve", "setTimeout", "generateUniqueId", "random", "substr", "isMobile", "navigator", "userAgent", "isTablet", "window", "innerWidth", "getScreenSize", "width", "height", "innerHeight", "toArabicNumbers", "str", "arabicNumbers", "digit", "parseInt", "toEnglishNumbers", "result", "for<PERSON>ach", "arabicNum", "index", "RegExp", "copyToClipboard", "clipboard", "writeText", "textArea", "document", "createElement", "value", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "fallback<PERSON><PERSON>r", "showNotification", "title", "Notification", "permission", "requestPermission", "then", "requestNotificationPermission", "isOnline", "onLine", "watchOnlineStatus", "callback", "handleOnline", "handleOffline", "addEventListener", "removeEventListener"], "sources": ["D:/برمجة/tste 1/client/src/utils/helpers.js"], "sourcesContent": ["// تنسيق رقم الهاتف السعودي\nexport const formatSaudiPhoneNumber = (phoneNumber) => {\n  // إزالة المسافات والرموز\n  const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)]/g, '');\n  \n  // إزالة الرمز الدولي إذا كان موجوداً\n  let formattedNumber = cleanNumber;\n  if (formattedNumber.startsWith('+966')) {\n    formattedNumber = formattedNumber.substring(4);\n  } else if (formattedNumber.startsWith('966')) {\n    formattedNumber = formattedNumber.substring(3);\n  } else if (formattedNumber.startsWith('0')) {\n    formattedNumber = formattedNumber.substring(1);\n  }\n  \n  // إضافة الرمز الدولي\n  return `+966${formattedNumber}`;\n};\n\n// التحقق من صحة رقم الهاتف السعودي\nexport const validateSaudiPhoneNumber = (phoneNumber) => {\n  const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)]/g, '');\n  const saudiPattern = /^(\\+966|966|0)?[5][0-9]{8}$/;\n  return saudiPattern.test(cleanNumber);\n};\n\n// التحقق من صحة البريد الإلكتروني\nexport const validateEmail = (email) => {\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\n\n// تنسيق الوقت المتبقي\nexport const formatTimeRemaining = (seconds) => {\n  if (seconds <= 0) return '00:00';\n  \n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const remainingSeconds = seconds % 60;\n  \n  if (hours > 0) {\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n};\n\n// تنسيق المبلغ\nexport const formatCurrency = (amount, currency = 'SAR', locale = 'ar-SA') => {\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 2\n  }).format(amount);\n};\n\n// تنسيق التاريخ\nexport const formatDate = (date, locale = 'ar-SA') => {\n  const options = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n    timeZone: 'Asia/Riyadh'\n  };\n  \n  return new Intl.DateTimeFormat(locale, options).format(new Date(date));\n};\n\n// حساب الوقت المتبقي من تاريخ الانتهاء\nexport const calculateTimeRemaining = (endTime) => {\n  const now = new Date();\n  const end = new Date(endTime);\n  const diff = end - now;\n  \n  if (diff <= 0) {\n    return { expired: true, remaining: 0 };\n  }\n  \n  return {\n    expired: false,\n    remaining: Math.floor(diff / 1000),\n    minutes: Math.floor(diff / (1000 * 60)),\n    seconds: Math.floor((diff % (1000 * 60)) / 1000)\n  };\n};\n\n// تحويل الثواني إلى دقائق\nexport const secondsToMinutes = (seconds) => {\n  return Math.floor(seconds / 60);\n};\n\n// تحويل الدقائق إلى ثواني\nexport const minutesToSeconds = (minutes) => {\n  return minutes * 60;\n};\n\n// تنظيف النص من الأحرف الخاصة\nexport const sanitizeText = (text) => {\n  return text.replace(/[<>\\\"'&]/g, '');\n};\n\n// حفظ البيانات في localStorage\nexport const saveToLocalStorage = (key, data) => {\n  try {\n    localStorage.setItem(key, JSON.stringify(data));\n    return true;\n  } catch (error) {\n    console.error('Error saving to localStorage:', error);\n    return false;\n  }\n};\n\n// استرجاع البيانات من localStorage\nexport const getFromLocalStorage = (key, defaultValue = null) => {\n  try {\n    const item = localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error('Error reading from localStorage:', error);\n    return defaultValue;\n  }\n};\n\n// إزالة البيانات من localStorage\nexport const removeFromLocalStorage = (key) => {\n  try {\n    localStorage.removeItem(key);\n    return true;\n  } catch (error) {\n    console.error('Error removing from localStorage:', error);\n    return false;\n  }\n};\n\n// تأخير التنفيذ\nexport const delay = (ms) => {\n  return new Promise(resolve => setTimeout(resolve, ms));\n};\n\n// إنشاء معرف فريد\nexport const generateUniqueId = () => {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\n\n// التحقق من كون الجهاز محمول\nexport const isMobile = () => {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n};\n\n// التحقق من كون الجهاز tablet\nexport const isTablet = () => {\n  return /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;\n};\n\n// الحصول على حجم الشاشة\nexport const getScreenSize = () => {\n  return {\n    width: window.innerWidth,\n    height: window.innerHeight\n  };\n};\n\n// تحويل الأرقام إلى العربية\nexport const toArabicNumbers = (str) => {\n  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];\n  return str.toString().replace(/[0-9]/g, (digit) => arabicNumbers[parseInt(digit)]);\n};\n\n// تحويل الأرقام إلى الإنجليزية\nexport const toEnglishNumbers = (str) => {\n  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];\n  let result = str.toString();\n  arabicNumbers.forEach((arabicNum, index) => {\n    result = result.replace(new RegExp(arabicNum, 'g'), index.toString());\n  });\n  return result;\n};\n\n// نسخ النص إلى الحافظة\nexport const copyToClipboard = async (text) => {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (error) {\n    // Fallback للمتصفحات القديمة\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (fallbackError) {\n      document.body.removeChild(textArea);\n      console.error('Failed to copy text:', fallbackError);\n      return false;\n    }\n  }\n};\n\n// إظهار إشعار\nexport const showNotification = (title, options = {}) => {\n  if ('Notification' in window && Notification.permission === 'granted') {\n    return new Notification(title, options);\n  } else if ('Notification' in window && Notification.permission !== 'denied') {\n    Notification.requestPermission().then(permission => {\n      if (permission === 'granted') {\n        return new Notification(title, options);\n      }\n    });\n  }\n  return null;\n};\n\n// طلب إذن الإشعارات\nexport const requestNotificationPermission = async () => {\n  if ('Notification' in window) {\n    const permission = await Notification.requestPermission();\n    return permission === 'granted';\n  }\n  return false;\n};\n\n// التحقق من الاتصال بالإنترنت\nexport const isOnline = () => {\n  return navigator.onLine;\n};\n\n// مراقبة حالة الاتصال\nexport const watchOnlineStatus = (callback) => {\n  const handleOnline = () => callback(true);\n  const handleOffline = () => callback(false);\n  \n  window.addEventListener('online', handleOnline);\n  window.addEventListener('offline', handleOffline);\n  \n  // إرجاع دالة لإلغاء المراقبة\n  return () => {\n    window.removeEventListener('online', handleOnline);\n    window.removeEventListener('offline', handleOffline);\n  };\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,sBAAsB,GAAIC,WAAW,IAAK;EACrD;EACA,MAAMC,WAAW,GAAGD,WAAW,CAACE,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;;EAE1D;EACA,IAAIC,eAAe,GAAGF,WAAW;EACjC,IAAIE,eAAe,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IACtCD,eAAe,GAAGA,eAAe,CAACE,SAAS,CAAC,CAAC,CAAC;EAChD,CAAC,MAAM,IAAIF,eAAe,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE;IAC5CD,eAAe,GAAGA,eAAe,CAACE,SAAS,CAAC,CAAC,CAAC;EAChD,CAAC,MAAM,IAAIF,eAAe,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;IAC1CD,eAAe,GAAGA,eAAe,CAACE,SAAS,CAAC,CAAC,CAAC;EAChD;;EAEA;EACA,OAAO,OAAOF,eAAe,EAAE;AACjC,CAAC;;AAED;AACA,OAAO,MAAMG,wBAAwB,GAAIN,WAAW,IAAK;EACvD,MAAMC,WAAW,GAAGD,WAAW,CAACE,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1D,MAAMK,YAAY,GAAG,6BAA6B;EAClD,OAAOA,YAAY,CAACC,IAAI,CAACP,WAAW,CAAC;AACvC,CAAC;;AAED;AACA,OAAO,MAAMQ,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,YAAY,GAAG,4BAA4B;EACjD,OAAOA,YAAY,CAACH,IAAI,CAACE,KAAK,CAAC;AACjC,CAAC;;AAED;AACA,OAAO,MAAME,mBAAmB,GAAIC,OAAO,IAAK;EAC9C,IAAIA,OAAO,IAAI,CAAC,EAAE,OAAO,OAAO;EAEhC,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,IAAI,CAAC;EACxC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;EACjD,MAAMK,gBAAgB,GAAGL,OAAO,GAAG,EAAE;EAErC,IAAIC,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,GAAGA,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtI,CAAC,MAAM;IACL,OAAO,GAAGH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACjG;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,QAAQ,GAAG,KAAK,EAAEC,MAAM,GAAG,OAAO,KAAK;EAC5E,OAAO,IAAIC,IAAI,CAACC,YAAY,CAACF,MAAM,EAAE;IACnCG,KAAK,EAAE,UAAU;IACjBJ,QAAQ,EAAEA,QAAQ;IAClBK,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;AACnB,CAAC;;AAED;AACA,OAAO,MAAMQ,UAAU,GAAGA,CAACC,IAAI,EAAEP,MAAM,GAAG,OAAO,KAAK;EACpD,MAAMQ,OAAO,GAAG;IACdC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE;EACZ,CAAC;EAED,OAAO,IAAIb,IAAI,CAACc,cAAc,CAACf,MAAM,EAAEQ,OAAO,CAAC,CAACH,MAAM,CAAC,IAAIW,IAAI,CAACT,IAAI,CAAC,CAAC;AACxE,CAAC;;AAED;AACA,OAAO,MAAMU,sBAAsB,GAAIC,OAAO,IAAK;EACjD,MAAMC,GAAG,GAAG,IAAIH,IAAI,CAAC,CAAC;EACtB,MAAMI,GAAG,GAAG,IAAIJ,IAAI,CAACE,OAAO,CAAC;EAC7B,MAAMG,IAAI,GAAGD,GAAG,GAAGD,GAAG;EAEtB,IAAIE,IAAI,IAAI,CAAC,EAAE;IACb,OAAO;MAAEC,OAAO,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAE,CAAC;EACxC;EAEA,OAAO;IACLD,OAAO,EAAE,KAAK;IACdC,SAAS,EAAEhC,IAAI,CAACC,KAAK,CAAC6B,IAAI,GAAG,IAAI,CAAC;IAClC5B,OAAO,EAAEF,IAAI,CAACC,KAAK,CAAC6B,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACvChC,OAAO,EAAEE,IAAI,CAACC,KAAK,CAAE6B,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI;EACjD,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMG,gBAAgB,GAAInC,OAAO,IAAK;EAC3C,OAAOE,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;AACjC,CAAC;;AAED;AACA,OAAO,MAAMoC,gBAAgB,GAAIhC,OAAO,IAAK;EAC3C,OAAOA,OAAO,GAAG,EAAE;AACrB,CAAC;;AAED;AACA,OAAO,MAAMiC,YAAY,GAAIC,IAAI,IAAK;EACpC,OAAOA,IAAI,CAACjD,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;AACtC,CAAC;;AAED;AACA,OAAO,MAAMkD,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAK;EAC/C,IAAI;IACFC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEI,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;IAC/C,OAAO,IAAI;EACb,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAME,mBAAmB,GAAGA,CAACR,GAAG,EAAES,YAAY,GAAG,IAAI,KAAK;EAC/D,IAAI;IACF,MAAMC,IAAI,GAAGR,YAAY,CAACS,OAAO,CAACX,GAAG,CAAC;IACtC,OAAOU,IAAI,GAAGN,IAAI,CAACQ,KAAK,CAACF,IAAI,CAAC,GAAGD,YAAY;EAC/C,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,OAAOG,YAAY;EACrB;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,sBAAsB,GAAIb,GAAG,IAAK;EAC7C,IAAI;IACFE,YAAY,CAACY,UAAU,CAACd,GAAG,CAAC;IAC5B,OAAO,IAAI;EACb,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,KAAK,GAAIC,EAAE,IAAK;EAC3B,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;AACxD,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;EACpC,OAAOjC,IAAI,CAACG,GAAG,CAAC,CAAC,CAACxB,QAAQ,CAAC,EAAE,CAAC,GAAGJ,IAAI,CAAC2D,MAAM,CAAC,CAAC,CAACvD,QAAQ,CAAC,EAAE,CAAC,CAACwD,MAAM,CAAC,CAAC,CAAC;AACvE,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC5B,OAAO,gEAAgE,CAACpE,IAAI,CAACqE,SAAS,CAACC,SAAS,CAAC;AACnG,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC5B,OAAO,eAAe,CAACvE,IAAI,CAACqE,SAAS,CAACC,SAAS,CAAC,IAAIE,MAAM,CAACC,UAAU,IAAI,GAAG;AAC9E,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EACjC,OAAO;IACLC,KAAK,EAAEH,MAAM,CAACC,UAAU;IACxBG,MAAM,EAAEJ,MAAM,CAACK;EACjB,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAIC,GAAG,IAAK;EACtC,MAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxE,OAAOD,GAAG,CAACpE,QAAQ,CAAC,CAAC,CAACjB,OAAO,CAAC,QAAQ,EAAGuF,KAAK,IAAKD,aAAa,CAACE,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC;AACpF,CAAC;;AAED;AACA,OAAO,MAAME,gBAAgB,GAAIJ,GAAG,IAAK;EACvC,MAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxE,IAAII,MAAM,GAAGL,GAAG,CAACpE,QAAQ,CAAC,CAAC;EAC3BqE,aAAa,CAACK,OAAO,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;IAC1CH,MAAM,GAAGA,MAAM,CAAC1F,OAAO,CAAC,IAAI8F,MAAM,CAACF,SAAS,EAAE,GAAG,CAAC,EAAEC,KAAK,CAAC5E,QAAQ,CAAC,CAAC,CAAC;EACvE,CAAC,CAAC;EACF,OAAOyE,MAAM;AACf,CAAC;;AAED;AACA,OAAO,MAAMK,eAAe,GAAG,MAAO9C,IAAI,IAAK;EAC7C,IAAI;IACF,MAAM0B,SAAS,CAACqB,SAAS,CAACC,SAAS,CAAChD,IAAI,CAAC;IACzC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd;IACA,MAAMyC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IACnDF,QAAQ,CAACG,KAAK,GAAGpD,IAAI;IACrBkD,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;IACnCA,QAAQ,CAACM,KAAK,CAAC,CAAC;IAChBN,QAAQ,CAACO,MAAM,CAAC,CAAC;IACjB,IAAI;MACFN,QAAQ,CAACO,WAAW,CAAC,MAAM,CAAC;MAC5BP,QAAQ,CAACG,IAAI,CAACK,WAAW,CAACT,QAAQ,CAAC;MACnC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOU,aAAa,EAAE;MACtBT,QAAQ,CAACG,IAAI,CAACK,WAAW,CAACT,QAAQ,CAAC;MACnCxC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEmD,aAAa,CAAC;MACpD,OAAO,KAAK;IACd;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEhF,OAAO,GAAG,CAAC,CAAC,KAAK;EACvD,IAAI,cAAc,IAAIgD,MAAM,IAAIiC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;IACrE,OAAO,IAAID,YAAY,CAACD,KAAK,EAAEhF,OAAO,CAAC;EACzC,CAAC,MAAM,IAAI,cAAc,IAAIgD,MAAM,IAAIiC,YAAY,CAACC,UAAU,KAAK,QAAQ,EAAE;IAC3ED,YAAY,CAACE,iBAAiB,CAAC,CAAC,CAACC,IAAI,CAACF,UAAU,IAAI;MAClD,IAAIA,UAAU,KAAK,SAAS,EAAE;QAC5B,OAAO,IAAID,YAAY,CAACD,KAAK,EAAEhF,OAAO,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMqF,6BAA6B,GAAG,MAAAA,CAAA,KAAY;EACvD,IAAI,cAAc,IAAIrC,MAAM,EAAE;IAC5B,MAAMkC,UAAU,GAAG,MAAMD,YAAY,CAACE,iBAAiB,CAAC,CAAC;IACzD,OAAOD,UAAU,KAAK,SAAS;EACjC;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA,OAAO,MAAMI,QAAQ,GAAGA,CAAA,KAAM;EAC5B,OAAOzC,SAAS,CAAC0C,MAAM;AACzB,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EAC7C,MAAMC,YAAY,GAAGA,CAAA,KAAMD,QAAQ,CAAC,IAAI,CAAC;EACzC,MAAME,aAAa,GAAGA,CAAA,KAAMF,QAAQ,CAAC,KAAK,CAAC;EAE3CzC,MAAM,CAAC4C,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EAC/C1C,MAAM,CAAC4C,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;;EAEjD;EACA,OAAO,MAAM;IACX3C,MAAM,CAAC6C,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAClD1C,MAAM,CAAC6C,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;EACtD,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}