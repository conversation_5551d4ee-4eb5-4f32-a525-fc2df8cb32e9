<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - نظام إدارة الشاشات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            text-decoration: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #28a745;
            border-color: #28a745;
        }
        
        .btn-secondary {
            background: #6c757d;
            border-color: #6c757d;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            min-height: 50px;
        }
        
        .hidden {
            display: none;
        }
        
        .page {
            min-height: 400px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار بسيط لنظام إدارة الشاشات</h1>
        
        <!-- الصفحة الرئيسية -->
        <div id="home-page" class="page">
            <h2>الصفحة الرئيسية</h2>
            
            <div>
                <h3>اختبار تغيير اللغة:</h3>
                <button class="btn" onclick="testLanguage('ar')">🇸🇦 العربية</button>
                <button class="btn" onclick="testLanguage('en')">🇺🇸 English</button>
            </div>
            
            <div>
                <h3>اختبار التنقل:</h3>
                <button class="btn btn-primary" onclick="testNavigation('displays')">🖥️ صفحة الشاشات</button>
                <button class="btn btn-secondary" onclick="testOwnerLogin()">👤 دخول المالك</button>
            </div>
        </div>
        
        <!-- صفحة الشاشات -->
        <div id="displays-page" class="page hidden">
            <h2>صفحة الشاشات</h2>
            
            <button class="btn" onclick="testNavigation('home')">⬅️ العودة للرئيسية</button>
            
            <div>
                <h3>الشاشات المتاحة:</h3>
                <button class="btn btn-primary" onclick="testDisplaySelect(1)">شاشة 1 - متاحة</button>
                <button class="btn" onclick="testDisplaySelect(2)" style="background: #dc3545; border-color: #dc3545;">شاشة 2 - مشغولة</button>
                <button class="btn btn-primary" onclick="testDisplaySelect(3)">شاشة 3 - متاحة</button>
            </div>
        </div>
        
        <!-- منطقة النتائج -->
        <div class="result" id="result">
            <strong>النتائج ستظهر هنا...</strong>
        </div>
        
        <!-- معلومات التشخيص -->
        <div class="result">
            <h3>🔍 معلومات التشخيص:</h3>
            <div id="diagnostics">
                <p>جاري التحميل...</p>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'ar';
        let testResults = [];

        // تحديث منطقة النتائج
        function updateResult(message, type = 'info') {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            
            result.innerHTML = `<strong>${icon} ${timestamp}</strong><br>${message}`;
            testResults.push(`${timestamp}: ${message}`);
            
            console.log(`${icon} ${message}`);
        }

        // اختبار تغيير اللغة
        function testLanguage(lang) {
            currentLanguage = lang;
            const langName = lang === 'ar' ? 'العربية' : 'English';
            updateResult(`تم تغيير اللغة إلى: ${langName}`, 'success');
            
            // تحديث اتجاه الصفحة
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = lang;
        }

        // اختبار التنقل
        function testNavigation(page) {
            const homePage = document.getElementById('home-page');
            const displaysPage = document.getElementById('displays-page');
            
            if (page === 'displays') {
                homePage.classList.add('hidden');
                displaysPage.classList.remove('hidden');
                updateResult('تم الانتقال إلى صفحة الشاشات', 'success');
            } else if (page === 'home') {
                displaysPage.classList.add('hidden');
                homePage.classList.remove('hidden');
                updateResult('تم الانتقال إلى الصفحة الرئيسية', 'success');
            }
        }

        // اختبار اختيار الشاشة
        function testDisplaySelect(displayId) {
            if (displayId === 2) {
                updateResult('❌ الشاشة 2 مشغولة - لا يمكن اختيارها', 'error');
            } else {
                updateResult(`✅ تم اختيار الشاشة ${displayId} بنجاح`, 'success');
                setTimeout(() => {
                    updateResult('🔄 سيتم الانتقال لصفحة تسجيل الدخول...', 'info');
                }, 1000);
            }
        }

        // اختبار دخول المالك
        function testOwnerLogin() {
            const password = prompt('أدخل كلمة المرور (admin123):');
            if (password === 'admin123') {
                updateResult('✅ تم تسجيل دخول المالك بنجاح', 'success');
            } else if (password === null) {
                updateResult('ℹ️ تم إلغاء تسجيل الدخول', 'info');
            } else {
                updateResult('❌ كلمة المرور غير صحيحة', 'error');
            }
        }

        // تشخيص النظام
        function runDiagnostics() {
            const diagnostics = document.getElementById('diagnostics');
            let info = [];
            
            // معلومات المتصفح
            info.push(`المتصفح: ${navigator.userAgent.split(' ').pop()}`);
            info.push(`اللغة: ${navigator.language}`);
            info.push(`الشاشة: ${screen.width}x${screen.height}`);
            
            // معلومات الصفحة
            info.push(`العناصر الموجودة:`);
            info.push(`- الصفحة الرئيسية: ${document.getElementById('home-page') ? '✅' : '❌'}`);
            info.push(`- صفحة الشاشات: ${document.getElementById('displays-page') ? '✅' : '❌'}`);
            info.push(`- منطقة النتائج: ${document.getElementById('result') ? '✅' : '❌'}`);
            
            // معلومات JavaScript
            info.push(`JavaScript مفعل: ✅`);
            info.push(`Console متاح: ${typeof console !== 'undefined' ? '✅' : '❌'}`);
            
            diagnostics.innerHTML = info.map(item => `<p style="margin: 5px 0; font-size: 14px;">${item}</p>`).join('');
        }

        // تشغيل التشخيص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateResult('🚀 تم تحميل صفحة الاختبار بنجاح', 'success');
            runDiagnostics();
            
            // اختبار تلقائي
            setTimeout(() => {
                updateResult('🔄 جاري تشغيل الاختبارات التلقائية...', 'info');
            }, 2000);
        });

        // اختبار دوري
        setInterval(() => {
            const now = new Date();
            if (now.getSeconds() % 30 === 0) {
                updateResult(`⏰ النظام يعمل بشكل طبيعي - ${now.toLocaleTimeString()}`, 'info');
            }
        }, 1000);
    </script>
</body>
</html>
