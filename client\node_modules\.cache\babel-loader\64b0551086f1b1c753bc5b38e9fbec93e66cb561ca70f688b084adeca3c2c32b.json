{"ast": null, "code": "import React,{useState}from'react';import{useTranslation}from'react-i18next';import{useNavigate}from'react-router-dom';import styled from'styled-components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Container=styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Card=styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  max-width: 500px;\n  width: 100%;\n  margin: 20px;\n`;const Logo=styled.div`\n  width: 120px;\n  height: 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  margin: 0 auto 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48px;\n  color: white;\n  font-weight: bold;\n`;const Title=styled.h1`\n  font-size: 2.5rem;\n  color: #333;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;const Subtitle=styled.p`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 40px;\n`;const LanguageSelector=styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  margin-bottom: 30px;\n`;const LanguageButton=styled.button`\n  padding: 10px 20px;\n  border: 2px solid ${props=>props.active?'#667eea':'#ddd'};\n  background: ${props=>props.active?'#667eea':'white'};\n  color: ${props=>props.active?'white':'#333'};\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props=>props.active?'#5a6fd8':'#f8f9ff'};\n  }\n`;const StartButton=styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 40px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  border-radius: 50px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;const OwnerLoginLink=styled.button`\n  background: none;\n  border: none;\n  color: #666;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-top: 20px;\n  text-decoration: underline;\n\n  &:hover {\n    color: #333;\n  }\n`;const HomePage=()=>{const{t,i18n}=useTranslation();const navigate=useNavigate();const[currentLanguage,setCurrentLanguage]=useState(i18n.language);const isRTL=currentLanguage==='ar';const handleLanguageChange=language=>{setCurrentLanguage(language);i18n.changeLanguage(language);localStorage.setItem('language',language);};const handleStartTransaction=()=>{navigate('/select-display');};const handleOwnerLogin=()=>{navigate('/owner-login');};return/*#__PURE__*/_jsx(Container,{isRTL:isRTL,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Logo,{children:\"\\uD83D\\uDCFA\"}),/*#__PURE__*/_jsx(Title,{children:t('companyName')}),/*#__PURE__*/_jsx(Subtitle,{children:t('welcome')}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{marginBottom:'15px',color:'#666',fontSize:'1rem'},children:t('selectLanguage')}),/*#__PURE__*/_jsxs(LanguageSelector,{children:[/*#__PURE__*/_jsx(LanguageButton,{active:currentLanguage==='ar',onClick:()=>handleLanguageChange('ar'),children:\"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"}),/*#__PURE__*/_jsx(LanguageButton,{active:currentLanguage==='en',onClick:()=>handleLanguageChange('en'),children:\"English\"})]})]}),/*#__PURE__*/_jsx(StartButton,{onClick:handleStartTransaction,children:t('startTransaction')}),/*#__PURE__*/_jsx(OwnerLoginLink,{onClick:handleOwnerLogin,children:currentLanguage==='ar'?'دخول المالك':'Owner Login'})]})});};export default HomePage;", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useNavigate", "styled", "jsx", "_jsx", "jsxs", "_jsxs", "Container", "div", "props", "isRTL", "Card", "Logo", "Title", "h1", "Subtitle", "p", "LanguageSelector", "LanguageButton", "button", "active", "StartButton", "OwnerLoginLink", "HomePage", "t", "i18n", "navigate", "currentLanguage", "setCurrentLanguage", "language", "handleLanguageChange", "changeLanguage", "localStorage", "setItem", "handleStartTransaction", "handleOwnerLogin", "children", "style", "marginBottom", "color", "fontSize", "onClick"], "sources": ["D:/برمجة/tste 1/client/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  max-width: 500px;\n  width: 100%;\n  margin: 20px;\n`;\n\nconst Logo = styled.div`\n  width: 120px;\n  height: 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  margin: 0 auto 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48px;\n  color: white;\n  font-weight: bold;\n`;\n\nconst Title = styled.h1`\n  font-size: 2.5rem;\n  color: #333;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 40px;\n`;\n\nconst LanguageSelector = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  margin-bottom: 30px;\n`;\n\nconst LanguageButton = styled.button`\n  padding: 10px 20px;\n  border: 2px solid ${props => props.active ? '#667eea' : '#ddd'};\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#333'};\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props => props.active ? '#5a6fd8' : '#f8f9ff'};\n  }\n`;\n\nconst StartButton = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 40px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  border-radius: 50px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nconst OwnerLoginLink = styled.button`\n  background: none;\n  border: none;\n  color: #666;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-top: 20px;\n  text-decoration: underline;\n\n  &:hover {\n    color: #333;\n  }\n`;\n\nconst HomePage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);\n\n  const isRTL = currentLanguage === 'ar';\n\n  const handleLanguageChange = (language) => {\n    setCurrentLanguage(language);\n    i18n.changeLanguage(language);\n    localStorage.setItem('language', language);\n  };\n\n  const handleStartTransaction = () => {\n    navigate('/select-display');\n  };\n\n  const handleOwnerLogin = () => {\n    navigate('/owner-login');\n  };\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Logo>\n          📺\n        </Logo>\n        \n        <Title>{t('companyName')}</Title>\n        <Subtitle>{t('welcome')}</Subtitle>\n        \n        <div>\n          <p style={{ marginBottom: '15px', color: '#666', fontSize: '1rem' }}>\n            {t('selectLanguage')}\n          </p>\n          <LanguageSelector>\n            <LanguageButton\n              active={currentLanguage === 'ar'}\n              onClick={() => handleLanguageChange('ar')}\n            >\n              العربية\n            </LanguageButton>\n            <LanguageButton\n              active={currentLanguage === 'en'}\n              onClick={() => handleLanguageChange('en')}\n            >\n              English\n            </LanguageButton>\n          </LanguageSelector>\n        </div>\n        \n        <StartButton onClick={handleStartTransaction}>\n          {t('startTransaction')}\n        </StartButton>\n        \n        <OwnerLoginLink onClick={handleOwnerLogin}>\n          {currentLanguage === 'ar' ? 'دخول المالك' : 'Owner Login'}\n        </OwnerLoginLink>\n      </Card>\n    </Container>\n  );\n};\n\nexport default HomePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,SAAS,CAAGL,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGT,MAAM,CAACM,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,IAAI,CAAGV,MAAM,CAACM,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAK,KAAK,CAAGX,MAAM,CAACY,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGb,MAAM,CAACc,CAAC;AACzB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGf,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAU,cAAc,CAAGhB,MAAM,CAACiB,MAAM;AACpC;AACA,sBAAsBV,KAAK,EAAIA,KAAK,CAACW,MAAM,CAAG,SAAS,CAAG,MAAM;AAChE,gBAAgBX,KAAK,EAAIA,KAAK,CAACW,MAAM,CAAG,SAAS,CAAG,OAAO;AAC3D,WAAWX,KAAK,EAAIA,KAAK,CAACW,MAAM,CAAG,OAAO,CAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBX,KAAK,EAAIA,KAAK,CAACW,MAAM,CAAG,SAAS,CAAG,SAAS;AAC/D;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGnB,MAAM,CAACiB,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,cAAc,CAAGpB,MAAM,CAACiB,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGzB,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA0B,QAAQ,CAAGzB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC0B,eAAe,CAAEC,kBAAkB,CAAC,CAAG7B,QAAQ,CAAC0B,IAAI,CAACI,QAAQ,CAAC,CAErE,KAAM,CAAAnB,KAAK,CAAGiB,eAAe,GAAK,IAAI,CAEtC,KAAM,CAAAG,oBAAoB,CAAID,QAAQ,EAAK,CACzCD,kBAAkB,CAACC,QAAQ,CAAC,CAC5BJ,IAAI,CAACM,cAAc,CAACF,QAAQ,CAAC,CAC7BG,YAAY,CAACC,OAAO,CAAC,UAAU,CAAEJ,QAAQ,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAK,sBAAsB,CAAGA,CAAA,GAAM,CACnCR,QAAQ,CAAC,iBAAiB,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAS,gBAAgB,CAAGA,CAAA,GAAM,CAC7BT,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,CAED,mBACEtB,IAAA,CAACG,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAA0B,QAAA,cACtB9B,KAAA,CAACK,IAAI,EAAAyB,QAAA,eACHhC,IAAA,CAACQ,IAAI,EAAAwB,QAAA,CAAC,cAEN,CAAM,CAAC,cAEPhC,IAAA,CAACS,KAAK,EAAAuB,QAAA,CAAEZ,CAAC,CAAC,aAAa,CAAC,CAAQ,CAAC,cACjCpB,IAAA,CAACW,QAAQ,EAAAqB,QAAA,CAAEZ,CAAC,CAAC,SAAS,CAAC,CAAW,CAAC,cAEnClB,KAAA,QAAA8B,QAAA,eACEhC,IAAA,MAAGiC,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CACjEZ,CAAC,CAAC,gBAAgB,CAAC,CACnB,CAAC,cACJlB,KAAA,CAACW,gBAAgB,EAAAmB,QAAA,eACfhC,IAAA,CAACc,cAAc,EACbE,MAAM,CAAEO,eAAe,GAAK,IAAK,CACjCc,OAAO,CAAEA,CAAA,GAAMX,oBAAoB,CAAC,IAAI,CAAE,CAAAM,QAAA,CAC3C,4CAED,CAAgB,CAAC,cACjBhC,IAAA,CAACc,cAAc,EACbE,MAAM,CAAEO,eAAe,GAAK,IAAK,CACjCc,OAAO,CAAEA,CAAA,GAAMX,oBAAoB,CAAC,IAAI,CAAE,CAAAM,QAAA,CAC3C,SAED,CAAgB,CAAC,EACD,CAAC,EAChB,CAAC,cAENhC,IAAA,CAACiB,WAAW,EAACoB,OAAO,CAAEP,sBAAuB,CAAAE,QAAA,CAC1CZ,CAAC,CAAC,kBAAkB,CAAC,CACX,CAAC,cAEdpB,IAAA,CAACkB,cAAc,EAACmB,OAAO,CAAEN,gBAAiB,CAAAC,QAAA,CACvCT,eAAe,GAAK,IAAI,CAAG,aAAa,CAAG,aAAa,CAC3C,CAAC,EACb,CAAC,CACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}