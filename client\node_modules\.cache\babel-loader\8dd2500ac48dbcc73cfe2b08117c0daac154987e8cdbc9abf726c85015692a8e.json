{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\hooks\\\\useAuth.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useState, useEffect, useContext, createContext } from 'react';\nimport { authService } from '../services/api';\nimport { saveToLocalStorage, getFromLocalStorage, removeFromLocalStorage } from '../utils/helpers';\n\n// إنشاء Context للمصادقة\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Provider للمصادقة\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // التحقق من وجود token عند تحميل التطبيق\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userInfo = getFromLocalStorage('userInfo');\n        if (token && userInfo) {\n          // التحقق من صحة الرمز المميز\n          const response = await authService.verifyToken();\n          if (response.success) {\n            setUser(userInfo);\n            setIsAuthenticated(true);\n          } else {\n            // إزالة البيانات غير الصحيحة\n            logout();\n          }\n        }\n      } catch (error) {\n        console.error('Error checking auth:', error);\n        logout();\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, []);\n\n  // تسجيل الدخول\n  const login = (userData, token) => {\n    localStorage.setItem('authToken', token);\n    saveToLocalStorage('userInfo', userData);\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n\n  // تسجيل الخروج\n  const logout = () => {\n    removeFromLocalStorage('authToken');\n    removeFromLocalStorage('userInfo');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  // تحديث بيانات المستخدم\n  const updateUser = userData => {\n    saveToLocalStorage('userInfo', userData);\n    setUser(userData);\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook لاستخدام المصادقة\n_s(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Hook لإرسال OTP\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const useOTP = () => {\n  _s3();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [otpSent, setOtpSent] = useState(false);\n  const sendOTP = async (phoneNumber, email, language = 'ar') => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await authService.sendOTP(phoneNumber, email, language);\n      if (response.success) {\n        setOtpSent(true);\n        return response;\n      } else {\n        setError(response.message || 'فشل في إرسال رمز التحقق');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في الشبكة');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const verifyOTP = async (phoneNumber, otpCode) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await authService.verifyOTP(phoneNumber, otpCode);\n      if (response.success) {\n        return response;\n      } else {\n        setError(response.message || 'رمز التحقق غير صحيح');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في التحقق من الرمز');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetOTP = () => {\n    setOtpSent(false);\n    setError(null);\n  };\n  return {\n    loading,\n    error,\n    otpSent,\n    sendOTP,\n    verifyOTP,\n    resetOTP\n  };\n};\n\n// Hook لتسجيل دخول المالك\n_s3(useOTP, \"uV1uX/JInSVzD6ssnWaH8OXSh+s=\");\nexport const useOwnerAuth = () => {\n  _s4();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const {\n    login\n  } = useAuth();\n  const ownerLogin = async password => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await authService.ownerLogin(password);\n      if (response.success) {\n        login(response.data.owner, response.data.token);\n        return response;\n      } else {\n        setError(response.message || 'كلمة المرور غير صحيحة');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في تسجيل الدخول');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    loading,\n    error,\n    ownerLogin\n  };\n};\n_s4(useOwnerAuth, \"n7fnOi7/a0SIOdRqAVLOniOf5qw=\", false, function () {\n  return [useAuth];\n});\nexport default useAuth;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["useState", "useEffect", "useContext", "createContext", "authService", "saveToLocalStorage", "getFromLocalStorage", "removeFromLocalStorage", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuth", "token", "localStorage", "getItem", "userInfo", "response", "verifyToken", "success", "logout", "error", "console", "login", "userData", "setItem", "updateUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "useOTP", "_s3", "setError", "otpSent", "setOtpSent", "sendOTP", "phoneNumber", "email", "language", "message", "verifyOTP", "otpCode", "resetOTP", "useOwnerAuth", "_s4", "owner<PERSON><PERSON><PERSON>", "password", "data", "owner", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/hooks/useAuth.js"], "sourcesContent": ["import { useState, useEffect, useContext, createContext } from 'react';\nimport { authService } from '../services/api';\nimport { saveToLocalStorage, getFromLocalStorage, removeFromLocalStorage } from '../utils/helpers';\n\n// إنشاء Context للمصادقة\nconst AuthContext = createContext();\n\n// Provider للمصادقة\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // التحقق من وجود token عند تحميل التطبيق\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userInfo = getFromLocalStorage('userInfo');\n        \n        if (token && userInfo) {\n          // التحقق من صحة الرمز المميز\n          const response = await authService.verifyToken();\n          if (response.success) {\n            setUser(userInfo);\n            setIsAuthenticated(true);\n          } else {\n            // إزالة البيانات غير الصحيحة\n            logout();\n          }\n        }\n      } catch (error) {\n        console.error('Error checking auth:', error);\n        logout();\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // تسجيل الدخول\n  const login = (userData, token) => {\n    localStorage.setItem('authToken', token);\n    saveToLocalStorage('userInfo', userData);\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n\n  // تسجيل الخروج\n  const logout = () => {\n    removeFromLocalStorage('authToken');\n    removeFromLocalStorage('userInfo');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  // تحديث بيانات المستخدم\n  const updateUser = (userData) => {\n    saveToLocalStorage('userInfo', userData);\n    setUser(userData);\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook لاستخدام المصادقة\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Hook لإرسال OTP\nexport const useOTP = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [otpSent, setOtpSent] = useState(false);\n\n  const sendOTP = async (phoneNumber, email, language = 'ar') => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await authService.sendOTP(phoneNumber, email, language);\n      if (response.success) {\n        setOtpSent(true);\n        return response;\n      } else {\n        setError(response.message || 'فشل في إرسال رمز التحقق');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في الشبكة');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const verifyOTP = async (phoneNumber, otpCode) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await authService.verifyOTP(phoneNumber, otpCode);\n      if (response.success) {\n        return response;\n      } else {\n        setError(response.message || 'رمز التحقق غير صحيح');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في التحقق من الرمز');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetOTP = () => {\n    setOtpSent(false);\n    setError(null);\n  };\n\n  return {\n    loading,\n    error,\n    otpSent,\n    sendOTP,\n    verifyOTP,\n    resetOTP,\n  };\n};\n\n// Hook لتسجيل دخول المالك\nexport const useOwnerAuth = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const { login } = useAuth();\n\n  const ownerLogin = async (password) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await authService.ownerLogin(password);\n      if (response.success) {\n        login(response.data.owner, response.data.token);\n        return response;\n      } else {\n        setError(response.message || 'كلمة المرور غير صحيحة');\n        return null;\n      }\n    } catch (error) {\n      setError(error.message || 'خطأ في تسجيل الدخول');\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    loading,\n    error,\n    ownerLogin,\n  };\n};\n\nexport default useAuth;\n"], "mappings": ";;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,sBAAsB,QAAQ,kBAAkB;;AAElG;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC/C,MAAMC,QAAQ,GAAGlB,mBAAmB,CAAC,UAAU,CAAC;QAEhD,IAAIe,KAAK,IAAIG,QAAQ,EAAE;UACrB;UACA,MAAMC,QAAQ,GAAG,MAAMrB,WAAW,CAACsB,WAAW,CAAC,CAAC;UAChD,IAAID,QAAQ,CAACE,OAAO,EAAE;YACpBZ,OAAO,CAACS,QAAQ,CAAC;YACjBL,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM;YACL;YACAS,MAAM,CAAC,CAAC;UACV;QACF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CD,MAAM,CAAC,CAAC;MACV,CAAC,SAAS;QACRX,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,KAAK,GAAGA,CAACC,QAAQ,EAAEX,KAAK,KAAK;IACjCC,YAAY,CAACW,OAAO,CAAC,WAAW,EAAEZ,KAAK,CAAC;IACxChB,kBAAkB,CAAC,UAAU,EAAE2B,QAAQ,CAAC;IACxCjB,OAAO,CAACiB,QAAQ,CAAC;IACjBb,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMS,MAAM,GAAGA,CAAA,KAAM;IACnBrB,sBAAsB,CAAC,WAAW,CAAC;IACnCA,sBAAsB,CAAC,UAAU,CAAC;IAClCQ,OAAO,CAAC,IAAI,CAAC;IACbI,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMe,UAAU,GAAIF,QAAQ,IAAK;IAC/B3B,kBAAkB,CAAC,UAAU,EAAE2B,QAAQ,CAAC;IACxCjB,OAAO,CAACiB,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMG,KAAK,GAAG;IACZrB,IAAI;IACJE,OAAO;IACPE,eAAe;IACfa,KAAK;IACLH,MAAM;IACNM;EACF,CAAC;EAED,oBACEzB,OAAA,CAACC,WAAW,CAAC0B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,EAChCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA3B,EAAA,CAxEaF,YAAY;AAAA8B,EAAA,GAAZ9B,YAAY;AAyEzB,OAAO,MAAM+B,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG1C,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI,CAACkC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CARaD,OAAO;AASpB,OAAO,MAAMI,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAM,CAAC/B,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,KAAK,EAAEmB,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMmD,OAAO,GAAG,MAAAA,CAAOC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAC7DrC,UAAU,CAAC,IAAI,CAAC;IAChB+B,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMrB,WAAW,CAAC+C,OAAO,CAACC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,CAAC;MACxE,IAAI7B,QAAQ,CAACE,OAAO,EAAE;QACpBuB,UAAU,CAAC,IAAI,CAAC;QAChB,OAAOzB,QAAQ;MACjB,CAAC,MAAM;QACLuB,QAAQ,CAACvB,QAAQ,CAAC8B,OAAO,IAAI,yBAAyB,CAAC;QACvD,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdmB,QAAQ,CAACnB,KAAK,CAAC0B,OAAO,IAAI,eAAe,CAAC;MAC1C,OAAO,IAAI;IACb,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,SAAS,GAAG,MAAAA,CAAOJ,WAAW,EAAEK,OAAO,KAAK;IAChDxC,UAAU,CAAC,IAAI,CAAC;IAChB+B,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMrB,WAAW,CAACoD,SAAS,CAACJ,WAAW,EAAEK,OAAO,CAAC;MAClE,IAAIhC,QAAQ,CAACE,OAAO,EAAE;QACpB,OAAOF,QAAQ;MACjB,CAAC,MAAM;QACLuB,QAAQ,CAACvB,QAAQ,CAAC8B,OAAO,IAAI,qBAAqB,CAAC;QACnD,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdmB,QAAQ,CAACnB,KAAK,CAAC0B,OAAO,IAAI,wBAAwB,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,QAAQ,GAAGA,CAAA,KAAM;IACrBR,UAAU,CAAC,KAAK,CAAC;IACjBF,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,OAAO;IACLhC,OAAO;IACPa,KAAK;IACLoB,OAAO;IACPE,OAAO;IACPK,SAAS;IACTE;EACF,CAAC;AACH,CAAC;;AAED;AAAAX,GAAA,CA7DaD,MAAM;AA8DnB,OAAO,MAAMa,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAM,CAAC5C,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,KAAK,EAAEmB,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM;IAAE+B;EAAM,CAAC,GAAGW,OAAO,CAAC,CAAC;EAE3B,MAAMmB,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrC7C,UAAU,CAAC,IAAI,CAAC;IAChB+B,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMrB,WAAW,CAACyD,UAAU,CAACC,QAAQ,CAAC;MACvD,IAAIrC,QAAQ,CAACE,OAAO,EAAE;QACpBI,KAAK,CAACN,QAAQ,CAACsC,IAAI,CAACC,KAAK,EAAEvC,QAAQ,CAACsC,IAAI,CAAC1C,KAAK,CAAC;QAC/C,OAAOI,QAAQ;MACjB,CAAC,MAAM;QACLuB,QAAQ,CAACvB,QAAQ,CAAC8B,OAAO,IAAI,uBAAuB,CAAC;QACrD,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdmB,QAAQ,CAACnB,KAAK,CAAC0B,OAAO,IAAI,qBAAqB,CAAC;MAChD,OAAO,IAAI;IACb,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLD,OAAO;IACPa,KAAK;IACLgC;EACF,CAAC;AACH,CAAC;AAACD,GAAA,CA/BWD,YAAY;EAAA,QAGLjB,OAAO;AAAA;AA8B3B,eAAeA,OAAO;AAAC,IAAAD,EAAA;AAAAwB,YAAA,CAAAxB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}