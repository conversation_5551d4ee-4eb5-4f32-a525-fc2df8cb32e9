{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\OwnerDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../hooks/useAuth';\nimport { ownerService, displayService } from '../services/api';\nimport { formatCurrency, formatTimeRemaining } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  min-height: 100vh;\n  background: #f8f9fa;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n_c = Container;\nconst Header = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n`;\n_c2 = Header;\nconst HeaderContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c3 = HeaderContent;\nconst Title = styled.h1`\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0;\n`;\n_c4 = Title;\nconst LogoutButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: white;\n    color: #667eea;\n  }\n`;\n_c5 = LogoutButton;\nconst Content = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 30px 20px;\n`;\n_c6 = Content;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n`;\n_c7 = StatsGrid;\nconst StatCard = styled.div`\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  text-align: center;\n`;\n_c8 = StatCard;\nconst StatValue = styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: ${props => props.color || '#667eea'};\n  margin-bottom: 10px;\n`;\n_c9 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 1rem;\n  color: #666;\n  font-weight: 600;\n`;\n_c0 = StatLabel;\nconst Section = styled.div`\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n`;\n_c1 = Section;\nconst SectionTitle = styled.h2`\n  font-size: 1.5rem;\n  color: #333;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n_c10 = SectionTitle;\nconst DisplayGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n_c11 = DisplayGrid;\nconst DisplayCard = styled.div`\n  border: 3px solid ${props => {\n  switch (props.status) {\n    case 'available':\n      return '#28a745';\n    case 'occupied':\n      return '#dc3545';\n    case 'reserved':\n      return '#ffc107';\n    case 'maintenance':\n      return '#6c757d';\n    default:\n      return '#ddd';\n  }\n}};\n  border-radius: 10px;\n  padding: 20px;\n  background: #f8f9fa;\n`;\n_c12 = DisplayCard;\nconst DisplayNumber = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n`;\n_c13 = DisplayNumber;\nconst DisplayStatus = styled.div`\n  display: inline-block;\n  padding: 5px 15px;\n  border-radius: 15px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 10px;\n  background: ${props => {\n  switch (props.status) {\n    case 'available':\n      return '#28a745';\n    case 'occupied':\n      return '#dc3545';\n    case 'reserved':\n      return '#ffc107';\n    case 'maintenance':\n      return '#6c757d';\n    default:\n      return '#ddd';\n  }\n}};\n`;\n_c14 = DisplayStatus;\nconst CustomerInfo = styled.div`\n  background: white;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 10px;\n`;\n_c15 = CustomerInfo;\nconst TransactionTable = styled.div`\n  overflow-x: auto;\n`;\n_c16 = TransactionTable;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n`;\n_c17 = Table;\nconst TableHeader = styled.th`\n  background: #f8f9fa;\n  padding: 12px;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n  border-bottom: 2px solid #ddd;\n  font-weight: 600;\n  color: #333;\n`;\n_c18 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px;\n  border-bottom: 1px solid #eee;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n`;\n_c19 = TableCell;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  font-size: 1.2rem;\n  color: #666;\n`;\n_c20 = LoadingSpinner;\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n  margin: 20px 0;\n`;\n_c21 = ErrorMessage;\nconst OwnerDashboard = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [dashboardData, setDashboardData] = useState(null);\n  const [displays, setDisplays] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const isRTL = i18n.language === 'ar';\n  useEffect(() => {\n    // التحقق من صلاحية المالك\n    if (!user || user.type !== 'owner') {\n      navigate('/owner-login');\n      return;\n    }\n    fetchDashboardData();\n    fetchDisplays();\n\n    // تحديث البيانات كل 30 ثانية\n    const interval = setInterval(() => {\n      fetchDashboardData();\n      fetchDisplays();\n    }, 30000);\n    return () => clearInterval(interval);\n  }, [user, navigate]);\n  const fetchDashboardData = async () => {\n    try {\n      const response = await ownerService.getDashboardStats();\n      if (response.success) {\n        setDashboardData(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل البيانات');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching dashboard data:', error);\n    }\n  };\n  const fetchDisplays = async () => {\n    try {\n      const response = await displayService.getAllDisplays();\n      if (response.success) {\n        setDisplays(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching displays:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'available':\n        return t('available');\n      case 'occupied':\n        return t('occupied');\n      case 'reserved':\n        return t('reserved');\n      case 'maintenance':\n        return t('maintenance');\n      default:\n        return status;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      isRTL: isRTL,\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: /*#__PURE__*/_jsxDEV(HeaderContent, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            children: t('ownerDashboard')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          children: isRTL ? 'جاري التحميل...' : 'Loading...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    isRTL: isRTL,\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: /*#__PURE__*/_jsxDEV(HeaderContent, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: t('ownerDashboard')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LogoutButton, {\n          onClick: handleLogout,\n          children: isRTL ? 'تسجيل الخروج' : 'Logout'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      children: [error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 19\n      }, this), dashboardData && /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            color: \"#28a745\",\n            children: dashboardData.today.transactions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: t('todayTransactions')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            color: \"#667eea\",\n            children: formatCurrency(dashboardData.today.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: t('todayRevenue')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            color: \"#dc3545\",\n            children: dashboardData.total.transactions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: t('totalTransactions')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            color: \"#ffc107\",\n            children: formatCurrency(dashboardData.total.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: t('totalRevenue')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: t('displayStatus')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DisplayGrid, {\n          children: displays.map(display => /*#__PURE__*/_jsxDEV(DisplayCard, {\n            status: display.status,\n            children: [/*#__PURE__*/_jsxDEV(DisplayNumber, {\n              children: t('displayNumber', {\n                number: display.displayNumber\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '600',\n                marginBottom: '10px'\n              },\n              children: display.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DisplayStatus, {\n              status: display.status,\n              children: getStatusText(display.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), display.status === 'occupied' && display.customerName && /*#__PURE__*/_jsxDEV(CustomerInfo, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: isRTL ? 'العميل:' : 'Customer:'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 26\n                }, this), \" \", display.customerName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 21\n              }, this), display.timeRemaining && !display.timeRemaining.expired && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: isRTL ? 'الوقت المتبقي:' : 'Time remaining:'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 28\n                }, this), \" \", formatTimeRemaining(display.timeRemaining.remaining)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 19\n            }, this)]\n          }, display.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), dashboardData && dashboardData.activeTransactions.length > 0 && /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: t('activeTransactions')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TransactionTable, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                  isRTL: isRTL,\n                  children: t('transactionNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  isRTL: isRTL,\n                  children: t('customerName')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  isRTL: isRTL,\n                  children: isRTL ? 'الشاشة' : 'Display'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  isRTL: isRTL,\n                  children: t('amount')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  isRTL: isRTL,\n                  children: t('endTime')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: dashboardData.activeTransactions.map(transaction => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  isRTL: isRTL,\n                  children: transaction.transactionNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  isRTL: isRTL,\n                  children: transaction.customerName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  isRTL: isRTL,\n                  children: transaction.displayName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  isRTL: isRTL,\n                  children: formatCurrency(transaction.amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  isRTL: isRTL,\n                  children: new Date(transaction.endTime).toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this)]\n              }, transaction.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n};\n_s(OwnerDashboard, \"7i2l+OcQzrdOgOryW0ZllS5hfSU=\", false, function () {\n  return [useTranslation, useNavigate, useAuth];\n});\n_c22 = OwnerDashboard;\nexport default OwnerDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"HeaderContent\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"LogoutButton\");\n$RefreshReg$(_c6, \"Content\");\n$RefreshReg$(_c7, \"StatsGrid\");\n$RefreshReg$(_c8, \"StatCard\");\n$RefreshReg$(_c9, \"StatValue\");\n$RefreshReg$(_c0, \"StatLabel\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c10, \"SectionTitle\");\n$RefreshReg$(_c11, \"DisplayGrid\");\n$RefreshReg$(_c12, \"DisplayCard\");\n$RefreshReg$(_c13, \"DisplayNumber\");\n$RefreshReg$(_c14, \"DisplayStatus\");\n$RefreshReg$(_c15, \"CustomerInfo\");\n$RefreshReg$(_c16, \"TransactionTable\");\n$RefreshReg$(_c17, \"Table\");\n$RefreshReg$(_c18, \"TableHeader\");\n$RefreshReg$(_c19, \"TableCell\");\n$RefreshReg$(_c20, \"LoadingSpinner\");\n$RefreshReg$(_c21, \"ErrorMessage\");\n$RefreshReg$(_c22, \"OwnerDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "styled", "useAuth", "ownerService", "displayService", "formatCurrency", "formatTimeRemaining", "jsxDEV", "_jsxDEV", "Container", "div", "props", "isRTL", "_c", "Header", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c3", "Title", "h1", "_c4", "LogoutButton", "button", "_c5", "Content", "_c6", "StatsGrid", "_c7", "StatCard", "_c8", "StatValue", "color", "_c9", "StatLabel", "_c0", "Section", "_c1", "SectionTitle", "h2", "_c10", "DisplayGrid", "_c11", "DisplayCard", "status", "_c12", "DisplayNumber", "_c13", "DisplayStatus", "_c14", "CustomerInfo", "_c15", "TransactionTable", "_c16", "Table", "table", "_c17", "TableHeader", "th", "_c18", "TableCell", "td", "_c19", "LoadingSpinner", "_c20", "ErrorMessage", "_c21", "OwnerDashboard", "_s", "t", "i18n", "navigate", "user", "logout", "dashboardData", "setDashboardData", "displays", "setDisplays", "loading", "setLoading", "error", "setError", "language", "type", "fetchDashboardData", "fetchDisplays", "interval", "setInterval", "clearInterval", "response", "getDashboardStats", "success", "data", "message", "console", "getAllDisplays", "handleLogout", "getStatusText", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "today", "transactions", "revenue", "total", "map", "display", "number", "displayNumber", "style", "fontWeight", "marginBottom", "name", "customerName", "timeRemaining", "expired", "remaining", "id", "activeTransactions", "length", "transaction", "transactionNumber", "displayName", "amount", "Date", "endTime", "toLocaleTimeString", "_c22", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/OwnerDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../hooks/useAuth';\nimport { ownerService, displayService } from '../services/api';\nimport { formatCurrency, formatTimeRemaining } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: #f8f9fa;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Header = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n`;\n\nconst HeaderContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0;\n`;\n\nconst LogoutButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: white;\n    color: #667eea;\n  }\n`;\n\nconst Content = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 30px 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n`;\n\nconst StatCard = styled.div`\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  text-align: center;\n`;\n\nconst StatValue = styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: ${props => props.color || '#667eea'};\n  margin-bottom: 10px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 1rem;\n  color: #666;\n  font-weight: 600;\n`;\n\nconst Section = styled.div`\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 1.5rem;\n  color: #333;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n\nconst DisplayGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n\nconst DisplayCard = styled.div`\n  border: 3px solid ${props => {\n    switch (props.status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  }};\n  border-radius: 10px;\n  padding: 20px;\n  background: #f8f9fa;\n`;\n\nconst DisplayNumber = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst DisplayStatus = styled.div`\n  display: inline-block;\n  padding: 5px 15px;\n  border-radius: 15px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 10px;\n  background: ${props => {\n    switch (props.status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  }};\n`;\n\nconst CustomerInfo = styled.div`\n  background: white;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 10px;\n`;\n\nconst TransactionTable = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n`;\n\nconst TableHeader = styled.th`\n  background: #f8f9fa;\n  padding: 12px;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n  border-bottom: 2px solid #ddd;\n  font-weight: 600;\n  color: #333;\n`;\n\nconst TableCell = styled.td`\n  padding: 12px;\n  border-bottom: 1px solid #eee;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  font-size: 1.2rem;\n  color: #666;\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n  margin: 20px 0;\n`;\n\nconst OwnerDashboard = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  \n  const [dashboardData, setDashboardData] = useState(null);\n  const [displays, setDisplays] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  const isRTL = i18n.language === 'ar';\n\n  useEffect(() => {\n    // التحقق من صلاحية المالك\n    if (!user || user.type !== 'owner') {\n      navigate('/owner-login');\n      return;\n    }\n\n    fetchDashboardData();\n    fetchDisplays();\n\n    // تحديث البيانات كل 30 ثانية\n    const interval = setInterval(() => {\n      fetchDashboardData();\n      fetchDisplays();\n    }, 30000);\n\n    return () => clearInterval(interval);\n  }, [user, navigate]);\n\n  const fetchDashboardData = async () => {\n    try {\n      const response = await ownerService.getDashboardStats();\n      if (response.success) {\n        setDashboardData(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل البيانات');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching dashboard data:', error);\n    }\n  };\n\n  const fetchDisplays = async () => {\n    try {\n      const response = await displayService.getAllDisplays();\n      if (response.success) {\n        setDisplays(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching displays:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'available': return t('available');\n      case 'occupied': return t('occupied');\n      case 'reserved': return t('reserved');\n      case 'maintenance': return t('maintenance');\n      default: return status;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container isRTL={isRTL}>\n        <Header>\n          <HeaderContent>\n            <Title>{t('ownerDashboard')}</Title>\n          </HeaderContent>\n        </Header>\n        <Content>\n          <LoadingSpinner>\n            {isRTL ? 'جاري التحميل...' : 'Loading...'}\n          </LoadingSpinner>\n        </Content>\n      </Container>\n    );\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Header>\n        <HeaderContent>\n          <Title>{t('ownerDashboard')}</Title>\n          <LogoutButton onClick={handleLogout}>\n            {isRTL ? 'تسجيل الخروج' : 'Logout'}\n          </LogoutButton>\n        </HeaderContent>\n      </Header>\n\n      <Content>\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        {dashboardData && (\n          <StatsGrid>\n            <StatCard>\n              <StatValue color=\"#28a745\">{dashboardData.today.transactions}</StatValue>\n              <StatLabel>{t('todayTransactions')}</StatLabel>\n            </StatCard>\n            <StatCard>\n              <StatValue color=\"#667eea\">\n                {formatCurrency(dashboardData.today.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')}\n              </StatValue>\n              <StatLabel>{t('todayRevenue')}</StatLabel>\n            </StatCard>\n            <StatCard>\n              <StatValue color=\"#dc3545\">{dashboardData.total.transactions}</StatValue>\n              <StatLabel>{t('totalTransactions')}</StatLabel>\n            </StatCard>\n            <StatCard>\n              <StatValue color=\"#ffc107\">\n                {formatCurrency(dashboardData.total.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')}\n              </StatValue>\n              <StatLabel>{t('totalRevenue')}</StatLabel>\n            </StatCard>\n          </StatsGrid>\n        )}\n\n        <Section>\n          <SectionTitle>{t('displayStatus')}</SectionTitle>\n          <DisplayGrid>\n            {displays.map((display) => (\n              <DisplayCard key={display.id} status={display.status}>\n                <DisplayNumber>\n                  {t('displayNumber', { number: display.displayNumber })}\n                </DisplayNumber>\n                <div style={{ fontWeight: '600', marginBottom: '10px' }}>\n                  {display.name}\n                </div>\n                <DisplayStatus status={display.status}>\n                  {getStatusText(display.status)}\n                </DisplayStatus>\n                \n                {display.status === 'occupied' && display.customerName && (\n                  <CustomerInfo>\n                    <div><strong>{isRTL ? 'العميل:' : 'Customer:'}</strong> {display.customerName}</div>\n                    {display.timeRemaining && !display.timeRemaining.expired && (\n                      <div><strong>{isRTL ? 'الوقت المتبقي:' : 'Time remaining:'}</strong> {formatTimeRemaining(display.timeRemaining.remaining)}</div>\n                    )}\n                  </CustomerInfo>\n                )}\n              </DisplayCard>\n            ))}\n          </DisplayGrid>\n        </Section>\n\n        {dashboardData && dashboardData.activeTransactions.length > 0 && (\n          <Section>\n            <SectionTitle>{t('activeTransactions')}</SectionTitle>\n            <TransactionTable>\n              <Table>\n                <thead>\n                  <tr>\n                    <TableHeader isRTL={isRTL}>{t('transactionNumber')}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{t('customerName')}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{isRTL ? 'الشاشة' : 'Display'}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{t('amount')}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{t('endTime')}</TableHeader>\n                  </tr>\n                </thead>\n                <tbody>\n                  {dashboardData.activeTransactions.map((transaction) => (\n                    <tr key={transaction.id}>\n                      <TableCell isRTL={isRTL}>{transaction.transactionNumber}</TableCell>\n                      <TableCell isRTL={isRTL}>{transaction.customerName}</TableCell>\n                      <TableCell isRTL={isRTL}>{transaction.displayName}</TableCell>\n                      <TableCell isRTL={isRTL}>\n                        {formatCurrency(transaction.amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')}\n                      </TableCell>\n                      <TableCell isRTL={isRTL}>\n                        {new Date(transaction.endTime).toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US')}\n                      </TableCell>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </TransactionTable>\n          </Section>\n        )}\n      </Content>\n    </Container>\n  );\n};\n\nexport default OwnerDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,YAAY,EAAEC,cAAc,QAAQ,iBAAiB;AAC9D,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,SAAS,GAAGR,MAAM,CAACS,GAAG;AAC5B;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;AACnD,CAAC;AAACC,EAAA,GAJIJ,SAAS;AAMf,MAAMK,MAAM,GAAGb,MAAM,CAACS,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GALID,MAAM;AAOZ,MAAME,aAAa,GAAGf,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GANID,aAAa;AAQnB,MAAME,KAAK,GAAGjB,MAAM,CAACkB,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,YAAY,GAAGpB,MAAM,CAACqB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,OAAO,GAAGvB,MAAM,CAACS,GAAG;AAC1B;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAJID,OAAO;AAMb,MAAME,SAAS,GAAGzB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAG3B,MAAM,CAACS,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GANID,QAAQ;AAQd,MAAME,SAAS,GAAG7B,MAAM,CAACS,GAAG;AAC5B;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACoB,KAAK,IAAI,SAAS;AAC5C;AACA,CAAC;AAACC,GAAA,GALIF,SAAS;AAOf,MAAMG,SAAS,GAAGhC,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAJID,SAAS;AAMf,MAAME,OAAO,GAAGlC,MAAM,CAACS,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GANID,OAAO;AAQb,MAAME,YAAY,GAAGpC,MAAM,CAACqC,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,YAAY;AAOlB,MAAMG,WAAW,GAAGvC,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGzC,MAAM,CAACS,GAAG;AAC9B,sBAAsBC,KAAK,IAAI;EAC3B,QAAQA,KAAK,CAACgC,MAAM;IAClB,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,UAAU;MAAE,OAAO,SAAS;IACjC,KAAK,UAAU;MAAE,OAAO,SAAS;IACjC,KAAK,aAAa;MAAE,OAAO,SAAS;IACpC;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAbIF,WAAW;AAejB,MAAMG,aAAa,GAAG5C,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GALID,aAAa;AAOnB,MAAME,aAAa,GAAG9C,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAI;EACrB,QAAQA,KAAK,CAACgC,MAAM;IAClB,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,UAAU;MAAE,OAAO,SAAS;IACjC,KAAK,UAAU;MAAE,OAAO,SAAS;IACjC,KAAK,aAAa;MAAE,OAAO,SAAS;IACpC;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH,CAAC;AAACK,IAAA,GAjBID,aAAa;AAmBnB,MAAME,YAAY,GAAGhD,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GALID,YAAY;AAOlB,MAAME,gBAAgB,GAAGlD,MAAM,CAACS,GAAG;AACnC;AACA,CAAC;AAAC0C,IAAA,GAFID,gBAAgB;AAItB,MAAME,KAAK,GAAGpD,MAAM,CAACqD,KAAK;AAC1B;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAJIF,KAAK;AAMX,MAAMG,WAAW,GAAGvD,MAAM,CAACwD,EAAE;AAC7B;AACA;AACA,gBAAgB9C,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,OAAO,GAAG,MAAM;AACvD;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GAPIF,WAAW;AASjB,MAAMG,SAAS,GAAG1D,MAAM,CAAC2D,EAAE;AAC3B;AACA;AACA,gBAAgBjD,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,OAAO,GAAG,MAAM;AACvD,CAAC;AAACiD,IAAA,GAJIF,SAAS;AAMf,MAAMG,cAAc,GAAG7D,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqD,IAAA,GAPID,cAAc;AASpB,MAAME,YAAY,GAAG/D,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAPID,YAAY;AASlB,MAAME,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGtE,cAAc,CAAC,CAAC;EACpC,MAAMuE,QAAQ,GAAGtE,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuE,IAAI;IAAEC;EAAO,CAAC,GAAGtE,OAAO,CAAC,CAAC;EAElC,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8E,QAAQ,EAAEC,WAAW,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgF,OAAO,EAAEC,UAAU,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMe,KAAK,GAAGyD,IAAI,CAACY,QAAQ,KAAK,IAAI;EAEpCnF,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACyE,IAAI,IAAIA,IAAI,CAACW,IAAI,KAAK,OAAO,EAAE;MAClCZ,QAAQ,CAAC,cAAc,CAAC;MACxB;IACF;IAEAa,kBAAkB,CAAC,CAAC;IACpBC,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCH,kBAAkB,CAAC,CAAC;MACpBC,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,KAAK,CAAC;IAET,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACd,IAAI,EAAED,QAAQ,CAAC,CAAC;EAEpB,MAAMa,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMrF,YAAY,CAACsF,iBAAiB,CAAC,CAAC;MACvD,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBhB,gBAAgB,CAACc,QAAQ,CAACG,IAAI,CAAC;QAC/BX,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACLA,QAAQ,CAACQ,QAAQ,CAACI,OAAO,IAAI,uBAAuB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,QAAQ,CAAC,eAAe,CAAC;MACzBa,OAAO,CAACd,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpF,cAAc,CAAC0F,cAAc,CAAC,CAAC;MACtD,IAAIN,QAAQ,CAACE,OAAO,EAAE;QACpBd,WAAW,CAACY,QAAQ,CAACG,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzBvB,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAM0B,aAAa,GAAIrD,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAOyB,CAAC,CAAC,WAAW,CAAC;MACvC,KAAK,UAAU;QAAE,OAAOA,CAAC,CAAC,UAAU,CAAC;MACrC,KAAK,UAAU;QAAE,OAAOA,CAAC,CAAC,UAAU,CAAC;MACrC,KAAK,aAAa;QAAE,OAAOA,CAAC,CAAC,aAAa,CAAC;MAC3C;QAAS,OAAOzB,MAAM;IACxB;EACF,CAAC;EAED,IAAIkC,OAAO,EAAE;IACX,oBACErE,OAAA,CAACC,SAAS;MAACG,KAAK,EAAEA,KAAM;MAAAqF,QAAA,gBACtBzF,OAAA,CAACM,MAAM;QAAAmF,QAAA,eACLzF,OAAA,CAACQ,aAAa;UAAAiF,QAAA,eACZzF,OAAA,CAACU,KAAK;YAAA+E,QAAA,EAAE7B,CAAC,CAAC,gBAAgB;UAAC;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACT7F,OAAA,CAACgB,OAAO;QAAAyE,QAAA,eACNzF,OAAA,CAACsD,cAAc;UAAAmC,QAAA,EACZrF,KAAK,GAAG,iBAAiB,GAAG;QAAY;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEhB;EAEA,oBACE7F,OAAA,CAACC,SAAS;IAACG,KAAK,EAAEA,KAAM;IAAAqF,QAAA,gBACtBzF,OAAA,CAACM,MAAM;MAAAmF,QAAA,eACLzF,OAAA,CAACQ,aAAa;QAAAiF,QAAA,gBACZzF,OAAA,CAACU,KAAK;UAAA+E,QAAA,EAAE7B,CAAC,CAAC,gBAAgB;QAAC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpC7F,OAAA,CAACa,YAAY;UAACiF,OAAO,EAAEP,YAAa;UAAAE,QAAA,EACjCrF,KAAK,GAAG,cAAc,GAAG;QAAQ;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET7F,OAAA,CAACgB,OAAO;MAAAyE,QAAA,GACLlB,KAAK,iBAAIvE,OAAA,CAACwD,YAAY;QAAAiC,QAAA,EAAElB;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,EAE7C5B,aAAa,iBACZjE,OAAA,CAACkB,SAAS;QAAAuE,QAAA,gBACRzF,OAAA,CAACoB,QAAQ;UAAAqE,QAAA,gBACPzF,OAAA,CAACsB,SAAS;YAACC,KAAK,EAAC,SAAS;YAAAkE,QAAA,EAAExB,aAAa,CAAC8B,KAAK,CAACC;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzE7F,OAAA,CAACyB,SAAS;YAAAgE,QAAA,EAAE7B,CAAC,CAAC,mBAAmB;UAAC;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACX7F,OAAA,CAACoB,QAAQ;UAAAqE,QAAA,gBACPzF,OAAA,CAACsB,SAAS;YAACC,KAAK,EAAC,SAAS;YAAAkE,QAAA,EACvB5F,cAAc,CAACoE,aAAa,CAAC8B,KAAK,CAACE,OAAO,EAAE,KAAK,EAAE7F,KAAK,GAAG,OAAO,GAAG,OAAO;UAAC;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACZ7F,OAAA,CAACyB,SAAS;YAAAgE,QAAA,EAAE7B,CAAC,CAAC,cAAc;UAAC;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACX7F,OAAA,CAACoB,QAAQ;UAAAqE,QAAA,gBACPzF,OAAA,CAACsB,SAAS;YAACC,KAAK,EAAC,SAAS;YAAAkE,QAAA,EAAExB,aAAa,CAACiC,KAAK,CAACF;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzE7F,OAAA,CAACyB,SAAS;YAAAgE,QAAA,EAAE7B,CAAC,CAAC,mBAAmB;UAAC;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACX7F,OAAA,CAACoB,QAAQ;UAAAqE,QAAA,gBACPzF,OAAA,CAACsB,SAAS;YAACC,KAAK,EAAC,SAAS;YAAAkE,QAAA,EACvB5F,cAAc,CAACoE,aAAa,CAACiC,KAAK,CAACD,OAAO,EAAE,KAAK,EAAE7F,KAAK,GAAG,OAAO,GAAG,OAAO;UAAC;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACZ7F,OAAA,CAACyB,SAAS;YAAAgE,QAAA,EAAE7B,CAAC,CAAC,cAAc;UAAC;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACZ,eAED7F,OAAA,CAAC2B,OAAO;QAAA8D,QAAA,gBACNzF,OAAA,CAAC6B,YAAY;UAAA4D,QAAA,EAAE7B,CAAC,CAAC,eAAe;QAAC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eACjD7F,OAAA,CAACgC,WAAW;UAAAyD,QAAA,EACTtB,QAAQ,CAACgC,GAAG,CAAEC,OAAO,iBACpBpG,OAAA,CAACkC,WAAW;YAAkBC,MAAM,EAAEiE,OAAO,CAACjE,MAAO;YAAAsD,QAAA,gBACnDzF,OAAA,CAACqC,aAAa;cAAAoD,QAAA,EACX7B,CAAC,CAAC,eAAe,EAAE;gBAAEyC,MAAM,EAAED,OAAO,CAACE;cAAc,CAAC;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAChB7F,OAAA;cAAKuG,KAAK,EAAE;gBAAEC,UAAU,EAAE,KAAK;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAhB,QAAA,EACrDW,OAAO,CAACM;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7F,OAAA,CAACuC,aAAa;cAACJ,MAAM,EAAEiE,OAAO,CAACjE,MAAO;cAAAsD,QAAA,EACnCD,aAAa,CAACY,OAAO,CAACjE,MAAM;YAAC;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EAEfO,OAAO,CAACjE,MAAM,KAAK,UAAU,IAAIiE,OAAO,CAACO,YAAY,iBACpD3G,OAAA,CAACyC,YAAY;cAAAgD,QAAA,gBACXzF,OAAA;gBAAAyF,QAAA,gBAAKzF,OAAA;kBAAAyF,QAAA,EAASrF,KAAK,GAAG,SAAS,GAAG;gBAAW;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,KAAC,EAACO,OAAO,CAACO,YAAY;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnFO,OAAO,CAACQ,aAAa,IAAI,CAACR,OAAO,CAACQ,aAAa,CAACC,OAAO,iBACtD7G,OAAA;gBAAAyF,QAAA,gBAAKzF,OAAA;kBAAAyF,QAAA,EAASrF,KAAK,GAAG,gBAAgB,GAAG;gBAAiB;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,KAAC,EAAC/F,mBAAmB,CAACsG,OAAO,CAACQ,aAAa,CAACE,SAAS,CAAC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACjI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CACf;UAAA,GAlBeO,OAAO,CAACW,EAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBf,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAET5B,aAAa,IAAIA,aAAa,CAAC+C,kBAAkB,CAACC,MAAM,GAAG,CAAC,iBAC3DjH,OAAA,CAAC2B,OAAO;QAAA8D,QAAA,gBACNzF,OAAA,CAAC6B,YAAY;UAAA4D,QAAA,EAAE7B,CAAC,CAAC,oBAAoB;QAAC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eACtD7F,OAAA,CAAC2C,gBAAgB;UAAA8C,QAAA,eACfzF,OAAA,CAAC6C,KAAK;YAAA4C,QAAA,gBACJzF,OAAA;cAAAyF,QAAA,eACEzF,OAAA;gBAAAyF,QAAA,gBACEzF,OAAA,CAACgD,WAAW;kBAAC5C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAE7B,CAAC,CAAC,mBAAmB;gBAAC;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC,eACjE7F,OAAA,CAACgD,WAAW;kBAAC5C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAE7B,CAAC,CAAC,cAAc;gBAAC;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC,eAC5D7F,OAAA,CAACgD,WAAW;kBAAC5C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAErF,KAAK,GAAG,QAAQ,GAAG;gBAAS;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC,eACvE7F,OAAA,CAACgD,WAAW;kBAAC5C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAE7B,CAAC,CAAC,QAAQ;gBAAC;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC,eACtD7F,OAAA,CAACgD,WAAW;kBAAC5C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAE7B,CAAC,CAAC,SAAS;gBAAC;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR7F,OAAA;cAAAyF,QAAA,EACGxB,aAAa,CAAC+C,kBAAkB,CAACb,GAAG,CAAEe,WAAW,iBAChDlH,OAAA;gBAAAyF,QAAA,gBACEzF,OAAA,CAACmD,SAAS;kBAAC/C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAEyB,WAAW,CAACC;gBAAiB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpE7F,OAAA,CAACmD,SAAS;kBAAC/C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAEyB,WAAW,CAACP;gBAAY;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/D7F,OAAA,CAACmD,SAAS;kBAAC/C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EAAEyB,WAAW,CAACE;gBAAW;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9D7F,OAAA,CAACmD,SAAS;kBAAC/C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EACrB5F,cAAc,CAACqH,WAAW,CAACG,MAAM,EAAE,KAAK,EAAEjH,KAAK,GAAG,OAAO,GAAG,OAAO;gBAAC;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACZ7F,OAAA,CAACmD,SAAS;kBAAC/C,KAAK,EAAEA,KAAM;kBAAAqF,QAAA,EACrB,IAAI6B,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,kBAAkB,CAACpH,KAAK,GAAG,OAAO,GAAG,OAAO;gBAAC;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA,GATLqB,WAAW,CAACH,EAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUnB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEhB,CAAC;AAAClC,EAAA,CAlMID,cAAc;EAAA,QACEnE,cAAc,EACjBC,WAAW,EACHE,OAAO;AAAA;AAAA+H,IAAA,GAH5B/D,cAAc;AAoMpB,eAAeA,cAAc;AAAC,IAAArD,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAgE,IAAA;AAAAC,YAAA,CAAArH,EAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}