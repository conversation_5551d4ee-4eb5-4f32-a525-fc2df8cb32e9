{"ast": null, "code": "import React from'react';import{<PERSON>rowserRouter as Router,Routes,Route}from'react-router-dom';import{AuthProvider}from'./hooks/useAuth';import'./utils/i18n';import'./App.css';// استيراد الصفحات\nimport HomePage from'./pages/HomePage';import DisplaySelectionPage from'./pages/DisplaySelectionPage';import LoginPage from'./pages/LoginPage';import NameInputPage from'./pages/NameInputPage';import PaymentPage from'./pages/PaymentPage';import SuccessPage from'./pages/SuccessPage';import OwnerLoginPage from'./pages/OwnerLoginPage';import OwnerDashboard from'./pages/OwnerDashboard';import DisplayScreen from'./pages/DisplayScreen';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(HomePage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/select-display\",element:/*#__PURE__*/_jsx(DisplaySelectionPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/enter-name\",element:/*#__PURE__*/_jsx(NameInputPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/payment\",element:/*#__PURE__*/_jsx(PaymentPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/success\",element:/*#__PURE__*/_jsx(SuccessPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/owner-login\",element:/*#__PURE__*/_jsx(OwnerLoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/owner-dashboard\",element:/*#__PURE__*/_jsx(OwnerDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/display/:displayId\",element:/*#__PURE__*/_jsx(DisplayScreen,{})})]})})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "HomePage", "DisplaySelectionPage", "LoginPage", "NameInputPage", "PaymentPage", "SuccessPage", "OwnerLoginPage", "OwnerDashboard", "DisplayScreen", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element"], "sources": ["D:/برمجة/tste 1/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './hooks/useAuth';\nimport './utils/i18n';\nimport './App.css';\n\n// استيراد الصفحات\nimport HomePage from './pages/HomePage';\nimport DisplaySelectionPage from './pages/DisplaySelectionPage';\nimport LoginPage from './pages/LoginPage';\nimport NameInputPage from './pages/NameInputPage';\nimport PaymentPage from './pages/PaymentPage';\nimport SuccessPage from './pages/SuccessPage';\nimport OwnerLoginPage from './pages/OwnerLoginPage';\nimport OwnerDashboard from './pages/OwnerDashboard';\nimport DisplayScreen from './pages/DisplayScreen';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/select-display\" element={<DisplaySelectionPage />} />\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"/enter-name\" element={<NameInputPage />} />\n            <Route path=\"/payment\" element={<PaymentPage />} />\n            <Route path=\"/success\" element={<SuccessPage />} />\n            <Route path=\"/owner-login\" element={<OwnerLoginPage />} />\n            <Route path=\"/owner-dashboard\" element={<OwnerDashboard />} />\n            <Route path=\"/display/:displayId\" element={<DisplayScreen />} />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,OAASC,YAAY,KAAQ,iBAAiB,CAC9C,MAAO,cAAc,CACrB,MAAO,WAAW,CAElB;AACA,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,oBAAoB,KAAM,8BAA8B,CAC/D,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACX,YAAY,EAAAe,QAAA,cACXJ,IAAA,CAACd,MAAM,EAAAkB,QAAA,cACLJ,IAAA,QAAKK,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBF,KAAA,CAACf,MAAM,EAAAiB,QAAA,eACLJ,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACV,QAAQ,GAAE,CAAE,CAAE,CAAC,cACzCU,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEP,IAAA,CAACT,oBAAoB,GAAE,CAAE,CAAE,CAAC,cACnES,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACR,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CQ,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEP,IAAA,CAACP,aAAa,GAAE,CAAE,CAAE,CAAC,cACxDO,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACN,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDM,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDK,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAE,CAAC,cAC1DI,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEP,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAC9DG,IAAA,CAACZ,KAAK,EAACkB,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAEP,IAAA,CAACF,aAAa,GAAE,CAAE,CAAE,CAAC,EAC1D,CAAC,CACN,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}