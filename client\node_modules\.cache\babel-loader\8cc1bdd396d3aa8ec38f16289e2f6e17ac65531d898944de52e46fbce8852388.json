{"ast": null, "code": "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};", "map": {"version": 3, "names": ["createContext", "getDefaults", "setDefaults", "getI18n", "setI18n", "initReactI18next", "I18nContext", "ReportNamespaces", "constructor", "usedNamespaces", "addUsedNamespaces", "namespaces", "for<PERSON>ach", "ns", "getUsedNamespaces", "Object", "keys", "composeInitialProps", "ForComponent", "ctx", "componentsInitialProps", "getInitialProps", "i18nInitialProps", "i18n", "reportNamespaces", "ret", "initialI18nStore", "languages", "l", "getResourceBundle", "initialLanguage", "language"], "sources": ["D:/برمجة/tste 1/node_modules/react-i18next/dist/es/context.js"], "sourcesContent": ["import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,WAAW,EAAEC,WAAW,QAAQ,eAAe;AACxD,SAASC,OAAO,EAAEC,OAAO,QAAQ,mBAAmB;AACpD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASJ,WAAW,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,gBAAgB;AACrE,OAAO,MAAMC,WAAW,GAAGN,aAAa,CAAC,CAAC;AAC1C,OAAO,MAAMO,gBAAgB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;EAC1B;EACAC,iBAAiBA,CAACC,UAAU,EAAE;IAC5BA,UAAU,CAACC,OAAO,CAACC,EAAE,IAAI;MACvB,IAAI,CAAC,IAAI,CAACJ,cAAc,CAACI,EAAE,CAAC,EAAE,IAAI,CAACJ,cAAc,CAACI,EAAE,CAAC,GAAG,IAAI;IAC9D,CAAC,CAAC;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAClB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,cAAc,CAAC;EACzC;AACF;AACA,OAAO,MAAMQ,mBAAmB,GAAGC,YAAY,IAAI,MAAMC,GAAG,IAAI;EAC9D,MAAMC,sBAAsB,GAAG,CAAC,MAAMF,YAAY,CAACG,eAAe,GAAGF,GAAG,CAAC,KAAK,CAAC,CAAC;EAChF,MAAMG,gBAAgB,GAAGD,eAAe,CAAC,CAAC;EAC1C,OAAO;IACL,GAAGD,sBAAsB;IACzB,GAAGE;EACL,CAAC;AACH,CAAC;AACD,OAAO,MAAMD,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAME,IAAI,GAAGpB,OAAO,CAAC,CAAC;EACtB,MAAMQ,UAAU,GAAGY,IAAI,CAACC,gBAAgB,EAAEV,iBAAiB,CAAC,CAAC,IAAI,EAAE;EACnE,MAAMW,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BH,IAAI,CAACI,SAAS,CAACf,OAAO,CAACgB,CAAC,IAAI;IAC1BF,gBAAgB,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;IACxBjB,UAAU,CAACC,OAAO,CAACC,EAAE,IAAI;MACvBa,gBAAgB,CAACE,CAAC,CAAC,CAACf,EAAE,CAAC,GAAGU,IAAI,CAACM,iBAAiB,CAACD,CAAC,EAAEf,EAAE,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ,CAAC,CAAC;EACFY,GAAG,CAACC,gBAAgB,GAAGA,gBAAgB;EACvCD,GAAG,CAACK,eAAe,GAAGP,IAAI,CAACQ,QAAQ;EACnC,OAAON,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}