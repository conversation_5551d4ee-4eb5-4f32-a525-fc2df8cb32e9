{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "i18next": "^23.7.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "socket.io-client": "^4.7.4", "styled-components": "^6.1.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}