{"ast": null, "code": "'use strict';\n\nvar check = function (it) {\n  return it && it.Math === Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n// eslint-disable-next-line es/no-global-this -- safe\ncheck(typeof globalThis == 'object' && globalThis) || check(typeof window == 'object' && window) ||\n// eslint-disable-next-line no-restricted-globals -- safe\ncheck(typeof self == 'object' && self) || check(typeof global == 'object' && global) || check(typeof this == 'object' && this) ||\n// eslint-disable-next-line no-new-func -- fallback\nfunction () {\n  return this;\n}() || Function('return this')();", "map": {"version": 3, "names": ["check", "it", "Math", "module", "exports", "globalThis", "window", "self", "global", "Function"], "sources": ["D:/برمجة/tste 1/client/node_modules/core-js-pure/internals/global-this.js"], "sourcesContent": ["'use strict';\nvar check = function (it) {\n  return it && it.Math === Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  check(typeof this == 'object' && this) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACxB,OAAOA,EAAE,IAAIA,EAAE,CAACC,IAAI,KAAKA,IAAI,IAAID,EAAE;AACrC,CAAC;;AAED;AACAE,MAAM,CAACC,OAAO;AACZ;AACAJ,KAAK,CAAC,OAAOK,UAAU,IAAI,QAAQ,IAAIA,UAAU,CAAC,IAClDL,KAAK,CAAC,OAAOM,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAAC;AAC1C;AACAN,KAAK,CAAC,OAAOO,IAAI,IAAI,QAAQ,IAAIA,IAAI,CAAC,IACtCP,KAAK,CAAC,OAAOQ,MAAM,IAAI,QAAQ,IAAIA,MAAM,CAAC,IAC1CR,KAAK,CAAC,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;AACtC;AACC,YAAY;EAAE,OAAO,IAAI;AAAE,CAAC,CAAE,CAAC,IAAIS,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}