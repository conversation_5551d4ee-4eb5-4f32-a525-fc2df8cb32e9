<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الشاشات الذكي | Smart Display Management System</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📺</text></svg>">
    
    <style>
        /* ===== RESET & BASE ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Colors - Professional Palette */
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary: #64748b;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            /* Neutrals */
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            
            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            /* Typography */
            --font-ar: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            
            /* Transitions */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* ===== TYPOGRAPHY ===== */
        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-ar);
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body.en {
            font-family: var(--font-en);
            direction: ltr;
        }

        /* ===== LAYOUT ===== */
        .app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-6);
        }

        .container-fluid {
            width: 100%;
            padding: 0 var(--space-6);
        }

        /* ===== HEADER ===== */
        .header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 50;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 4rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--primary);
            text-decoration: none;
        }

        .logo-icon {
            width: 2rem;
            height: 2rem;
            background: var(--gradient-primary);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1rem;
        }

        .nav {
            display: flex;
            align-items: center;
            gap: var(--space-6);
        }

        .nav-link {
            color: var(--gray-600);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-fast);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-md);
        }

        .nav-link:hover {
            color: var(--primary);
            background: var(--gray-100);
        }

        .nav-link.active {
            color: var(--primary);
            background: var(--primary);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: var(--white);
        }

        /* ===== LANGUAGE SWITCHER ===== */
        .language-switcher {
            display: flex;
            background: var(--gray-100);
            border-radius: var(--radius-full);
            padding: var(--space-1);
        }

        .lang-btn {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            border: none;
            background: transparent;
            border-radius: var(--radius-full);
            cursor: pointer;
            transition: var(--transition-fast);
            font-weight: 500;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .lang-btn.active {
            background: var(--white);
            color: var(--primary);
            box-shadow: var(--shadow-sm);
        }

        .lang-btn:hover:not(.active) {
            color: var(--gray-800);
        }

        /* ===== MAIN CONTENT ===== */
        .main {
            flex: 1;
            padding: var(--space-8) 0;
        }

        /* ===== HERO SECTION ===== */
        .hero {
            text-align: center;
            padding: var(--space-20) 0;
            background: var(--gradient-primary);
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: var(--space-6);
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: var(--space-8);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-actions {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }

        /* ===== BUTTONS ===== */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--white);
            color: var(--primary);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .btn-lg {
            padding: var(--space-4) var(--space-8);
            font-size: 1.125rem;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
            
            .nav {
                gap: var(--space-3);
            }
            
            .container {
                padding: 0 var(--space-4);
            }
        }

        /* ===== ANIMATIONS ===== */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* ===== UTILITIES ===== */
        .hidden {
            display: none !important;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <!-- Logo -->
                    <a href="#" class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <span data-key="system-name">نظام إدارة الشاشات الذكي</span>
                    </a>

                    <!-- Navigation -->
                    <nav class="nav">
                        <a href="#" class="nav-link active" data-key="home">الرئيسية</a>
                        <a href="#" class="nav-link" data-key="displays">الشاشات</a>
                        <a href="#" class="nav-link" data-key="reports">التقارير</a>
                        <a href="#" class="nav-link" data-key="settings">الإعدادات</a>
                        
                        <!-- Language Switcher -->
                        <div class="language-switcher">
                            <button class="lang-btn active" data-lang="ar">
                                <span>🇸🇦</span>
                                <span>العربية</span>
                            </button>
                            <button class="lang-btn" data-lang="en">
                                <span>🇺🇸</span>
                                <span>English</span>
                            </button>
                        </div>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Hero Section -->
            <section class="hero">
                <div class="container">
                    <div class="hero-content animate-fade-in-up">
                        <h1 class="hero-title" data-key="hero-title">
                            نظام إدارة الشاشات الذكي
                        </h1>
                        <p class="hero-subtitle" data-key="hero-subtitle">
                            حل متطور وشامل لإدارة الشاشات التفاعلية مع تقنيات الذكاء الاصطناعي ونظام دفع متقدم
                        </p>
                        <div class="hero-actions">
                            <a href="#" class="btn btn-primary btn-lg" id="start-transaction">
                                <i class="fas fa-play"></i>
                                <span data-key="start-transaction">ابدأ المعاملة</span>
                            </a>
                            <a href="#" class="btn btn-secondary btn-lg" id="owner-dashboard">
                                <i class="fas fa-chart-line"></i>
                                <span data-key="owner-dashboard">لوحة التحكم</span>
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Translation System
        const translations = {
            ar: {
                'system-name': 'نظام إدارة الشاشات الذكي',
                'home': 'الرئيسية',
                'displays': 'الشاشات',
                'reports': 'التقارير',
                'settings': 'الإعدادات',
                'hero-title': 'نظام إدارة الشاشات الذكي',
                'hero-subtitle': 'حل متطور وشامل لإدارة الشاشات التفاعلية مع تقنيات الذكاء الاصطناعي ونظام دفع متقدم',
                'start-transaction': 'ابدأ المعاملة',
                'owner-dashboard': 'لوحة التحكم'
            },
            en: {
                'system-name': 'Smart Display Management System',
                'home': 'Home',
                'displays': 'Displays',
                'reports': 'Reports',
                'settings': 'Settings',
                'hero-title': 'Smart Display Management System',
                'hero-subtitle': 'Advanced and comprehensive solution for managing interactive displays with AI technology and advanced payment system',
                'start-transaction': 'Start Transaction',
                'owner-dashboard': 'Dashboard'
            }
        };

        // Language Management
        class LanguageManager {
            constructor() {
                this.currentLang = 'ar';
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.updateLanguage();
            }

            setupEventListeners() {
                document.querySelectorAll('.lang-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const lang = e.currentTarget.dataset.lang;
                        this.setLanguage(lang);
                    });
                });
            }

            setLanguage(lang) {
                this.currentLang = lang;
                
                // Update active button
                document.querySelectorAll('.lang-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.lang === lang);
                });

                // Update body class and direction
                document.body.className = lang === 'en' ? 'en' : '';
                document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
                document.documentElement.lang = lang;

                this.updateLanguage();
            }

            updateLanguage() {
                document.querySelectorAll('[data-key]').forEach(element => {
                    const key = element.dataset.key;
                    if (translations[this.currentLang][key]) {
                        element.textContent = translations[this.currentLang][key];
                    }
                });
            }
        }

        // Application Manager
        class AppManager {
            constructor() {
                this.languageManager = new LanguageManager();
                this.init();
            }

            init() {
                this.setupEventListeners();
                console.log('🚀 Smart Display Management System Initialized');
            }

            setupEventListeners() {
                // Start Transaction Button
                document.getElementById('start-transaction')?.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.startTransaction();
                });

                // Owner Dashboard Button
                document.getElementById('owner-dashboard')?.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.openOwnerDashboard();
                });
            }

            startTransaction() {
                console.log('🎯 Starting new transaction...');
                const lang = this.languageManager.currentLang;
                window.location.href = `displays.html?lang=${lang}`;
            }

            openOwnerDashboard() {
                console.log('📊 Opening owner dashboard...');
                // Will be implemented in next phase
                alert('سيتم الانتقال إلى لوحة تحكم المالك...\nWill navigate to owner dashboard...');
            }
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', () => {
            new AppManager();
        });
    </script>
</body>
</html>
