{"ast": null, "code": "export { Trans } from './Trans.js';\nexport { Trans as TransWithoutContext } from './TransWithoutContext.js';\nexport { useTranslation } from './useTranslation.js';\nexport { withTranslation } from './withTranslation.js';\nexport { Translation } from './Translation.js';\nexport { I18nextProvider } from './I18nextProvider.js';\nexport { withSSR } from './withSSR.js';\nexport { useSSR } from './useSSR.js';\nexport { initReactI18next } from './initReactI18next.js';\nexport { setDefaults, getDefaults } from './defaults.js';\nexport { setI18n, getI18n } from './i18nInstance.js';\nexport { I18nContext, composeInitialProps, getInitialProps } from './context.js';\nexport const date = () => '';\nexport const time = () => '';\nexport const number = () => '';\nexport const select = () => '';\nexport const plural = () => '';\nexport const selectOrdinal = () => '';", "map": {"version": 3, "names": ["Trans", "TransWithoutContext", "useTranslation", "withTranslation", "Translation", "I18nextProvider", "withSSR", "useSSR", "initReactI18next", "setDefaults", "getDefaults", "setI18n", "getI18n", "I18nContext", "composeInitialProps", "getInitialProps", "date", "time", "number", "select", "plural", "selectOrdinal"], "sources": ["D:/برمجة/tste 1/node_modules/react-i18next/dist/es/index.js"], "sourcesContent": ["export { Trans } from './Trans.js';\nexport { Trans as TransWithoutContext } from './TransWithoutContext.js';\nexport { useTranslation } from './useTranslation.js';\nexport { withTranslation } from './withTranslation.js';\nexport { Translation } from './Translation.js';\nexport { I18nextProvider } from './I18nextProvider.js';\nexport { withSSR } from './withSSR.js';\nexport { useSSR } from './useSSR.js';\nexport { initReactI18next } from './initReactI18next.js';\nexport { setDefaults, getDefaults } from './defaults.js';\nexport { setI18n, getI18n } from './i18nInstance.js';\nexport { I18nContext, composeInitialProps, getInitialProps } from './context.js';\nexport const date = () => '';\nexport const time = () => '';\nexport const number = () => '';\nexport const select = () => '';\nexport const plural = () => '';\nexport const selectOrdinal = () => '';"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASA,KAAK,IAAIC,mBAAmB,QAAQ,0BAA0B;AACvE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,WAAW,EAAEC,WAAW,QAAQ,eAAe;AACxD,SAASC,OAAO,EAAEC,OAAO,QAAQ,mBAAmB;AACpD,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,cAAc;AAChF,OAAO,MAAMC,IAAI,GAAGA,CAAA,KAAM,EAAE;AAC5B,OAAO,MAAMC,IAAI,GAAGA,CAAA,KAAM,EAAE;AAC5B,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM,EAAE;AAC9B,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM,EAAE;AAC9B,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM,EAAE;AAC9B,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}