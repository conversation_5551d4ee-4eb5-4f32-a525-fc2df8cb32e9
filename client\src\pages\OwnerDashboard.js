import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../hooks/useAuth';
import { ownerService, displayService } from '../services/api';
import { formatCurrency, formatTimeRemaining } from '../utils/helpers';

const Container = styled.div`
  min-height: 100vh;
  background: #f8f9fa;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Header = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`;

const LogoutButton = styled.button`
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2px solid white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: white;
    color: #667eea;
  }
`;

const Content = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 2.5rem;
  font-weight: bold;
  color: ${props => props.color || '#667eea'};
  margin-bottom: 10px;
`;

const StatLabel = styled.div`
  font-size: 1rem;
  color: #666;
  font-weight: 600;
`;

const Section = styled.div`
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 20px;
  font-weight: 700;
`;

const DisplayGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const DisplayCard = styled.div`
  border: 3px solid ${props => {
    switch (props.status) {
      case 'available': return '#28a745';
      case 'occupied': return '#dc3545';
      case 'reserved': return '#ffc107';
      case 'maintenance': return '#6c757d';
      default: return '#ddd';
    }
  }};
  border-radius: 10px;
  padding: 20px;
  background: #f8f9fa;
`;

const DisplayNumber = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
`;

const DisplayStatus = styled.div`
  display: inline-block;
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
  background: ${props => {
    switch (props.status) {
      case 'available': return '#28a745';
      case 'occupied': return '#dc3545';
      case 'reserved': return '#ffc107';
      case 'maintenance': return '#6c757d';
      default: return '#ddd';
    }
  }};
`;

const CustomerInfo = styled.div`
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-top: 10px;
`;

const TransactionTable = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
`;

const TableHeader = styled.th`
  background: #f8f9fa;
  padding: 12px;
  text-align: ${props => props.isRTL ? 'right' : 'left'};
  border-bottom: 2px solid #ddd;
  font-weight: 600;
  color: #333;
`;

const TableCell = styled.td`
  padding: 12px;
  border-bottom: 1px solid #eee;
  text-align: ${props => props.isRTL ? 'right' : 'left'};
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #666;
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  margin: 20px 0;
`;

const OwnerDashboard = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  
  const [dashboardData, setDashboardData] = useState(null);
  const [displays, setDisplays] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    // التحقق من صلاحية المالك
    if (!user || user.type !== 'owner') {
      navigate('/owner-login');
      return;
    }

    fetchDashboardData();
    fetchDisplays();

    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(() => {
      fetchDashboardData();
      fetchDisplays();
    }, 30000);

    return () => clearInterval(interval);
  }, [user, navigate]);

  const fetchDashboardData = async () => {
    try {
      const response = await ownerService.getDashboardStats();
      if (response.success) {
        setDashboardData(response.data);
        setError(null);
      } else {
        setError(response.message || 'فشل في تحميل البيانات');
      }
    } catch (error) {
      setError('خطأ في الشبكة');
      console.error('Error fetching dashboard data:', error);
    }
  };

  const fetchDisplays = async () => {
    try {
      const response = await displayService.getAllDisplays();
      if (response.success) {
        setDisplays(response.data);
      }
    } catch (error) {
      console.error('Error fetching displays:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'available': return t('available');
      case 'occupied': return t('occupied');
      case 'reserved': return t('reserved');
      case 'maintenance': return t('maintenance');
      default: return status;
    }
  };

  if (loading) {
    return (
      <Container isRTL={isRTL}>
        <Header>
          <HeaderContent>
            <Title>{t('ownerDashboard')}</Title>
          </HeaderContent>
        </Header>
        <Content>
          <LoadingSpinner>
            {isRTL ? 'جاري التحميل...' : 'Loading...'}
          </LoadingSpinner>
        </Content>
      </Container>
    );
  }

  return (
    <Container isRTL={isRTL}>
      <Header>
        <HeaderContent>
          <Title>{t('ownerDashboard')}</Title>
          <LogoutButton onClick={handleLogout}>
            {isRTL ? 'تسجيل الخروج' : 'Logout'}
          </LogoutButton>
        </HeaderContent>
      </Header>

      <Content>
        {error && <ErrorMessage>{error}</ErrorMessage>}

        {dashboardData && (
          <StatsGrid>
            <StatCard>
              <StatValue color="#28a745">{dashboardData.today.transactions}</StatValue>
              <StatLabel>{t('todayTransactions')}</StatLabel>
            </StatCard>
            <StatCard>
              <StatValue color="#667eea">
                {formatCurrency(dashboardData.today.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')}
              </StatValue>
              <StatLabel>{t('todayRevenue')}</StatLabel>
            </StatCard>
            <StatCard>
              <StatValue color="#dc3545">{dashboardData.total.transactions}</StatValue>
              <StatLabel>{t('totalTransactions')}</StatLabel>
            </StatCard>
            <StatCard>
              <StatValue color="#ffc107">
                {formatCurrency(dashboardData.total.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')}
              </StatValue>
              <StatLabel>{t('totalRevenue')}</StatLabel>
            </StatCard>
          </StatsGrid>
        )}

        <Section>
          <SectionTitle>{t('displayStatus')}</SectionTitle>
          <DisplayGrid>
            {displays.map((display) => (
              <DisplayCard key={display.id} status={display.status}>
                <DisplayNumber>
                  {t('displayNumber', { number: display.displayNumber })}
                </DisplayNumber>
                <div style={{ fontWeight: '600', marginBottom: '10px' }}>
                  {display.name}
                </div>
                <DisplayStatus status={display.status}>
                  {getStatusText(display.status)}
                </DisplayStatus>
                
                {display.status === 'occupied' && display.customerName && (
                  <CustomerInfo>
                    <div><strong>{isRTL ? 'العميل:' : 'Customer:'}</strong> {display.customerName}</div>
                    {display.timeRemaining && !display.timeRemaining.expired && (
                      <div><strong>{isRTL ? 'الوقت المتبقي:' : 'Time remaining:'}</strong> {formatTimeRemaining(display.timeRemaining.remaining)}</div>
                    )}
                  </CustomerInfo>
                )}
              </DisplayCard>
            ))}
          </DisplayGrid>
        </Section>

        {dashboardData && dashboardData.activeTransactions.length > 0 && (
          <Section>
            <SectionTitle>{t('activeTransactions')}</SectionTitle>
            <TransactionTable>
              <Table>
                <thead>
                  <tr>
                    <TableHeader isRTL={isRTL}>{t('transactionNumber')}</TableHeader>
                    <TableHeader isRTL={isRTL}>{t('customerName')}</TableHeader>
                    <TableHeader isRTL={isRTL}>{isRTL ? 'الشاشة' : 'Display'}</TableHeader>
                    <TableHeader isRTL={isRTL}>{t('amount')}</TableHeader>
                    <TableHeader isRTL={isRTL}>{t('endTime')}</TableHeader>
                  </tr>
                </thead>
                <tbody>
                  {dashboardData.activeTransactions.map((transaction) => (
                    <tr key={transaction.id}>
                      <TableCell isRTL={isRTL}>{transaction.transactionNumber}</TableCell>
                      <TableCell isRTL={isRTL}>{transaction.customerName}</TableCell>
                      <TableCell isRTL={isRTL}>{transaction.displayName}</TableCell>
                      <TableCell isRTL={isRTL}>
                        {formatCurrency(transaction.amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')}
                      </TableCell>
                      <TableCell isRTL={isRTL}>
                        {new Date(transaction.endTime).toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US')}
                      </TableCell>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </TransactionTable>
          </Section>
        )}
      </Content>
    </Container>
  );
};

export default OwnerDashboard;
