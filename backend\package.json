{"name": "display-management-backend", "version": "1.0.0", "description": "نظام إدارة الشاشات - الخادم الخلفي", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "nodemailer": "^6.9.7", "sqlite3": "^5.1.6", "stripe": "^14.7.0", "socket.io": "^4.7.4", "uuid": "^9.0.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/compression": "^1.7.5", "@types/node": "^20.10.4", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["display", "management", "typescript", "express", "socket.io"], "author": "Display Management System", "license": "MIT"}