{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\PaymentPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { transactionService, paymentService } from '../services/api';\nimport { formatCurrency } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n`;\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n_c = Container;\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n`;\n_c2 = Card;\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 30px;\n`;\n_c3 = Title;\nconst TransactionSummary = styled.div`\n  background: #f8f9fa;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n`;\n_c4 = TransactionSummary;\nconst SummaryRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  font-size: 1.1rem;\n\n  &:last-child {\n    margin-bottom: 0;\n    padding-top: 15px;\n    border-top: 2px solid #ddd;\n    font-weight: bold;\n    font-size: 1.3rem;\n    color: #667eea;\n  }\n`;\n_c5 = SummaryRow;\nconst PaymentMethods = styled.div`\n  display: grid;\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n_c6 = PaymentMethods;\nconst PaymentMethod = styled.button`\n  background: ${props => props.selected ? '#667eea' : 'white'};\n  color: ${props => props.selected ? 'white' : '#333'};\n  border: 3px solid ${props => props.selected ? '#667eea' : '#ddd'};\n  padding: 20px;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1.1rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props => props.selected ? '#5a6fd8' : '#f8f9ff'};\n  }\n`;\n_c7 = PaymentMethod;\nconst NFCReader = styled.div`\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  padding: 40px;\n  border-radius: 20px;\n  margin: 20px 0;\n  animation: ${props => props.active ? pulse : 'none'} 2s infinite;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${spin} 3s linear infinite;\n  }\n`;\n_c8 = NFCReader;\nconst NFCIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 15px;\n  position: relative;\n  z-index: 1;\n`;\n_c9 = NFCIcon;\nconst NFCText = styled.div`\n  font-size: 1.3rem;\n  font-weight: 600;\n  position: relative;\n  z-index: 1;\n`;\n_c0 = NFCText;\nconst ProcessingSpinner = styled.div`\n  width: 50px;\n  height: 50px;\n  border: 5px solid #f3f3f3;\n  border-top: 5px solid #667eea;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin: 20px auto;\n`;\n_c1 = ProcessingSpinner;\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px 40px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n  margin: 0 10px;\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n_c10 = Button;\nconst CancelButton = styled(Button)`\n  background: #dc3545;\n  \n  &:hover:not(:disabled) {\n    background: #c82333;\n    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);\n  }\n`;\n_c11 = CancelButton;\nconst SuccessMessage = styled.div`\n  background: #28a745;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 20px 0;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;\n_c12 = SuccessMessage;\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n`;\n_c13 = ErrorMessage;\nconst PaymentPage = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [paymentMethod, setPaymentMethod] = useState('nfc');\n  const [paymentStatus, setPaymentStatus] = useState('idle'); // idle, processing, success, failed\n  const [transaction, setTransaction] = useState(null);\n  const [error, setError] = useState(null);\n  const isRTL = i18n.language === 'ar';\n  const {\n    selectedDisplay,\n    customer,\n    customerName,\n    amount,\n    duration\n  } = location.state || {};\n  useEffect(() => {\n    // التحقق من وجود البيانات المطلوبة\n    if (!selectedDisplay || !customer || !customerName) {\n      navigate('/select-display');\n      return;\n    }\n\n    // إنشاء المعاملة\n    createTransaction();\n  }, [selectedDisplay, customer, customerName, amount, duration, navigate]);\n  const createTransaction = async () => {\n    try {\n      const response = await transactionService.createTransaction(selectedDisplay.id, customerName, amount, duration);\n      if (response.success) {\n        setTransaction(response.data);\n      } else {\n        setError(response.message || 'فشل في إنشاء المعاملة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error creating transaction:', error);\n    }\n  };\n  const handleNFCPayment = async () => {\n    if (!transaction) return;\n    setPaymentStatus('processing');\n    setError(null);\n    try {\n      // محاكاة دفع NFC\n      const response = await paymentService.simulateNFCPayment(transaction.transactionId);\n      if (response.success) {\n        // تأكيد المعاملة\n        const confirmResponse = await transactionService.confirmTransaction(transaction.transactionId, response.data.paymentIntentId);\n        if (confirmResponse.success) {\n          setPaymentStatus('success');\n\n          // الانتقال لصفحة النجاح بعد 3 ثوان\n          setTimeout(() => {\n            navigate('/success', {\n              state: {\n                transaction: confirmResponse.data,\n                customerName,\n                selectedDisplay\n              }\n            });\n          }, 3000);\n        } else {\n          setPaymentStatus('failed');\n          setError(confirmResponse.message || 'فشل في تأكيد المعاملة');\n        }\n      } else {\n        setPaymentStatus('failed');\n        setError(response.message || 'فشل في الدفع');\n      }\n    } catch (error) {\n      setPaymentStatus('failed');\n      setError('خطأ في معالجة الدفع');\n      console.error('Payment error:', error);\n    }\n  };\n  const handleCancel = () => {\n    navigate('/enter-name', {\n      state: {\n        selectedDisplay,\n        customer\n      }\n    });\n  };\n  if (!selectedDisplay || !customer || !customerName) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    isRTL: isRTL,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: t('payment')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TransactionSummary, {\n        children: [/*#__PURE__*/_jsxDEV(SummaryRow, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [t('customerName'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: customerName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryRow, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [t('displayNumber', {\n              number: selectedDisplay.displayNumber\n            }), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: selectedDisplay.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryRow, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [t('duration'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [Math.floor(duration / 60), \" \", t('minutes')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryRow, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [t('amount'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 19\n      }, this), paymentStatus === 'success' && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n        children: [t('paymentSuccessful'), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), isRTL ? 'جاري التحويل...' : 'Redirecting...']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this), paymentStatus === 'idle' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: t('paymentMethod')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(PaymentMethods, {\n          children: /*#__PURE__*/_jsxDEV(PaymentMethod, {\n            selected: paymentMethod === 'nfc',\n            onClick: () => setPaymentMethod('nfc'),\n            children: [\"\\uD83D\\uDCF1 \", t('nfcPayment')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), paymentMethod === 'nfc' && /*#__PURE__*/_jsxDEV(NFCReader, {\n          active: paymentStatus === 'processing',\n          children: [/*#__PURE__*/_jsxDEV(NFCIcon, {\n            children: \"\\uD83D\\uDCE1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(NFCText, {\n            children: paymentStatus === 'processing' ? t('processing') : t('placeCardOnReader')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 17\n          }, this), paymentStatus === 'processing' && /*#__PURE__*/_jsxDEV(ProcessingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 52\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '30px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleNFCPayment,\n            disabled: paymentStatus === 'processing' || !transaction,\n            children: paymentStatus === 'processing' ? t('processing') : t('confirm')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CancelButton, {\n            onClick: handleCancel,\n            disabled: paymentStatus === 'processing',\n            children: t('cancel')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), paymentStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setPaymentStatus('idle'),\n          children: t('retry')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CancelButton, {\n          onClick: handleCancel,\n          children: t('cancel')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 292,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentPage, \"nuCOIIzOpqNzn3Yjkws20H/BGzA=\", false, function () {\n  return [useTranslation, useNavigate, useLocation];\n});\n_c14 = PaymentPage;\nexport default PaymentPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"TransactionSummary\");\n$RefreshReg$(_c5, \"SummaryRow\");\n$RefreshReg$(_c6, \"PaymentMethods\");\n$RefreshReg$(_c7, \"PaymentMethod\");\n$RefreshReg$(_c8, \"NFCReader\");\n$RefreshReg$(_c9, \"NFCIcon\");\n$RefreshReg$(_c0, \"NFCText\");\n$RefreshReg$(_c1, \"ProcessingSpinner\");\n$RefreshReg$(_c10, \"Button\");\n$RefreshReg$(_c11, \"CancelButton\");\n$RefreshReg$(_c12, \"SuccessMessage\");\n$RefreshReg$(_c13, \"ErrorMessage\");\n$RefreshReg$(_c14, \"PaymentPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useLocation", "styled", "keyframes", "transactionService", "paymentService", "formatCurrency", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "pulse", "spin", "Container", "div", "props", "isRTL", "_c", "Card", "_c2", "Title", "h1", "_c3", "TransactionSummary", "_c4", "SummaryRow", "_c5", "PaymentMethods", "_c6", "PaymentMethod", "button", "selected", "_c7", "<PERSON><PERSON><PERSON><PERSON>", "active", "_c8", "NFCIcon", "_c9", "NFCText", "_c0", "ProcessingSpinner", "_c1", "<PERSON><PERSON>", "disabled", "_c10", "CancelButton", "_c11", "SuccessMessage", "_c12", "ErrorMessage", "_c13", "PaymentPage", "_s", "t", "i18n", "navigate", "location", "paymentMethod", "setPaymentMethod", "paymentStatus", "setPaymentStatus", "transaction", "setTransaction", "error", "setError", "language", "selectedDisplay", "customer", "customerName", "amount", "duration", "state", "createTransaction", "response", "id", "success", "data", "message", "console", "handleNFCPayment", "simulateNFCPayment", "transactionId", "confirmResponse", "confirmTransaction", "paymentIntentId", "setTimeout", "handleCancel", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "number", "displayNumber", "name", "Math", "floor", "style", "marginBottom", "onClick", "marginTop", "_c14", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/PaymentPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { transactionService, paymentService } from '../services/api';\nimport { formatCurrency } from '../utils/helpers';\n\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n`;\n\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 30px;\n`;\n\nconst TransactionSummary = styled.div`\n  background: #f8f9fa;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n`;\n\nconst SummaryRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  font-size: 1.1rem;\n\n  &:last-child {\n    margin-bottom: 0;\n    padding-top: 15px;\n    border-top: 2px solid #ddd;\n    font-weight: bold;\n    font-size: 1.3rem;\n    color: #667eea;\n  }\n`;\n\nconst PaymentMethods = styled.div`\n  display: grid;\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n\nconst PaymentMethod = styled.button`\n  background: ${props => props.selected ? '#667eea' : 'white'};\n  color: ${props => props.selected ? 'white' : '#333'};\n  border: 3px solid ${props => props.selected ? '#667eea' : '#ddd'};\n  padding: 20px;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1.1rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props => props.selected ? '#5a6fd8' : '#f8f9ff'};\n  }\n`;\n\nconst NFCReader = styled.div`\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  padding: 40px;\n  border-radius: 20px;\n  margin: 20px 0;\n  animation: ${props => props.active ? pulse : 'none'} 2s infinite;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${spin} 3s linear infinite;\n  }\n`;\n\nconst NFCIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 15px;\n  position: relative;\n  z-index: 1;\n`;\n\nconst NFCText = styled.div`\n  font-size: 1.3rem;\n  font-weight: 600;\n  position: relative;\n  z-index: 1;\n`;\n\nconst ProcessingSpinner = styled.div`\n  width: 50px;\n  height: 50px;\n  border: 5px solid #f3f3f3;\n  border-top: 5px solid #667eea;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin: 20px auto;\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px 40px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n  margin: 0 10px;\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst CancelButton = styled(Button)`\n  background: #dc3545;\n  \n  &:hover:not(:disabled) {\n    background: #c82333;\n    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);\n  }\n`;\n\nconst SuccessMessage = styled.div`\n  background: #28a745;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 20px 0;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n`;\n\nconst PaymentPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const [paymentMethod, setPaymentMethod] = useState('nfc');\n  const [paymentStatus, setPaymentStatus] = useState('idle'); // idle, processing, success, failed\n  const [transaction, setTransaction] = useState(null);\n  const [error, setError] = useState(null);\n\n  const isRTL = i18n.language === 'ar';\n  const { selectedDisplay, customer, customerName, amount, duration } = location.state || {};\n\n  useEffect(() => {\n    // التحقق من وجود البيانات المطلوبة\n    if (!selectedDisplay || !customer || !customerName) {\n      navigate('/select-display');\n      return;\n    }\n\n    // إنشاء المعاملة\n    createTransaction();\n  }, [selectedDisplay, customer, customerName, amount, duration, navigate]);\n\n  const createTransaction = async () => {\n    try {\n      const response = await transactionService.createTransaction(\n        selectedDisplay.id,\n        customerName,\n        amount,\n        duration\n      );\n\n      if (response.success) {\n        setTransaction(response.data);\n      } else {\n        setError(response.message || 'فشل في إنشاء المعاملة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error creating transaction:', error);\n    }\n  };\n\n  const handleNFCPayment = async () => {\n    if (!transaction) return;\n\n    setPaymentStatus('processing');\n    setError(null);\n\n    try {\n      // محاكاة دفع NFC\n      const response = await paymentService.simulateNFCPayment(transaction.transactionId);\n      \n      if (response.success) {\n        // تأكيد المعاملة\n        const confirmResponse = await transactionService.confirmTransaction(\n          transaction.transactionId,\n          response.data.paymentIntentId\n        );\n\n        if (confirmResponse.success) {\n          setPaymentStatus('success');\n          \n          // الانتقال لصفحة النجاح بعد 3 ثوان\n          setTimeout(() => {\n            navigate('/success', {\n              state: {\n                transaction: confirmResponse.data,\n                customerName,\n                selectedDisplay\n              }\n            });\n          }, 3000);\n        } else {\n          setPaymentStatus('failed');\n          setError(confirmResponse.message || 'فشل في تأكيد المعاملة');\n        }\n      } else {\n        setPaymentStatus('failed');\n        setError(response.message || 'فشل في الدفع');\n      }\n    } catch (error) {\n      setPaymentStatus('failed');\n      setError('خطأ في معالجة الدفع');\n      console.error('Payment error:', error);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/enter-name', {\n      state: { selectedDisplay, customer }\n    });\n  };\n\n  if (!selectedDisplay || !customer || !customerName) {\n    return null;\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Title>{t('payment')}</Title>\n\n        <TransactionSummary>\n          <SummaryRow>\n            <span>{t('customerName')}:</span>\n            <span>{customerName}</span>\n          </SummaryRow>\n          <SummaryRow>\n            <span>{t('displayNumber', { number: selectedDisplay.displayNumber })}:</span>\n            <span>{selectedDisplay.name}</span>\n          </SummaryRow>\n          <SummaryRow>\n            <span>{t('duration')}:</span>\n            <span>{Math.floor(duration / 60)} {t('minutes')}</span>\n          </SummaryRow>\n          <SummaryRow>\n            <span>{t('amount')}:</span>\n            <span>{formatCurrency(amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')}</span>\n          </SummaryRow>\n        </TransactionSummary>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        {paymentStatus === 'success' && (\n          <SuccessMessage>\n            {t('paymentSuccessful')}\n            <br />\n            {isRTL ? 'جاري التحويل...' : 'Redirecting...'}\n          </SuccessMessage>\n        )}\n\n        {paymentStatus === 'idle' && (\n          <>\n            <div style={{ marginBottom: '20px' }}>\n              <h3>{t('paymentMethod')}</h3>\n            </div>\n\n            <PaymentMethods>\n              <PaymentMethod\n                selected={paymentMethod === 'nfc'}\n                onClick={() => setPaymentMethod('nfc')}\n              >\n                📱 {t('nfcPayment')}\n              </PaymentMethod>\n            </PaymentMethods>\n\n            {paymentMethod === 'nfc' && (\n              <NFCReader active={paymentStatus === 'processing'}>\n                <NFCIcon>📡</NFCIcon>\n                <NFCText>\n                  {paymentStatus === 'processing' \n                    ? t('processing') \n                    : t('placeCardOnReader')\n                  }\n                </NFCText>\n                {paymentStatus === 'processing' && <ProcessingSpinner />}\n              </NFCReader>\n            )}\n\n            <div style={{ marginTop: '30px' }}>\n              <Button\n                onClick={handleNFCPayment}\n                disabled={paymentStatus === 'processing' || !transaction}\n              >\n                {paymentStatus === 'processing' ? t('processing') : t('confirm')}\n              </Button>\n              \n              <CancelButton\n                onClick={handleCancel}\n                disabled={paymentStatus === 'processing'}\n              >\n                {t('cancel')}\n              </CancelButton>\n            </div>\n          </>\n        )}\n\n        {paymentStatus === 'failed' && (\n          <div style={{ marginTop: '20px' }}>\n            <Button onClick={() => setPaymentStatus('idle')}>\n              {t('retry')}\n            </Button>\n            \n            <CancelButton onClick={handleCancel}>\n              {t('cancel')}\n            </CancelButton>\n          </div>\n        )}\n      </Card>\n    </Container>\n  );\n};\n\nexport default PaymentPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,iBAAiB;AACpE,SAASC,cAAc,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,KAAK,GAAGR,SAAS;AACvB;AACA;AACA;AACA,CAAC;AAED,MAAMS,IAAI,GAAGT,SAAS;AACtB;AACA;AACA,CAAC;AAED,MAAMU,SAAS,GAAGX,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;AACnD,CAAC;AAACC,EAAA,GARIJ,SAAS;AAUf,MAAMK,IAAI,GAAGhB,MAAM,CAACY,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GARID,IAAI;AAUV,MAAME,KAAK,GAAGlB,MAAM,CAACmB,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,kBAAkB,GAAGrB,MAAM,CAACY,GAAG;AACrC;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,kBAAkB;AAOxB,MAAME,UAAU,GAAGvB,MAAM,CAACY,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAfID,UAAU;AAiBhB,MAAME,cAAc,GAAGzB,MAAM,CAACY,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,cAAc;AAMpB,MAAME,aAAa,GAAG3B,MAAM,CAAC4B,MAAM;AACnC,gBAAgBf,KAAK,IAAIA,KAAK,CAACgB,QAAQ,GAAG,SAAS,GAAG,OAAO;AAC7D,WAAWhB,KAAK,IAAIA,KAAK,CAACgB,QAAQ,GAAG,OAAO,GAAG,MAAM;AACrD,sBAAsBhB,KAAK,IAAIA,KAAK,CAACgB,QAAQ,GAAG,SAAS,GAAG,MAAM;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBhB,KAAK,IAAIA,KAAK,CAACgB,QAAQ,GAAG,SAAS,GAAG,SAAS;AACjE;AACA,CAAC;AAACC,GAAA,GAnBIH,aAAa;AAqBnB,MAAMI,SAAS,GAAG/B,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACmB,MAAM,GAAGvB,KAAK,GAAG,MAAM;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBC,IAAI;AACrB;AACA,CAAC;AAACuB,GAAA,GApBIF,SAAS;AAsBf,MAAMG,OAAO,GAAGlC,MAAM,CAACY,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GALID,OAAO;AAOb,MAAME,OAAO,GAAGpC,MAAM,CAACY,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GALID,OAAO;AAOb,MAAME,iBAAiB,GAAGtC,MAAM,CAACY,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,eAAeF,IAAI;AACnB;AACA,CAAC;AAAC6B,GAAA,GARID,iBAAiB;AAUvB,MAAME,MAAM,GAAGxC,MAAM,CAAC4B,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaf,KAAK,IAAIA,KAAK,CAAC4B,QAAQ,GAAG,GAAG,GAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GArBIF,MAAM;AAuBZ,MAAMG,YAAY,GAAG3C,MAAM,CAACwC,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,IAAA,GAPID,YAAY;AASlB,MAAME,cAAc,GAAG7C,MAAM,CAACY,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GARID,cAAc;AAUpB,MAAME,YAAY,GAAG/C,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GANID,YAAY;AAQlB,MAAME,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGvD,cAAc,CAAC,CAAC;EACpC,MAAMwD,QAAQ,GAAGvD,WAAW,CAAC,CAAC;EAC9B,MAAMwD,QAAQ,GAAGvD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMmB,KAAK,GAAGsC,IAAI,CAACW,QAAQ,KAAK,IAAI;EACpC,MAAM;IAAEC,eAAe;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGd,QAAQ,CAACe,KAAK,IAAI,CAAC,CAAC;EAE1FzE,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACoE,eAAe,IAAI,CAACC,QAAQ,IAAI,CAACC,YAAY,EAAE;MAClDb,QAAQ,CAAC,iBAAiB,CAAC;MAC3B;IACF;;IAEA;IACAiB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACN,eAAe,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,EAAEf,QAAQ,CAAC,CAAC;EAEzE,MAAMiB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrE,kBAAkB,CAACoE,iBAAiB,CACzDN,eAAe,CAACQ,EAAE,EAClBN,YAAY,EACZC,MAAM,EACNC,QACF,CAAC;MAED,IAAIG,QAAQ,CAACE,OAAO,EAAE;QACpBb,cAAc,CAACW,QAAQ,CAACG,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLZ,QAAQ,CAACS,QAAQ,CAACI,OAAO,IAAI,uBAAuB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdC,QAAQ,CAAC,eAAe,CAAC;MACzBc,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMgB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAClB,WAAW,EAAE;IAElBD,gBAAgB,CAAC,YAAY,CAAC;IAC9BI,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMS,QAAQ,GAAG,MAAMpE,cAAc,CAAC2E,kBAAkB,CAACnB,WAAW,CAACoB,aAAa,CAAC;MAEnF,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMO,eAAe,GAAG,MAAM9E,kBAAkB,CAAC+E,kBAAkB,CACjEtB,WAAW,CAACoB,aAAa,EACzBR,QAAQ,CAACG,IAAI,CAACQ,eAChB,CAAC;QAED,IAAIF,eAAe,CAACP,OAAO,EAAE;UAC3Bf,gBAAgB,CAAC,SAAS,CAAC;;UAE3B;UACAyB,UAAU,CAAC,MAAM;YACf9B,QAAQ,CAAC,UAAU,EAAE;cACnBgB,KAAK,EAAE;gBACLV,WAAW,EAAEqB,eAAe,CAACN,IAAI;gBACjCR,YAAY;gBACZF;cACF;YACF,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLN,gBAAgB,CAAC,QAAQ,CAAC;UAC1BI,QAAQ,CAACkB,eAAe,CAACL,OAAO,IAAI,uBAAuB,CAAC;QAC9D;MACF,CAAC,MAAM;QACLjB,gBAAgB,CAAC,QAAQ,CAAC;QAC1BI,QAAQ,CAACS,QAAQ,CAACI,OAAO,IAAI,cAAc,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdH,gBAAgB,CAAC,QAAQ,CAAC;MAC1BI,QAAQ,CAAC,qBAAqB,CAAC;MAC/Bc,OAAO,CAACf,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzB/B,QAAQ,CAAC,aAAa,EAAE;MACtBgB,KAAK,EAAE;QAAEL,eAAe;QAAEC;MAAS;IACrC,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACD,eAAe,IAAI,CAACC,QAAQ,IAAI,CAACC,YAAY,EAAE;IAClD,OAAO,IAAI;EACb;EAEA,oBACE5D,OAAA,CAACK,SAAS;IAACG,KAAK,EAAEA,KAAM;IAAAuE,QAAA,eACtB/E,OAAA,CAACU,IAAI;MAAAqE,QAAA,gBACH/E,OAAA,CAACY,KAAK;QAAAmE,QAAA,EAAElC,CAAC,CAAC,SAAS;MAAC;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAE7BnF,OAAA,CAACe,kBAAkB;QAAAgE,QAAA,gBACjB/E,OAAA,CAACiB,UAAU;UAAA8D,QAAA,gBACT/E,OAAA;YAAA+E,QAAA,GAAOlC,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnF,OAAA;YAAA+E,QAAA,EAAOnB;UAAY;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACbnF,OAAA,CAACiB,UAAU;UAAA8D,QAAA,gBACT/E,OAAA;YAAA+E,QAAA,GAAOlC,CAAC,CAAC,eAAe,EAAE;cAAEuC,MAAM,EAAE1B,eAAe,CAAC2B;YAAc,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7EnF,OAAA;YAAA+E,QAAA,EAAOrB,eAAe,CAAC4B;UAAI;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACbnF,OAAA,CAACiB,UAAU;UAAA8D,QAAA,gBACT/E,OAAA;YAAA+E,QAAA,GAAOlC,CAAC,CAAC,UAAU,CAAC,EAAC,GAAC;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BnF,OAAA;YAAA+E,QAAA,GAAOQ,IAAI,CAACC,KAAK,CAAC1B,QAAQ,GAAG,EAAE,CAAC,EAAC,GAAC,EAACjB,CAAC,CAAC,SAAS,CAAC;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACbnF,OAAA,CAACiB,UAAU;UAAA8D,QAAA,gBACT/E,OAAA;YAAA+E,QAAA,GAAOlC,CAAC,CAAC,QAAQ,CAAC,EAAC,GAAC;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BnF,OAAA;YAAA+E,QAAA,EAAOjF,cAAc,CAAC+D,MAAM,EAAE,KAAK,EAAErD,KAAK,GAAG,OAAO,GAAG,OAAO;UAAC;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAEpB5B,KAAK,iBAAIvD,OAAA,CAACyC,YAAY;QAAAsC,QAAA,EAAExB;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,EAE7ChC,aAAa,KAAK,SAAS,iBAC1BnD,OAAA,CAACuC,cAAc;QAAAwC,QAAA,GACZlC,CAAC,CAAC,mBAAmB,CAAC,eACvB7C,OAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACL3E,KAAK,GAAG,iBAAiB,GAAG,gBAAgB;MAAA;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACjB,EAEAhC,aAAa,KAAK,MAAM,iBACvBnD,OAAA,CAAAE,SAAA;QAAA6E,QAAA,gBACE/E,OAAA;UAAKyF,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAX,QAAA,eACnC/E,OAAA;YAAA+E,QAAA,EAAKlC,CAAC,CAAC,eAAe;UAAC;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAENnF,OAAA,CAACmB,cAAc;UAAA4D,QAAA,eACb/E,OAAA,CAACqB,aAAa;YACZE,QAAQ,EAAE0B,aAAa,KAAK,KAAM;YAClC0C,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAAC,KAAK,CAAE;YAAA6B,QAAA,GACxC,eACI,EAAClC,CAAC,CAAC,YAAY,CAAC;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAEhBlC,aAAa,KAAK,KAAK,iBACtBjD,OAAA,CAACyB,SAAS;UAACC,MAAM,EAAEyB,aAAa,KAAK,YAAa;UAAA4B,QAAA,gBAChD/E,OAAA,CAAC4B,OAAO;YAAAmD,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrBnF,OAAA,CAAC8B,OAAO;YAAAiD,QAAA,EACL5B,aAAa,KAAK,YAAY,GAC3BN,CAAC,CAAC,YAAY,CAAC,GACfA,CAAC,CAAC,mBAAmB;UAAC;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEnB,CAAC,EACThC,aAAa,KAAK,YAAY,iBAAInD,OAAA,CAACgC,iBAAiB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACZ,eAEDnF,OAAA;UAAKyF,KAAK,EAAE;YAAEG,SAAS,EAAE;UAAO,CAAE;UAAAb,QAAA,gBAChC/E,OAAA,CAACkC,MAAM;YACLyD,OAAO,EAAEpB,gBAAiB;YAC1BpC,QAAQ,EAAEgB,aAAa,KAAK,YAAY,IAAI,CAACE,WAAY;YAAA0B,QAAA,EAExD5B,aAAa,KAAK,YAAY,GAAGN,CAAC,CAAC,YAAY,CAAC,GAAGA,CAAC,CAAC,SAAS;UAAC;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAETnF,OAAA,CAACqC,YAAY;YACXsD,OAAO,EAAEb,YAAa;YACtB3C,QAAQ,EAAEgB,aAAa,KAAK,YAAa;YAAA4B,QAAA,EAExClC,CAAC,CAAC,QAAQ;UAAC;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA,eACN,CACH,EAEAhC,aAAa,KAAK,QAAQ,iBACzBnD,OAAA;QAAKyF,KAAK,EAAE;UAAEG,SAAS,EAAE;QAAO,CAAE;QAAAb,QAAA,gBAChC/E,OAAA,CAACkC,MAAM;UAACyD,OAAO,EAAEA,CAAA,KAAMvC,gBAAgB,CAAC,MAAM,CAAE;UAAA2B,QAAA,EAC7ClC,CAAC,CAAC,OAAO;QAAC;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAETnF,OAAA,CAACqC,YAAY;UAACsD,OAAO,EAAEb,YAAa;UAAAC,QAAA,EACjClC,CAAC,CAAC,QAAQ;QAAC;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACvC,EAAA,CAjMID,WAAW;EAAA,QACKpD,cAAc,EACjBC,WAAW,EACXC,WAAW;AAAA;AAAAoG,IAAA,GAHxBlD,WAAW;AAmMjB,eAAeA,WAAW;AAAC,IAAAlC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAmD,IAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}