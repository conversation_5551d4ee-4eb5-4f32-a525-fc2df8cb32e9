import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import styled, { keyframes } from 'styled-components';
import { transactionService, paymentService } from '../services/api';
import { formatCurrency } from '../utils/helpers';

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Card = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  text-align: center;
`;

const Title = styled.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 30px;
`;

const TransactionSummary = styled.div`
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
`;

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 1.1rem;

  &:last-child {
    margin-bottom: 0;
    padding-top: 15px;
    border-top: 2px solid #ddd;
    font-weight: bold;
    font-size: 1.3rem;
    color: #667eea;
  }
`;

const PaymentMethods = styled.div`
  display: grid;
  gap: 15px;
  margin-bottom: 30px;
`;

const PaymentMethod = styled.button`
  background: ${props => props.selected ? '#667eea' : 'white'};
  color: ${props => props.selected ? 'white' : '#333'};
  border: 3px solid ${props => props.selected ? '#667eea' : '#ddd'};
  padding: 20px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;

  &:hover {
    border-color: #667eea;
    background: ${props => props.selected ? '#5a6fd8' : '#f8f9ff'};
  }
`;

const NFCReader = styled.div`
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 40px;
  border-radius: 20px;
  margin: 20px 0;
  animation: ${props => props.active ? pulse : 'none'} 2s infinite;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: ${spin} 3s linear infinite;
  }
`;

const NFCIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
`;

const NFCText = styled.div`
  font-size: 1.3rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
`;

const ProcessingSpinner = styled.div`
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #667eea;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin: 20px auto;
`;

const Button = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px 40px;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${props => props.disabled ? 0.6 : 1};
  margin: 0 10px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`;

const CancelButton = styled(Button)`
  background: #dc3545;
  
  &:hover:not(:disabled) {
    background: #c82333;
    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
  }
`;

const SuccessMessage = styled.div`
  background: #28a745;
  color: white;
  padding: 20px;
  border-radius: 15px;
  margin: 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
`;

const PaymentPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [paymentMethod, setPaymentMethod] = useState('nfc');
  const [paymentStatus, setPaymentStatus] = useState('idle'); // idle, processing, success, failed
  const [transaction, setTransaction] = useState(null);
  const [error, setError] = useState(null);

  const isRTL = i18n.language === 'ar';
  const { selectedDisplay, customer, customerName, amount, duration } = location.state || {};

  useEffect(() => {
    // التحقق من وجود البيانات المطلوبة
    if (!selectedDisplay || !customer || !customerName) {
      navigate('/select-display');
      return;
    }

    // إنشاء المعاملة
    createTransaction();
  }, [selectedDisplay, customer, customerName, amount, duration, navigate]);

  const createTransaction = async () => {
    try {
      const response = await transactionService.createTransaction(
        selectedDisplay.id,
        customerName,
        amount,
        duration
      );

      if (response.success) {
        setTransaction(response.data);
      } else {
        setError(response.message || 'فشل في إنشاء المعاملة');
      }
    } catch (error) {
      setError('خطأ في الشبكة');
      console.error('Error creating transaction:', error);
    }
  };

  const handleNFCPayment = async () => {
    if (!transaction) return;

    setPaymentStatus('processing');
    setError(null);

    try {
      // محاكاة دفع NFC
      const response = await paymentService.simulateNFCPayment(transaction.transactionId);
      
      if (response.success) {
        // تأكيد المعاملة
        const confirmResponse = await transactionService.confirmTransaction(
          transaction.transactionId,
          response.data.paymentIntentId
        );

        if (confirmResponse.success) {
          setPaymentStatus('success');
          
          // الانتقال لصفحة النجاح بعد 3 ثوان
          setTimeout(() => {
            navigate('/success', {
              state: {
                transaction: confirmResponse.data,
                customerName,
                selectedDisplay
              }
            });
          }, 3000);
        } else {
          setPaymentStatus('failed');
          setError(confirmResponse.message || 'فشل في تأكيد المعاملة');
        }
      } else {
        setPaymentStatus('failed');
        setError(response.message || 'فشل في الدفع');
      }
    } catch (error) {
      setPaymentStatus('failed');
      setError('خطأ في معالجة الدفع');
      console.error('Payment error:', error);
    }
  };

  const handleCancel = () => {
    navigate('/enter-name', {
      state: { selectedDisplay, customer }
    });
  };

  if (!selectedDisplay || !customer || !customerName) {
    return null;
  }

  return (
    <Container isRTL={isRTL}>
      <Card>
        <Title>{t('payment')}</Title>

        <TransactionSummary>
          <SummaryRow>
            <span>{t('customerName')}:</span>
            <span>{customerName}</span>
          </SummaryRow>
          <SummaryRow>
            <span>{t('displayNumber', { number: selectedDisplay.displayNumber })}:</span>
            <span>{selectedDisplay.name}</span>
          </SummaryRow>
          <SummaryRow>
            <span>{t('duration')}:</span>
            <span>{Math.floor(duration / 60)} {t('minutes')}</span>
          </SummaryRow>
          <SummaryRow>
            <span>{t('amount')}:</span>
            <span>{formatCurrency(amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')}</span>
          </SummaryRow>
        </TransactionSummary>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        {paymentStatus === 'success' && (
          <SuccessMessage>
            {t('paymentSuccessful')}
            <br />
            {isRTL ? 'جاري التحويل...' : 'Redirecting...'}
          </SuccessMessage>
        )}

        {paymentStatus === 'idle' && (
          <>
            <div style={{ marginBottom: '20px' }}>
              <h3>{t('paymentMethod')}</h3>
            </div>

            <PaymentMethods>
              <PaymentMethod
                selected={paymentMethod === 'nfc'}
                onClick={() => setPaymentMethod('nfc')}
              >
                📱 {t('nfcPayment')}
              </PaymentMethod>
            </PaymentMethods>

            {paymentMethod === 'nfc' && (
              <NFCReader active={paymentStatus === 'processing'}>
                <NFCIcon>📡</NFCIcon>
                <NFCText>
                  {paymentStatus === 'processing' 
                    ? t('processing') 
                    : t('placeCardOnReader')
                  }
                </NFCText>
                {paymentStatus === 'processing' && <ProcessingSpinner />}
              </NFCReader>
            )}

            <div style={{ marginTop: '30px' }}>
              <Button
                onClick={handleNFCPayment}
                disabled={paymentStatus === 'processing' || !transaction}
              >
                {paymentStatus === 'processing' ? t('processing') : t('confirm')}
              </Button>
              
              <CancelButton
                onClick={handleCancel}
                disabled={paymentStatus === 'processing'}
              >
                {t('cancel')}
              </CancelButton>
            </div>
          </>
        )}

        {paymentStatus === 'failed' && (
          <div style={{ marginTop: '20px' }}>
            <Button onClick={() => setPaymentStatus('idle')}>
              {t('retry')}
            </Button>
            
            <CancelButton onClick={handleCancel}>
              {t('cancel')}
            </CancelButton>
          </div>
        )}
      </Card>
    </Container>
  );
};

export default PaymentPage;
