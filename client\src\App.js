import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuth';
import './utils/i18n';
import './App.css';

// استيراد الصفحات
import HomePage from './pages/HomePage';
import DisplaySelectionPage from './pages/DisplaySelectionPage';
import LoginPage from './pages/LoginPage';
import NameInputPage from './pages/NameInputPage';
import PaymentPage from './pages/PaymentPage';
import SuccessPage from './pages/SuccessPage';
import OwnerLoginPage from './pages/OwnerLoginPage';
import OwnerDashboard from './pages/OwnerDashboard';
import DisplayScreen from './pages/DisplayScreen';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/select-display" element={<DisplaySelectionPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/enter-name" element={<NameInputPage />} />
            <Route path="/payment" element={<PaymentPage />} />
            <Route path="/success" element={<SuccessPage />} />
            <Route path="/owner-login" element={<OwnerLoginPage />} />
            <Route path="/owner-dashboard" element={<OwnerDashboard />} />
            <Route path="/display/:displayId" element={<DisplayScreen />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
