import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useOwnerAuth } from '../hooks/useAuth';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Card = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 450px;
  width: 100%;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const Logo = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
`;

const Title = styled.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
`;

const Subtitle = styled.p`
  font-size: 1rem;
  color: #666;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 600;
  color: #333;
  font-size: 1rem;
`;

const Input = styled.input`
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  &:invalid {
    border-color: #dc3545;
  }
`;

const Button = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${props => props.disabled ? 0.6 : 1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`;

const BackButton = styled.button`
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 10px;

  &:hover {
    background: #667eea;
    color: white;
  }
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`;

const InfoBox = styled.div`
  background: #e3f2fd;
  color: #1976d2;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  text-align: center;
`;

const OwnerLoginPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { loading, error, ownerLogin } = useOwnerAuth();
  
  const [password, setPassword] = useState('');

  const isRTL = i18n.language === 'ar';

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!password.trim()) {
      return;
    }

    const response = await ownerLogin(password);
    
    if (response) {
      // الانتقال للوحة تحكم المالك
      navigate('/owner-dashboard');
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <Container isRTL={isRTL}>
      <Card>
        <Header>
          <Logo>👤</Logo>
          <Title>
            {isRTL ? 'دخول المالك' : 'Owner Login'}
          </Title>
          <Subtitle>
            {isRTL ? 'لوحة تحكم النظام' : 'System Control Panel'}
          </Subtitle>
        </Header>

        <InfoBox>
          {isRTL ? 
            'كلمة المرور الافتراضية: admin123' :
            'Default password: admin123'
          }
        </InfoBox>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <Form onSubmit={handleSubmit}>
          <InputGroup>
            <Label>
              {isRTL ? 'كلمة المرور' : 'Password'}
            </Label>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder={isRTL ? 'أدخل كلمة المرور' : 'Enter password'}
              required
            />
          </InputGroup>

          <Button type="submit" disabled={loading || !password.trim()}>
            {loading ? 
              (isRTL ? 'جاري التحقق...' : 'Verifying...') : 
              (isRTL ? 'دخول' : 'Login')
            }
          </Button>
        </Form>

        <BackButton onClick={handleBack}>
          {t('back')}
        </BackButton>
      </Card>
    </Container>
  );
};

export default OwnerLoginPage;
