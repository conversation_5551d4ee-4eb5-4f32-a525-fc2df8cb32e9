{"version": 3, "file": "static/css/main.a085e297.css", "mappings": "kIAAA,EAGE,qBAAsB,CADtB,SAEF,CAEA,OALE,QAaF,CARA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,iJAMF,CAEA,KACE,uEAEF,CAMA,UACE,aAAc,CACd,gBACF,CAEA,UACE,aAAc,CACd,eACF,CAGA,EAEE,uBACF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CCzDA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',\n    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    'Cairo', '<PERSON><PERSON><PERSON>', sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* دعم الخطوط العربية */\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap');\n\n/* إعدادات RTL */\n[dir=\"rtl\"] {\n  direction: rtl;\n  text-align: right;\n}\n\n[dir=\"ltr\"] {\n  direction: ltr;\n  text-align: left;\n}\n\n/* تحسين الأداء للأنيميشن */\n* {\n  -webkit-transform: translateZ(0);\n  transform: translateZ(0);\n}\n\n/* إخفاء scrollbar في webkit browsers */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n"], "names": [], "sourceRoot": ""}