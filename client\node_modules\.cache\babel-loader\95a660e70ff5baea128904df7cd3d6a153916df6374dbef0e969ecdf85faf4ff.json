{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n_c = Container;\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  max-width: 500px;\n  width: 100%;\n  margin: 20px;\n`;\n_c2 = Card;\nconst Logo = styled.div`\n  width: 120px;\n  height: 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  margin: 0 auto 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48px;\n  color: white;\n  font-weight: bold;\n`;\n_c3 = Logo;\nconst Title = styled.h1`\n  font-size: 2.5rem;\n  color: #333;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;\n_c4 = Title;\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 40px;\n`;\n_c5 = Subtitle;\nconst LanguageSelector = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  margin-bottom: 30px;\n`;\n_c6 = LanguageSelector;\nconst LanguageButton = styled.button`\n  padding: 10px 20px;\n  border: 2px solid ${props => props.active ? '#667eea' : '#ddd'};\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#333'};\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props => props.active ? '#5a6fd8' : '#f8f9ff'};\n  }\n`;\n_c7 = LanguageButton;\nconst StartButton = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 40px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  border-radius: 50px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;\n_c8 = StartButton;\nconst OwnerLoginLink = styled.button`\n  background: none;\n  border: none;\n  color: #666;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-top: 20px;\n  text-decoration: underline;\n\n  &:hover {\n    color: #333;\n  }\n`;\n_c9 = OwnerLoginLink;\nconst HomePage = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigate = useNavigate();\n  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);\n  const isRTL = currentLanguage === 'ar';\n  const handleLanguageChange = language => {\n    setCurrentLanguage(language);\n    i18n.changeLanguage(language);\n    localStorage.setItem('language', language);\n  };\n  const handleStartTransaction = () => {\n    navigate('/select-display');\n  };\n  const handleOwnerLogin = () => {\n    navigate('/owner-login');\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    isRTL: isRTL,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: \"\\uD83D\\uDCFA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: t('companyName')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n        children: t('welcome')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginBottom: '15px',\n            color: '#666',\n            fontSize: '1rem'\n          },\n          children: t('selectLanguage')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LanguageSelector, {\n          children: [/*#__PURE__*/_jsxDEV(LanguageButton, {\n            active: currentLanguage === 'ar',\n            onClick: () => handleLanguageChange('ar'),\n            children: \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LanguageButton, {\n            active: currentLanguage === 'en',\n            onClick: () => handleLanguageChange('en'),\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StartButton, {\n        onClick: handleStartTransaction,\n        children: t('startTransaction')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(OwnerLoginLink, {\n        onClick: handleOwnerLogin,\n        children: currentLanguage === 'ar' ? 'دخول المالك' : 'Owner Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"ZufbWIKIgBEATDjCvA9MNXt+dmU=\", false, function () {\n  return [useTranslation, useNavigate];\n});\n_c0 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Subtitle\");\n$RefreshReg$(_c6, \"LanguageSelector\");\n$RefreshReg$(_c7, \"LanguageButton\");\n$RefreshReg$(_c8, \"StartButton\");\n$RefreshReg$(_c9, \"OwnerLoginLink\");\n$RefreshReg$(_c0, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useNavigate", "styled", "jsxDEV", "_jsxDEV", "Container", "div", "props", "isRTL", "_c", "Card", "_c2", "Logo", "_c3", "Title", "h1", "_c4", "Subtitle", "p", "_c5", "LanguageSelector", "_c6", "LanguageButton", "button", "active", "_c7", "StartButton", "_c8", "OwnerLoginLink", "_c9", "HomePage", "_s", "t", "i18n", "navigate", "currentLanguage", "setCurrentLanguage", "language", "handleLanguageChange", "changeLanguage", "localStorage", "setItem", "handleStartTransaction", "handleOwnerLogin", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "color", "fontSize", "onClick", "_c0", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  max-width: 500px;\n  width: 100%;\n  margin: 20px;\n`;\n\nconst Logo = styled.div`\n  width: 120px;\n  height: 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  margin: 0 auto 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48px;\n  color: white;\n  font-weight: bold;\n`;\n\nconst Title = styled.h1`\n  font-size: 2.5rem;\n  color: #333;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 40px;\n`;\n\nconst LanguageSelector = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  margin-bottom: 30px;\n`;\n\nconst LanguageButton = styled.button`\n  padding: 10px 20px;\n  border: 2px solid ${props => props.active ? '#667eea' : '#ddd'};\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#333'};\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props => props.active ? '#5a6fd8' : '#f8f9ff'};\n  }\n`;\n\nconst StartButton = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 40px;\n  font-size: 1.3rem;\n  font-weight: 600;\n  border-radius: 50px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nconst OwnerLoginLink = styled.button`\n  background: none;\n  border: none;\n  color: #666;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-top: 20px;\n  text-decoration: underline;\n\n  &:hover {\n    color: #333;\n  }\n`;\n\nconst HomePage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);\n\n  const isRTL = currentLanguage === 'ar';\n\n  const handleLanguageChange = (language) => {\n    setCurrentLanguage(language);\n    i18n.changeLanguage(language);\n    localStorage.setItem('language', language);\n  };\n\n  const handleStartTransaction = () => {\n    navigate('/select-display');\n  };\n\n  const handleOwnerLogin = () => {\n    navigate('/owner-login');\n  };\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Logo>\n          📺\n        </Logo>\n        \n        <Title>{t('companyName')}</Title>\n        <Subtitle>{t('welcome')}</Subtitle>\n        \n        <div>\n          <p style={{ marginBottom: '15px', color: '#666', fontSize: '1rem' }}>\n            {t('selectLanguage')}\n          </p>\n          <LanguageSelector>\n            <LanguageButton\n              active={currentLanguage === 'ar'}\n              onClick={() => handleLanguageChange('ar')}\n            >\n              العربية\n            </LanguageButton>\n            <LanguageButton\n              active={currentLanguage === 'en'}\n              onClick={() => handleLanguageChange('en')}\n            >\n              English\n            </LanguageButton>\n          </LanguageSelector>\n        </div>\n        \n        <StartButton onClick={handleStartTransaction}>\n          {t('startTransaction')}\n        </StartButton>\n        \n        <OwnerLoginLink onClick={handleOwnerLogin}>\n          {currentLanguage === 'ar' ? 'دخول المالك' : 'Owner Login'}\n        </OwnerLoginLink>\n      </Card>\n    </Container>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,SAAS,GAAGH,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;AACnD,CAAC;AAACC,EAAA,GATIJ,SAAS;AAWf,MAAMK,IAAI,GAAGR,MAAM,CAACI,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GATID,IAAI;AAWV,MAAME,IAAI,GAAGV,MAAM,CAACI,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAZID,IAAI;AAcV,MAAME,KAAK,GAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,QAAQ,GAAGf,MAAM,CAACgB,CAAC;AACzB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,QAAQ;AAMd,MAAMG,gBAAgB,GAAGlB,MAAM,CAACI,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GALID,gBAAgB;AAOtB,MAAME,cAAc,GAAGpB,MAAM,CAACqB,MAAM;AACpC;AACA,sBAAsBhB,KAAK,IAAIA,KAAK,CAACiB,MAAM,GAAG,SAAS,GAAG,MAAM;AAChE,gBAAgBjB,KAAK,IAAIA,KAAK,CAACiB,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWjB,KAAK,IAAIA,KAAK,CAACiB,MAAM,GAAG,OAAO,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBjB,KAAK,IAAIA,KAAK,CAACiB,MAAM,GAAG,SAAS,GAAG,SAAS;AAC/D;AACA,CAAC;AAACC,GAAA,GAfIH,cAAc;AAiBpB,MAAMI,WAAW,GAAGxB,MAAM,CAACqB,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GApBID,WAAW;AAsBjB,MAAME,cAAc,GAAG1B,MAAM,CAACqB,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAZID,cAAc;AAcpB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGjC,cAAc,CAAC,CAAC;EACpC,MAAMkC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAACkC,IAAI,CAACI,QAAQ,CAAC;EAErE,MAAM7B,KAAK,GAAG2B,eAAe,KAAK,IAAI;EAEtC,MAAMG,oBAAoB,GAAID,QAAQ,IAAK;IACzCD,kBAAkB,CAACC,QAAQ,CAAC;IAC5BJ,IAAI,CAACM,cAAc,CAACF,QAAQ,CAAC;IAC7BG,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEJ,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;IACnCR,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;IAC7BT,QAAQ,CAAC,cAAc,CAAC;EAC1B,CAAC;EAED,oBACE9B,OAAA,CAACC,SAAS;IAACG,KAAK,EAAEA,KAAM;IAAAoC,QAAA,eACtBxC,OAAA,CAACM,IAAI;MAAAkC,QAAA,gBACHxC,OAAA,CAACQ,IAAI;QAAAgC,QAAA,EAAC;MAEN;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEP5C,OAAA,CAACU,KAAK;QAAA8B,QAAA,EAAEZ,CAAC,CAAC,aAAa;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACjC5C,OAAA,CAACa,QAAQ;QAAA2B,QAAA,EAAEZ,CAAC,CAAC,SAAS;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAEnC5C,OAAA;QAAAwC,QAAA,gBACExC,OAAA;UAAG6C,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAR,QAAA,EACjEZ,CAAC,CAAC,gBAAgB;QAAC;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACJ5C,OAAA,CAACgB,gBAAgB;UAAAwB,QAAA,gBACfxC,OAAA,CAACkB,cAAc;YACbE,MAAM,EAAEW,eAAe,KAAK,IAAK;YACjCkB,OAAO,EAAEA,CAAA,KAAMf,oBAAoB,CAAC,IAAI,CAAE;YAAAM,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC,eACjB5C,OAAA,CAACkB,cAAc;YACbE,MAAM,EAAEW,eAAe,KAAK,IAAK;YACjCkB,OAAO,EAAEA,CAAA,KAAMf,oBAAoB,CAAC,IAAI,CAAE;YAAAM,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEN5C,OAAA,CAACsB,WAAW;QAAC2B,OAAO,EAAEX,sBAAuB;QAAAE,QAAA,EAC1CZ,CAAC,CAAC,kBAAkB;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEd5C,OAAA,CAACwB,cAAc;QAACyB,OAAO,EAAEV,gBAAiB;QAAAC,QAAA,EACvCT,eAAe,KAAK,IAAI,GAAG,aAAa,GAAG;MAAa;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACjB,EAAA,CA7DID,QAAQ;EAAA,QACQ9B,cAAc,EACjBC,WAAW;AAAA;AAAAqD,GAAA,GAFxBxB,QAAQ;AA+Dd,eAAeA,QAAQ;AAAC,IAAArB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}