{"ast": null, "code": "import i18n from'i18next';import{initReactI18next}from'react-i18next';// ترجمات العربية\nconst arabicTranslations={translation:{// الصفحة الرئيسية\nwelcome:'أهلاً وسهلاً بكم',companyName:'نظام إدارة الشاشات',selectLanguage:'اختر اللغة',startTransaction:'ابدأ المعاملة',// اختيار الشاشة\nselectDisplay:'اختر الشاشة',displayNumber:'شاشة {{number}}',available:'متاحة',occupied:'مشغولة',reserved:'محجوزة',maintenance:'صيانة',timeRemaining:'الوقت المتبقي: {{time}}',// تسجيل الدخول\nphoneNumber:'رقم الهاتف',email:'البريد الإلكتروني',enterPhoneNumber:'أدخل رقم الهاتف',enterEmail:'أدخل البريد الإلكتروني',sendOTP:'إرسال رمز التحقق',otpCode:'رمز التحقق',enterOTP:'أدخل رمز التحقق',verifyOTP:'تحقق من الرمز',otpSent:'تم إرسال رمز التحقق إلى بريدك الإلكتروني',// إدخال الاسم\ncustomerName:'اسم العميل',enterName:'أدخل اسمك',displayPrice:'السعر: {{price}} ريال',displayDuration:'مدة العرض: {{duration}} دقيقة',proceedToPayment:'المتابعة للدفع',// الدفع\npayment:'الدفع',paymentMethod:'طريقة الدفع',nfcPayment:'دفع بالبطاقة اللاسلكية',cardPayment:'دفع بالبطاقة',placeCardOnReader:'ضع بطاقتك على القارئ',processing:'جاري المعالجة...',paymentSuccessful:'تم الدفع بنجاح',paymentFailed:'فشل في الدفع',// المعاملة\ntransactionNumber:'رقم المعاملة',transactionDetails:'تفاصيل المعاملة',amount:'المبلغ',duration:'المدة',startTime:'وقت البداية',endTime:'وقت الانتهاء',// أزرار عامة\nnext:'التالي',back:'السابق',cancel:'إلغاء',confirm:'تأكيد',retry:'إعادة المحاولة',close:'إغلاق',// رسائل الخطأ\nerror:'خطأ',invalidPhoneNumber:'رقم الهاتف غير صحيح',invalidEmail:'البريد الإلكتروني غير صحيح',invalidOTP:'رمز التحقق غير صحيح',networkError:'خطأ في الشبكة',serverError:'خطأ في الخادم',// واجهة المالك\nownerDashboard:'لوحة تحكم المالك',statistics:'الإحصائيات',todayTransactions:'معاملات اليوم',todayRevenue:'إيرادات اليوم',totalTransactions:'إجمالي المعاملات',totalRevenue:'إجمالي الإيرادات',activeTransactions:'المعاملات النشطة',displayStatus:'حالة الشاشات',settings:'الإعدادات',reports:'التقارير',// الوقت\nminutes:'دقيقة',seconds:'ثانية',hours:'ساعة',days:'يوم'}};// ترجمات الإنجليزية\nconst englishTranslations={translation:{// Home page\nwelcome:'Welcome',companyName:'Display Management System',selectLanguage:'Select Language',startTransaction:'Start Transaction',// Display selection\nselectDisplay:'Select Display',displayNumber:'Display {{number}}',available:'Available',occupied:'Occupied',reserved:'Reserved',maintenance:'Maintenance',timeRemaining:'Time remaining: {{time}}',// Login\nphoneNumber:'Phone Number',email:'Email',enterPhoneNumber:'Enter phone number',enterEmail:'Enter email address',sendOTP:'Send Verification Code',otpCode:'Verification Code',enterOTP:'Enter verification code',verifyOTP:'Verify Code',otpSent:'Verification code sent to your email',// Name input\ncustomerName:'Customer Name',enterName:'Enter your name',displayPrice:'Price: {{price}} SAR',displayDuration:'Duration: {{duration}} minutes',proceedToPayment:'Proceed to Payment',// Payment\npayment:'Payment',paymentMethod:'Payment Method',nfcPayment:'NFC Card Payment',cardPayment:'Card Payment',placeCardOnReader:'Place your card on the reader',processing:'Processing...',paymentSuccessful:'Payment Successful',paymentFailed:'Payment Failed',// Transaction\ntransactionNumber:'Transaction Number',transactionDetails:'Transaction Details',amount:'Amount',duration:'Duration',startTime:'Start Time',endTime:'End Time',// General buttons\nnext:'Next',back:'Back',cancel:'Cancel',confirm:'Confirm',retry:'Retry',close:'Close',// Error messages\nerror:'Error',invalidPhoneNumber:'Invalid phone number',invalidEmail:'Invalid email address',invalidOTP:'Invalid verification code',networkError:'Network error',serverError:'Server error',// Owner interface\nownerDashboard:'Owner Dashboard',statistics:'Statistics',todayTransactions:\"Today's Transactions\",todayRevenue:\"Today's Revenue\",totalTransactions:'Total Transactions',totalRevenue:'Total Revenue',activeTransactions:'Active Transactions',displayStatus:'Display Status',settings:'Settings',reports:'Reports',// Time\nminutes:'minutes',seconds:'seconds',hours:'hours',days:'days'}};// إعداد i18n\ni18n.use(initReactI18next).init({resources:{ar:arabicTranslations,en:englishTranslations},lng:localStorage.getItem('language')||'ar',// اللغة الافتراضية\nfallbackLng:'ar',interpolation:{escapeValue:false// React already escapes values\n},react:{useSuspense:false}});export default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "arabicTranslations", "translation", "welcome", "companyName", "selectLanguage", "startTransaction", "selectDisplay", "displayNumber", "available", "occupied", "reserved", "maintenance", "timeRemaining", "phoneNumber", "email", "enterPhoneNumber", "enterEmail", "sendOTP", "otpCode", "enterOTP", "verifyOTP", "otpSent", "customerName", "enterName", "displayPrice", "displayDuration", "proceedToPayment", "payment", "paymentMethod", "nfcPayment", "cardPayment", "placeCardOnReader", "processing", "paymentSuccessful", "paymentFailed", "transactionNumber", "transactionDetails", "amount", "duration", "startTime", "endTime", "next", "back", "cancel", "confirm", "retry", "close", "error", "invalidPhoneNumber", "invalidEmail", "invalidOTP", "networkError", "serverError", "ownerDashboard", "statistics", "todayTransactions", "todayRevenue", "totalTransactions", "totalRevenue", "activeTransactions", "displayStatus", "settings", "reports", "minutes", "seconds", "hours", "days", "englishTranslations", "use", "init", "resources", "ar", "en", "lng", "localStorage", "getItem", "fallbackLng", "interpolation", "escapeValue", "react", "useSuspense"], "sources": ["D:/برمجة/tste 1/client/src/utils/i18n.js"], "sourcesContent": ["import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\n\n// ترجمات العربية\nconst arabicTranslations = {\n  translation: {\n    // الصفحة الرئيسية\n    welcome: 'أهلاً وسهلاً بكم',\n    companyName: 'نظام إدارة الشاشات',\n    selectLanguage: 'اختر اللغة',\n    startTransaction: 'ابدأ المعاملة',\n    \n    // اختيار الشاشة\n    selectDisplay: 'اختر الشاشة',\n    displayNumber: 'شاشة {{number}}',\n    available: 'متاحة',\n    occupied: 'مشغولة',\n    reserved: 'محجوزة',\n    maintenance: 'صيانة',\n    timeRemaining: 'الوقت المتبقي: {{time}}',\n    \n    // تسجيل الدخول\n    phoneNumber: 'رقم الهاتف',\n    email: 'البريد الإلكتروني',\n    enterPhoneNumber: 'أدخل رقم الهاتف',\n    enterEmail: 'أدخل البريد الإلكتروني',\n    sendOTP: 'إرسال رمز التحقق',\n    otpCode: 'رمز التحقق',\n    enterOTP: 'أدخل رمز التحقق',\n    verifyOTP: 'تحقق من الرمز',\n    otpSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',\n    \n    // إدخال الاسم\n    customerName: 'اسم العميل',\n    enterName: 'أدخل اسمك',\n    displayPrice: 'السعر: {{price}} ريال',\n    displayDuration: 'مدة العرض: {{duration}} دقيقة',\n    proceedToPayment: 'المتابعة للدفع',\n    \n    // الدفع\n    payment: 'الدفع',\n    paymentMethod: 'طريقة الدفع',\n    nfcPayment: 'دفع بالبطاقة اللاسلكية',\n    cardPayment: 'دفع بالبطاقة',\n    placeCardOnReader: 'ضع بطاقتك على القارئ',\n    processing: 'جاري المعالجة...',\n    paymentSuccessful: 'تم الدفع بنجاح',\n    paymentFailed: 'فشل في الدفع',\n    \n    // المعاملة\n    transactionNumber: 'رقم المعاملة',\n    transactionDetails: 'تفاصيل المعاملة',\n    amount: 'المبلغ',\n    duration: 'المدة',\n    startTime: 'وقت البداية',\n    endTime: 'وقت الانتهاء',\n    \n    // أزرار عامة\n    next: 'التالي',\n    back: 'السابق',\n    cancel: 'إلغاء',\n    confirm: 'تأكيد',\n    retry: 'إعادة المحاولة',\n    close: 'إغلاق',\n    \n    // رسائل الخطأ\n    error: 'خطأ',\n    invalidPhoneNumber: 'رقم الهاتف غير صحيح',\n    invalidEmail: 'البريد الإلكتروني غير صحيح',\n    invalidOTP: 'رمز التحقق غير صحيح',\n    networkError: 'خطأ في الشبكة',\n    serverError: 'خطأ في الخادم',\n    \n    // واجهة المالك\n    ownerDashboard: 'لوحة تحكم المالك',\n    statistics: 'الإحصائيات',\n    todayTransactions: 'معاملات اليوم',\n    todayRevenue: 'إيرادات اليوم',\n    totalTransactions: 'إجمالي المعاملات',\n    totalRevenue: 'إجمالي الإيرادات',\n    activeTransactions: 'المعاملات النشطة',\n    displayStatus: 'حالة الشاشات',\n    settings: 'الإعدادات',\n    reports: 'التقارير',\n    \n    // الوقت\n    minutes: 'دقيقة',\n    seconds: 'ثانية',\n    hours: 'ساعة',\n    days: 'يوم',\n  }\n};\n\n// ترجمات الإنجليزية\nconst englishTranslations = {\n  translation: {\n    // Home page\n    welcome: 'Welcome',\n    companyName: 'Display Management System',\n    selectLanguage: 'Select Language',\n    startTransaction: 'Start Transaction',\n    \n    // Display selection\n    selectDisplay: 'Select Display',\n    displayNumber: 'Display {{number}}',\n    available: 'Available',\n    occupied: 'Occupied',\n    reserved: 'Reserved',\n    maintenance: 'Maintenance',\n    timeRemaining: 'Time remaining: {{time}}',\n    \n    // Login\n    phoneNumber: 'Phone Number',\n    email: 'Email',\n    enterPhoneNumber: 'Enter phone number',\n    enterEmail: 'Enter email address',\n    sendOTP: 'Send Verification Code',\n    otpCode: 'Verification Code',\n    enterOTP: 'Enter verification code',\n    verifyOTP: 'Verify Code',\n    otpSent: 'Verification code sent to your email',\n    \n    // Name input\n    customerName: 'Customer Name',\n    enterName: 'Enter your name',\n    displayPrice: 'Price: {{price}} SAR',\n    displayDuration: 'Duration: {{duration}} minutes',\n    proceedToPayment: 'Proceed to Payment',\n    \n    // Payment\n    payment: 'Payment',\n    paymentMethod: 'Payment Method',\n    nfcPayment: 'NFC Card Payment',\n    cardPayment: 'Card Payment',\n    placeCardOnReader: 'Place your card on the reader',\n    processing: 'Processing...',\n    paymentSuccessful: 'Payment Successful',\n    paymentFailed: 'Payment Failed',\n    \n    // Transaction\n    transactionNumber: 'Transaction Number',\n    transactionDetails: 'Transaction Details',\n    amount: 'Amount',\n    duration: 'Duration',\n    startTime: 'Start Time',\n    endTime: 'End Time',\n    \n    // General buttons\n    next: 'Next',\n    back: 'Back',\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    retry: 'Retry',\n    close: 'Close',\n    \n    // Error messages\n    error: 'Error',\n    invalidPhoneNumber: 'Invalid phone number',\n    invalidEmail: 'Invalid email address',\n    invalidOTP: 'Invalid verification code',\n    networkError: 'Network error',\n    serverError: 'Server error',\n    \n    // Owner interface\n    ownerDashboard: 'Owner Dashboard',\n    statistics: 'Statistics',\n    todayTransactions: \"Today's Transactions\",\n    todayRevenue: \"Today's Revenue\",\n    totalTransactions: 'Total Transactions',\n    totalRevenue: 'Total Revenue',\n    activeTransactions: 'Active Transactions',\n    displayStatus: 'Display Status',\n    settings: 'Settings',\n    reports: 'Reports',\n    \n    // Time\n    minutes: 'minutes',\n    seconds: 'seconds',\n    hours: 'hours',\n    days: 'days',\n  }\n};\n\n// إعداد i18n\ni18n\n  .use(initReactI18next)\n  .init({\n    resources: {\n      ar: arabicTranslations,\n      en: englishTranslations,\n    },\n    lng: localStorage.getItem('language') || 'ar', // اللغة الافتراضية\n    fallbackLng: 'ar',\n    \n    interpolation: {\n      escapeValue: false, // React already escapes values\n    },\n    \n    react: {\n      useSuspense: false,\n    },\n  });\n\nexport default i18n;\n"], "mappings": "AAAA,MAAO,CAAAA,IAAI,KAAM,SAAS,CAC1B,OAASC,gBAAgB,KAAQ,eAAe,CAEhD;AACA,KAAM,CAAAC,kBAAkB,CAAG,CACzBC,WAAW,CAAE,CACX;AACAC,OAAO,CAAE,kBAAkB,CAC3BC,WAAW,CAAE,oBAAoB,CACjCC,cAAc,CAAE,YAAY,CAC5BC,gBAAgB,CAAE,eAAe,CAEjC;AACAC,aAAa,CAAE,aAAa,CAC5BC,aAAa,CAAE,iBAAiB,CAChCC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,QAAQ,CAClBC,WAAW,CAAE,OAAO,CACpBC,aAAa,CAAE,yBAAyB,CAExC;AACAC,WAAW,CAAE,YAAY,CACzBC,KAAK,CAAE,mBAAmB,CAC1BC,gBAAgB,CAAE,iBAAiB,CACnCC,UAAU,CAAE,wBAAwB,CACpCC,OAAO,CAAE,kBAAkB,CAC3BC,OAAO,CAAE,YAAY,CACrBC,QAAQ,CAAE,iBAAiB,CAC3BC,SAAS,CAAE,eAAe,CAC1BC,OAAO,CAAE,0CAA0C,CAEnD;AACAC,YAAY,CAAE,YAAY,CAC1BC,SAAS,CAAE,WAAW,CACtBC,YAAY,CAAE,uBAAuB,CACrCC,eAAe,CAAE,+BAA+B,CAChDC,gBAAgB,CAAE,gBAAgB,CAElC;AACAC,OAAO,CAAE,OAAO,CAChBC,aAAa,CAAE,aAAa,CAC5BC,UAAU,CAAE,wBAAwB,CACpCC,WAAW,CAAE,cAAc,CAC3BC,iBAAiB,CAAE,sBAAsB,CACzCC,UAAU,CAAE,kBAAkB,CAC9BC,iBAAiB,CAAE,gBAAgB,CACnCC,aAAa,CAAE,cAAc,CAE7B;AACAC,iBAAiB,CAAE,cAAc,CACjCC,kBAAkB,CAAE,iBAAiB,CACrCC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,aAAa,CACxBC,OAAO,CAAE,cAAc,CAEvB;AACAC,IAAI,CAAE,QAAQ,CACdC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,OAAO,CAChBC,KAAK,CAAE,gBAAgB,CACvBC,KAAK,CAAE,OAAO,CAEd;AACAC,KAAK,CAAE,KAAK,CACZC,kBAAkB,CAAE,qBAAqB,CACzCC,YAAY,CAAE,4BAA4B,CAC1CC,UAAU,CAAE,qBAAqB,CACjCC,YAAY,CAAE,eAAe,CAC7BC,WAAW,CAAE,eAAe,CAE5B;AACAC,cAAc,CAAE,kBAAkB,CAClCC,UAAU,CAAE,YAAY,CACxBC,iBAAiB,CAAE,eAAe,CAClCC,YAAY,CAAE,eAAe,CAC7BC,iBAAiB,CAAE,kBAAkB,CACrCC,YAAY,CAAE,kBAAkB,CAChCC,kBAAkB,CAAE,kBAAkB,CACtCC,aAAa,CAAE,cAAc,CAC7BC,QAAQ,CAAE,WAAW,CACrBC,OAAO,CAAE,UAAU,CAEnB;AACAC,OAAO,CAAE,OAAO,CAChBC,OAAO,CAAE,OAAO,CAChBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,KACR,CACF,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAG,CAC1BlE,WAAW,CAAE,CACX;AACAC,OAAO,CAAE,SAAS,CAClBC,WAAW,CAAE,2BAA2B,CACxCC,cAAc,CAAE,iBAAiB,CACjCC,gBAAgB,CAAE,mBAAmB,CAErC;AACAC,aAAa,CAAE,gBAAgB,CAC/BC,aAAa,CAAE,oBAAoB,CACnCC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,UAAU,CACpBC,WAAW,CAAE,aAAa,CAC1BC,aAAa,CAAE,0BAA0B,CAEzC;AACAC,WAAW,CAAE,cAAc,CAC3BC,KAAK,CAAE,OAAO,CACdC,gBAAgB,CAAE,oBAAoB,CACtCC,UAAU,CAAE,qBAAqB,CACjCC,OAAO,CAAE,wBAAwB,CACjCC,OAAO,CAAE,mBAAmB,CAC5BC,QAAQ,CAAE,yBAAyB,CACnCC,SAAS,CAAE,aAAa,CACxBC,OAAO,CAAE,sCAAsC,CAE/C;AACAC,YAAY,CAAE,eAAe,CAC7BC,SAAS,CAAE,iBAAiB,CAC5BC,YAAY,CAAE,sBAAsB,CACpCC,eAAe,CAAE,gCAAgC,CACjDC,gBAAgB,CAAE,oBAAoB,CAEtC;AACAC,OAAO,CAAE,SAAS,CAClBC,aAAa,CAAE,gBAAgB,CAC/BC,UAAU,CAAE,kBAAkB,CAC9BC,WAAW,CAAE,cAAc,CAC3BC,iBAAiB,CAAE,+BAA+B,CAClDC,UAAU,CAAE,eAAe,CAC3BC,iBAAiB,CAAE,oBAAoB,CACvCC,aAAa,CAAE,gBAAgB,CAE/B;AACAC,iBAAiB,CAAE,oBAAoB,CACvCC,kBAAkB,CAAE,qBAAqB,CACzCC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,UAAU,CACpBC,SAAS,CAAE,YAAY,CACvBC,OAAO,CAAE,UAAU,CAEnB;AACAC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,OAAO,CAEd;AACAC,KAAK,CAAE,OAAO,CACdC,kBAAkB,CAAE,sBAAsB,CAC1CC,YAAY,CAAE,uBAAuB,CACrCC,UAAU,CAAE,2BAA2B,CACvCC,YAAY,CAAE,eAAe,CAC7BC,WAAW,CAAE,cAAc,CAE3B;AACAC,cAAc,CAAE,iBAAiB,CACjCC,UAAU,CAAE,YAAY,CACxBC,iBAAiB,CAAE,sBAAsB,CACzCC,YAAY,CAAE,iBAAiB,CAC/BC,iBAAiB,CAAE,oBAAoB,CACvCC,YAAY,CAAE,eAAe,CAC7BC,kBAAkB,CAAE,qBAAqB,CACzCC,aAAa,CAAE,gBAAgB,CAC/BC,QAAQ,CAAE,UAAU,CACpBC,OAAO,CAAE,SAAS,CAElB;AACAC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,MACR,CACF,CAAC,CAED;AACApE,IAAI,CACDsE,GAAG,CAACrE,gBAAgB,CAAC,CACrBsE,IAAI,CAAC,CACJC,SAAS,CAAE,CACTC,EAAE,CAAEvE,kBAAkB,CACtBwE,EAAE,CAAEL,mBACN,CAAC,CACDM,GAAG,CAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAI,IAAI,CAAE;AAC/CC,WAAW,CAAE,IAAI,CAEjBC,aAAa,CAAE,CACbC,WAAW,CAAE,KAAO;AACtB,CAAC,CAEDC,KAAK,CAAE,CACLC,WAAW,CAAE,KACf,CACF,CAAC,CAAC,CAEJ,cAAe,CAAAlF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}