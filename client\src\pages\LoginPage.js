import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { useOTP, useAuth } from '../hooks/useAuth';
import { validateSaudiPhoneNumber, validateEmail, formatSaudiPhoneNumber } from '../utils/helpers';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Card = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const Title = styled.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
`;

const DisplayInfo = styled.div`
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  text-align: center;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 600;
  color: #333;
  font-size: 1rem;
`;

const Input = styled.input`
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  direction: ${props => props.type === 'tel' ? 'ltr' : 'inherit'};
  text-align: ${props => props.type === 'tel' ? 'left' : 'inherit'};

  &:focus {
    outline: none;
    border-color: #667eea;
  }

  &:invalid {
    border-color: #dc3545;
  }
`;

const Button = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${props => props.disabled ? 0.6 : 1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`;

const BackButton = styled.button`
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #667eea;
    color: white;
  }
`;

const ErrorMessage = styled.div`
  background: #dc3545;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`;

const SuccessMessage = styled.div`
  background: #28a745;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`;

const OTPInfo = styled.div`
  background: #e3f2fd;
  color: #1976d2;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
  font-size: 0.9rem;
`;

const LoginPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();
  const { loading, error, otpSent, sendOTP, verifyOTP, resetOTP } = useOTP();

  const [step, setStep] = useState('phone'); // 'phone' or 'otp'
  const [formData, setFormData] = useState({
    phoneNumber: '',
    email: '',
    otpCode: ''
  });
  const [validationErrors, setValidationErrors] = useState({});

  const isRTL = i18n.language === 'ar';
  const selectedDisplay = location.state?.selectedDisplay;

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // إزالة رسالة الخطأ عند التعديل
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (step === 'phone') {
      if (!formData.phoneNumber) {
        errors.phoneNumber = t('enterPhoneNumber');
      } else if (!validateSaudiPhoneNumber(formData.phoneNumber)) {
        errors.phoneNumber = t('invalidPhoneNumber');
      }

      if (!formData.email) {
        errors.email = t('enterEmail');
      } else if (!validateEmail(formData.email)) {
        errors.email = t('invalidEmail');
      }
    } else if (step === 'otp') {
      if (!formData.otpCode) {
        errors.otpCode = t('enterOTP');
      } else if (formData.otpCode.length !== 6) {
        errors.otpCode = t('invalidOTP');
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSendOTP = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);
    const response = await sendOTP(formattedPhone, formData.email, i18n.language);
    
    if (response) {
      setStep('otp');
    }
  };

  const handleVerifyOTP = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);
    const response = await verifyOTP(formattedPhone, formData.otpCode);
    
    if (response) {
      // تسجيل الدخول
      login(response.data.customer, response.data.token);
      
      // الانتقال لصفحة إدخال الاسم
      navigate('/enter-name', {
        state: {
          selectedDisplay,
          customer: response.data.customer
        }
      });
    }
  };

  const handleBack = () => {
    if (step === 'otp') {
      setStep('phone');
      resetOTP();
    } else {
      navigate('/select-display');
    }
  };

  const handleResendOTP = async () => {
    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);
    await sendOTP(formattedPhone, formData.email, i18n.language);
  };

  return (
    <Container isRTL={isRTL}>
      <Card>
        <Header>
          <Title>
            {step === 'phone' ? 
              (isRTL ? 'تسجيل الدخول' : 'Login') : 
              (isRTL ? 'رمز التحقق' : 'Verification Code')
            }
          </Title>
          
          {selectedDisplay && (
            <DisplayInfo>
              <strong>{t('displayNumber', { number: selectedDisplay.displayNumber })}</strong>
              <br />
              {selectedDisplay.name}
            </DisplayInfo>
          )}
        </Header>

        {error && <ErrorMessage>{error}</ErrorMessage>}
        
        {otpSent && step === 'otp' && (
          <SuccessMessage>
            {t('otpSent')}
          </SuccessMessage>
        )}

        {step === 'phone' ? (
          <Form onSubmit={handleSendOTP}>
            <InputGroup>
              <Label>{t('phoneNumber')}</Label>
              <Input
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                placeholder={t('enterPhoneNumber')}
                required
              />
              {validationErrors.phoneNumber && (
                <ErrorMessage>{validationErrors.phoneNumber}</ErrorMessage>
              )}
            </InputGroup>

            <InputGroup>
              <Label>{t('email')}</Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder={t('enterEmail')}
                required
              />
              {validationErrors.email && (
                <ErrorMessage>{validationErrors.email}</ErrorMessage>
              )}
            </InputGroup>

            <Button type="submit" disabled={loading}>
              {loading ? t('processing') : t('sendOTP')}
            </Button>
          </Form>
        ) : (
          <Form onSubmit={handleVerifyOTP}>
            <OTPInfo>
              {isRTL ? 
                `تم إرسال رمز التحقق إلى: ${formData.email}` :
                `Verification code sent to: ${formData.email}`
              }
            </OTPInfo>

            <InputGroup>
              <Label>{t('otpCode')}</Label>
              <Input
                type="text"
                name="otpCode"
                value={formData.otpCode}
                onChange={handleInputChange}
                placeholder={t('enterOTP')}
                maxLength="6"
                required
              />
              {validationErrors.otpCode && (
                <ErrorMessage>{validationErrors.otpCode}</ErrorMessage>
              )}
            </InputGroup>

            <Button type="submit" disabled={loading}>
              {loading ? t('processing') : t('verifyOTP')}
            </Button>

            <Button 
              type="button" 
              onClick={handleResendOTP}
              disabled={loading}
              style={{ background: '#6c757d' }}
            >
              {isRTL ? 'إعادة إرسال الرمز' : 'Resend Code'}
            </Button>
          </Form>
        )}

        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <BackButton onClick={handleBack}>
            {t('back')}
          </BackButton>
        </div>
      </Card>
    </Container>
  );
};

export default LoginPage;
