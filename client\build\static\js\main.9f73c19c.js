/*! For license information please see main.9f73c19c.js.LICENSE.txt */
(()=>{var e={4:(e,t,n)=>{"use strict";var r=n(853),o=n(43),a=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(l(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),x=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),_=Symbol.for("react.lazy");Symbol.for("react.scope");var T=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var R=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var O=Symbol.iterator;function N(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=O&&e[O]||e["@@iterator"])?e:null}var P=Symbol.for("react.client.reference");function L(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===P?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case v:return"Profiler";case y:return"StrictMode";case k:return"Suspense";case E:return"SuspenseList";case T:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case x:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case S:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:L(e.type)||"Memo";case _:t=e._payload,e=e._init;try{return L(e(t))}catch(n){}}return null}var j=Array.isArray,A=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z={pending:!1,data:null,method:null,action:null},F=[],$=-1;function I(e){return{current:e}}function M(e){0>$||(e.current=F[$],F[$]=null,$--)}function U(e,t){$++,F[$]=e.current,e.current=t}var B=I(null),H=I(null),V=I(null),W=I(null);function q(e,t){switch(U(V,t),U(H,e),U(B,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?od(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ad(t=od(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}M(B),U(B,e)}function K(){M(B),M(H),M(V)}function Q(e){null!==e.memoizedState&&U(W,e);var t=B.current,n=ad(t,e.type);t!==n&&(U(H,e),U(B,n))}function Y(e){H.current===e&&(M(B),M(H)),W.current===e&&(M(W),Qd._currentValue=z)}var J=Object.prototype.hasOwnProperty,G=r.unstable_scheduleCallback,X=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,oe=r.unstable_UserBlockingPriority,ae=r.unstable_NormalPriority,ie=r.unstable_LowPriority,se=r.unstable_IdlePriority,le=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof le&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/me|0)|0},he=Math.log,me=Math.LN2;var ge=256,ye=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var o=0,a=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var s=134217727&r;return 0!==s?0!==(r=s&~a)?o=ve(r):0!==(i&=s)?o=ve(i):n||0!==(n=s&~e)&&(o=ve(n)):0!==(s=r&~a)?o=ve(s):0!==i?o=ve(i):n||0!==(n=r&~e)&&(o=ve(n)),0===o?0:0!==t&&t!==o&&0===(t&a)&&((a=o&-o)>=(n=t&-t)||32===a&&0!==(4194048&n))?t:o}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function xe(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Se(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function ke(){var e=ye;return 0===(62914560&(ye<<=1))&&(ye=4194304),e}function Ee(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function _e(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Te(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Re(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Oe(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Ne(){var e=D.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var Pe=Math.random().toString(36).slice(2),Le="__reactFiber$"+Pe,je="__reactProps$"+Pe,Ae="__reactContainer$"+Pe,De="__reactEvents$"+Pe,ze="__reactListeners$"+Pe,Fe="__reactHandles$"+Pe,$e="__reactResources$"+Pe,Ie="__reactMarker$"+Pe;function Me(e){delete e[Le],delete e[je],delete e[De],delete e[ze],delete e[Fe]}function Ue(e){var t=e[Le];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[Le]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Le])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function Be(e){if(e=e[Le]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function Ve(e){var t=e[$e];return t||(t=e[$e]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function We(e){e[Ie]=!0}var qe=new Set,Ke={};function Qe(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for(Ke[e]=t,e=0;e<t.length;e++)qe.add(t[e])}var Je,Ge,Xe=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(o=t,J.call(et,o)||!J.call(Ze,o)&&(Xe.test(o)?et[o]=!0:(Ze[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function ot(e){if(void 0===Je)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Je=t&&t[1]||"",Ge=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Je+e+Ge}var at=!1;function it(e,t){if(!e||at)return"";at=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(s){if(s&&r&&"string"===typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),i=a[0],s=a[1];if(i&&s){var l=i.split("\n"),u=s.split("\n");for(o=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;o<u.length&&!u[o].includes("DetermineComponentFrameRoot");)o++;if(r===l.length||o===u.length)for(r=l.length-1,o=u.length-1;1<=r&&0<=o&&l[r]!==u[o];)o--;for(;1<=r&&0<=o;r--,o--)if(l[r]!==u[o]){if(1!==r||1!==o)do{if(r--,0>--o||l[r]!==u[o]){var c="\n"+l[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=o);break}}}finally{at=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ot(n):""}function st(e){switch(e.tag){case 26:case 27:case 5:return ot(e.type);case 16:return ot("Lazy");case 13:return ot("Suspense");case 19:return ot("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return ot("Activity");default:return""}}function lt(e){try{var t="";do{t+=st(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ht=/[\n"\\]/g;function mt(e){return e.replace(ht,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,o,a,i,s){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?vt(e,i,ut(t)):null!=n?vt(e,i,ut(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=a&&(e.defaultChecked=!!a),null!=o&&(e.checked=o&&"function"!==typeof o&&"symbol"!==typeof o),null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.name=""+ut(s):e.removeAttribute("name")}function yt(e,t,n,r,o,a,i,s){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=t||null!=n){if(!("submit"!==a&&"reset"!==a||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,s||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:o)&&"symbol"!==typeof r&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function xt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(j(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function St(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var kt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Et(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||kt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&n[o]!==r&&Et(e,o,r)}else for(var a in t)t.hasOwnProperty(a)&&Et(e,a,t[a])}function _t(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ot(e){return Rt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Nt=null;function Pt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Lt=null,jt=null;function At(e){var t=Be(e);if(t&&(e=t.stateNode)){var n=e[je]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=r[je]||null;if(!o)throw Error(i(90));gt(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Dt=!1;function zt(e,t,n){if(Dt)return e(t,n);Dt=!0;try{return e(t)}finally{if(Dt=!1,(null!==Lt||null!==jt)&&(Uu(),Lt&&(t=Lt,e=jt,jt=Lt=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function Ft(e,t){var n=e.stateNode;if(null===n)return null;var r=n[je]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var $t=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),It=!1;if($t)try{var Mt={};Object.defineProperty(Mt,"passive",{get:function(){It=!0}}),window.addEventListener("test",Mt,Mt),window.removeEventListener("test",Mt,Mt)}catch(Af){It=!1}var Ut=null,Bt=null,Ht=null;function Vt(){if(Ht)return Ht;var e,t,n=Bt,r=n.length,o="value"in Ut?Ut.value:Ut.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Ht=o.slice(e,1<t?1-t:void 0)}function Wt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function qt(){return!0}function Kt(){return!1}function Qt(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?qt:Kt,this.isPropagationStopped=Kt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=qt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=qt)},persist:function(){},isPersistent:qt}),t}var Yt,Jt,Gt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Qt(Xt),en=f({},Xt,{view:0,detail:0}),tn=Qt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Gt&&(Gt&&"mousemove"===e.type?(Yt=e.screenX-Gt.screenX,Jt=e.screenY-Gt.screenY):Jt=Yt=0,Gt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Jt}}),rn=Qt(nn),on=Qt(f({},nn,{dataTransfer:0})),an=Qt(f({},en,{relatedTarget:0})),sn=Qt(f({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Qt(f({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Qt(f({},Xt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function hn(){return pn}var mn=Qt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Wt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hn,charCode:function(e){return"keypress"===e.type?Wt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Wt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Qt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Qt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hn})),vn=Qt(f({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Qt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Qt(f({},Xt,{newState:0,oldState:0})),xn=[9,13,27,32],Sn=$t&&"CompositionEvent"in window,kn=null;$t&&"documentMode"in document&&(kn=document.documentMode);var En=$t&&"TextEvent"in window&&!kn,Cn=$t&&(!Sn||kn&&8<kn&&11>=kn),_n=String.fromCharCode(32),Tn=!1;function Rn(e,t){switch(e){case"keyup":return-1!==xn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function On(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Nn=!1;var Pn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ln(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Pn[e.type]:"textarea"===t}function jn(e,t,n,r){Lt?jt?jt.push(r):jt=[r]:Lt=r,0<(t=Vc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,Dn=null;function zn(e){zc(e,0)}function Fn(e){if(ft(He(e)))return e}function $n(e,t){if("change"===e)return t}var In=!1;if($t){var Mn;if($t){var Un="oninput"in document;if(!Un){var Bn=document.createElement("div");Bn.setAttribute("oninput","return;"),Un="function"===typeof Bn.oninput}Mn=Un}else Mn=!1;In=Mn&&(!document.documentMode||9<document.documentMode)}function Hn(){An&&(An.detachEvent("onpropertychange",Vn),Dn=An=null)}function Vn(e){if("value"===e.propertyName&&Fn(Dn)){var t=[];jn(t,Dn,e,Pt(e)),zt(zn,t)}}function Wn(e,t,n){"focusin"===e?(Hn(),Dn=n,(An=t).attachEvent("onpropertychange",Vn)):"focusout"===e&&Hn()}function qn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Fn(Dn)}function Kn(e,t){if("click"===e)return Fn(t)}function Qn(e,t){if("input"===e||"change"===e)return Fn(t)}var Yn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Jn(e,t){if(Yn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!J.call(t,o)||!Yn(e[o],t[o]))return!1}return!0}function Gn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Gn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Gn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=$t&&"documentMode"in document&&11>=document.documentMode,rr=null,or=null,ar=null,ir=!1;function sr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Jn(ar,r)||(ar=r,0<(r=Vc(or,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}$t&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var pr=fr("animationend"),hr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),yr=fr("transitionstart"),vr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Sr(e,t){wr.set(e,t),Qe(t,[e])}xr.push("scrollEnd");var kr=new WeakMap;function Er(e,t){if("object"===typeof e&&null!==e){var n=kr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},kr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var Cr=[],_r=0,Tr=0;function Rr(){for(var e=_r,t=Tr=_r=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var o=Cr[t];Cr[t++]=null;var a=Cr[t];if(Cr[t++]=null,null!==r&&null!==o){var i=r.pending;null===i?o.next=o:(o.next=i.next,i.next=o),r.pending=o}0!==a&&Lr(n,o,a)}}function Or(e,t,n,r){Cr[_r++]=e,Cr[_r++]=t,Cr[_r++]=n,Cr[_r++]=r,Tr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Nr(e,t,n,r){return Or(e,t,n,r),jr(e)}function Pr(e,t){return Or(e,null,null,t),jr(e)}function Lr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(o=!0)),e=a,a=a.return;return 3===e.tag?(a=e.stateNode,o&&null!==t&&(o=31-pe(n),null===(r=(e=a.hiddenUpdates)[o])?e[o]=[t]:r.push(t),t.lane=536870912|n),a):null}function jr(e){if(50<Lu)throw Lu=0,ju=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ar={};function Dr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zr(e,t,n,r){return new Dr(e,t,n,r)}function Fr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function $r(e,t){var n=e.alternate;return null===n?((n=zr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ir(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Mr(e,t,n,r,o,a){var s=0;if(r=e,"function"===typeof e)Fr(e)&&(s=1);else if("string"===typeof e)s=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,B.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case T:return(e=zr(31,n,t,o)).elementType=T,e.lanes=a,e;case g:return Ur(n.children,o,a,t);case y:s=8,o|=24;break;case v:return(e=zr(12,n,t,2|o)).elementType=v,e.lanes=a,e;case k:return(e=zr(13,n,t,o)).elementType=k,e.lanes=a,e;case E:return(e=zr(19,n,t,o)).elementType=E,e.lanes=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case x:s=10;break e;case w:s=9;break e;case S:s=11;break e;case C:s=14;break e;case _:s=16,r=null;break e}s=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=zr(s,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function Ur(e,t,n,r){return(e=zr(7,e,r,t)).lanes=n,e}function Br(e,t,n){return(e=zr(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=zr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Vr=[],Wr=0,qr=null,Kr=0,Qr=[],Yr=0,Jr=null,Gr=1,Xr="";function Zr(e,t){Vr[Wr++]=Kr,Vr[Wr++]=qr,qr=e,Kr=t}function eo(e,t,n){Qr[Yr++]=Gr,Qr[Yr++]=Xr,Qr[Yr++]=Jr,Jr=e;var r=Gr;e=Xr;var o=32-pe(r)-1;r&=~(1<<o),n+=1;var a=32-pe(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Gr=1<<32-pe(t)+o|n<<o|r,Xr=a+e}else Gr=1<<a|n<<o|r,Xr=e}function to(e){null!==e.return&&(Zr(e,1),eo(e,1,0))}function no(e){for(;e===qr;)qr=Vr[--Wr],Vr[Wr]=null,Kr=Vr[--Wr],Vr[Wr]=null;for(;e===Jr;)Jr=Qr[--Yr],Qr[Yr]=null,Xr=Qr[--Yr],Qr[Yr]=null,Gr=Qr[--Yr],Qr[Yr]=null}var ro=null,oo=null,ao=!1,io=null,so=!1,lo=Error(i(519));function uo(e){throw go(Er(Error(i(418,"")),e)),lo}function co(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Le]=e,t[je]=r,n){case"dialog":Fc("cancel",t),Fc("close",t);break;case"iframe":case"object":case"embed":Fc("load",t);break;case"video":case"audio":for(n=0;n<Ac.length;n++)Fc(Ac[n],t);break;case"source":Fc("error",t);break;case"img":case"image":case"link":Fc("error",t),Fc("load",t);break;case"details":Fc("toggle",t);break;case"input":Fc("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Fc("invalid",t);break;case"textarea":Fc("invalid",t),xt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Jc(t.textContent,n)?(null!=r.popover&&(Fc("beforetoggle",t),Fc("toggle",t)),null!=r.onScroll&&Fc("scroll",t),null!=r.onScrollEnd&&Fc("scrollend",t),null!=r.onClick&&(t.onclick=Gc),t=!0):t=!1,t||uo(e)}function fo(e){for(ro=e.return;ro;)switch(ro.tag){case 5:case 13:return void(so=!1);case 27:case 3:return void(so=!0);default:ro=ro.return}}function po(e){if(e!==ro)return!1;if(!ao)return fo(e),ao=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&oo&&uo(e),fo(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){oo=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}oo=null}}else 27===n?(n=oo,pd(e.type)?(e=vd,vd=null,oo=e):oo=n):oo=ro?yd(e.stateNode.nextSibling):null;return!0}function ho(){oo=ro=null,ao=!1}function mo(){var e=io;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),io=null),e}function go(e){null===io?io=[e]:io.push(e)}var yo=I(null),vo=null,bo=null;function wo(e,t,n){U(yo,t._currentValue),t._currentValue=n}function xo(e){e._currentValue=yo.current,M(yo)}function So(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ko(e,t,n,r){var o=e.child;for(null!==o&&(o.return=e);null!==o;){var a=o.dependencies;if(null!==a){var s=o.child;a=a.firstContext;e:for(;null!==a;){var l=a;a=o;for(var u=0;u<t.length;u++)if(l.context===t[u]){a.lanes|=n,null!==(l=a.alternate)&&(l.lanes|=n),So(a.return,n,e),r||(s=null);break e}a=l.next}}else if(18===o.tag){if(null===(s=o.return))throw Error(i(341));s.lanes|=n,null!==(a=s.alternate)&&(a.lanes|=n),So(s,n,e),s=null}else s=o.child;if(null!==s)s.return=o;else for(s=o;null!==s;){if(s===e){s=null;break}if(null!==(o=s.sibling)){o.return=s.return,s=o;break}s=s.return}o=s}}function Eo(e,t,n,r){e=null;for(var o=t,a=!1;null!==o;){if(!a)if(0!==(524288&o.flags))a=!0;else if(0!==(262144&o.flags))break;if(10===o.tag){var s=o.alternate;if(null===s)throw Error(i(387));if(null!==(s=s.memoizedProps)){var l=o.type;Yn(o.pendingProps.value,s.value)||(null!==e?e.push(l):e=[l])}}else if(o===W.current){if(null===(s=o.alternate))throw Error(i(387));s.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(null!==e?e.push(Qd):e=[Qd])}o=o.return}null!==e&&ko(t,e,n,r),t.flags|=262144}function Co(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function _o(e){vo=e,bo=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function To(e){return Oo(vo,e)}function Ro(e,t){return null===vo&&_o(e),Oo(e,t)}function Oo(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===bo){if(null===e)throw Error(i(308));bo=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else bo=bo.next=t;return n}var No="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Po=r.unstable_scheduleCallback,Lo=r.unstable_NormalPriority,jo={$$typeof:x,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ao(){return{controller:new No,data:new Map,refCount:0}}function Do(e){e.refCount--,0===e.refCount&&Po(Lo,function(){e.controller.abort()})}var zo=null,Fo=0,$o=0,Io=null;function Mo(){if(0===--Fo&&null!==zo){null!==Io&&(Io.status="fulfilled");var e=zo;zo=null,$o=0,Io=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Uo=A.S;A.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===zo){var n=zo=[];Fo=0,$o=Oc(),Io={status:"pending",value:void 0,then:function(e){n.push(e)}}}Fo++,t.then(Mo,Mo)}(0,t),null!==Uo&&Uo(e,t)};var Bo=I(null);function Ho(){var e=Bo.current;return null!==e?e:ru.pooledCache}function Vo(e,t){U(Bo,null===t?Bo.current:t.pool)}function Wo(){var e=Ho();return null===e?null:{parent:jo._currentValue,pool:e}}var qo=Error(i(460)),Ko=Error(i(474)),Qo=Error(i(542)),Yo={then:function(){}};function Jo(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Go(){}function Xo(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Go,Go),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ta(e=t.reason),e;default:if("string"===typeof t.status)t.then(Go,Go);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ta(e=t.reason),e}throw Zo=t,qo}}var Zo=null;function ea(){if(null===Zo)throw Error(i(459));var e=Zo;return Zo=null,e}function ta(e){if(e===qo||e===Qo)throw Error(i(483))}var na=!1;function ra(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function oa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function aa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ia(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nu)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=jr(e),Lr(e,null,n),t}return Or(e,r,t,n),jr(e)}function sa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Te(e,n)}}function la(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ua=!1;function ca(){if(ua){if(null!==Io)throw Io}}function da(e,t,n,r){ua=!1;var o=e.updateQueue;na=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,s=o.shared.pending;if(null!==s){o.shared.pending=null;var l=s,u=l.next;l.next=null,null===i?a=u:i.next=u,i=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==a){var d=o.baseState;for(i=0,c=u=l=null,s=a;;){var p=-536870913&s.lane,h=p!==s.lane;if(h?(au&p)===p:(r&p)===p){0!==p&&p===$o&&(ua=!0),null!==c&&(c=c.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});e:{var m=e,g=s;p=t;var y=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(y,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=g.payload)?m.call(y,d,p):m)||void 0===p)break e;d=f({},d,p);break e;case 2:na=!0}}null!==(p=s.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=o.callbacks)?o.callbacks=[p]:h.push(p))}else h={lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,i|=p;if(null===(s=s.next)){if(null===(s=o.shared.pending))break;s=(h=s).next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}null===c&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,null===a&&(o.shared.lanes=0),pu|=i,e.lanes=i,e.memoizedState=d}}function fa(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function pa(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fa(n[e],t)}var ha=I(null),ma=I(0);function ga(e,t){U(ma,e=du),U(ha,t),du=e|t.baseLanes}function ya(){U(ma,du),U(ha,ha.current)}function va(){du=ma.current,M(ha),M(ma)}var ba=0,wa=null,xa=null,Sa=null,ka=!1,Ea=!1,Ca=!1,_a=0,Ta=0,Ra=null,Oa=0;function Na(){throw Error(i(321))}function Pa(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function La(e,t,n,r,o,a){return ba=a,wa=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?qi:Ki,Ca=!1,a=n(r,o),Ca=!1,Ea&&(a=Aa(t,n,r,o)),ja(e),a}function ja(e){A.H=Wi;var t=null!==xa&&null!==xa.next;if(ba=0,Sa=xa=wa=null,ka=!1,Ta=0,Ra=null,t)throw Error(i(300));null===e||Ts||null!==(e=e.dependencies)&&Co(e)&&(Ts=!0)}function Aa(e,t,n,r){wa=e;var o=0;do{if(Ea&&(Ra=null),Ta=0,Ea=!1,25<=o)throw Error(i(301));if(o+=1,Sa=xa=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}A.H=Qi,a=t(n,r)}while(Ea);return a}function Da(){var e=A.H,t=e.useState()[0];return t="function"===typeof t.then?Ua(t):t,e=e.useState()[0],(null!==xa?xa.memoizedState:null)!==e&&(wa.flags|=1024),t}function za(){var e=0!==_a;return _a=0,e}function Fa(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function $a(e){if(ka){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}ka=!1}ba=0,Sa=xa=wa=null,Ea=!1,Ta=_a=0,Ra=null}function Ia(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Sa?wa.memoizedState=Sa=e:Sa=Sa.next=e,Sa}function Ma(){if(null===xa){var e=wa.alternate;e=null!==e?e.memoizedState:null}else e=xa.next;var t=null===Sa?wa.memoizedState:Sa.next;if(null!==t)Sa=t,xa=e;else{if(null===e){if(null===wa.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(xa=e).memoizedState,baseState:xa.baseState,baseQueue:xa.baseQueue,queue:xa.queue,next:null},null===Sa?wa.memoizedState=Sa=e:Sa=Sa.next=e}return Sa}function Ua(e){var t=Ta;return Ta+=1,null===Ra&&(Ra=[]),e=Xo(Ra,e,t),t=wa,null===(null===Sa?t.memoizedState:Sa.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?qi:Ki),e}function Ba(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ua(e);if(e.$$typeof===x)return To(e)}throw Error(i(438,String(e)))}function Ha(e){var t=null,n=wa.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wa.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wa.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=R;return t.index++,n}function Va(e,t){return"function"===typeof t?t(e):t}function Wa(e){return qa(Ma(),xa,e)}function qa(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var o=e.baseQueue,a=r.pending;if(null!==a){if(null!==o){var s=o.next;o.next=a.next,a.next=s}t.baseQueue=o=a,r.pending=null}if(a=e.baseState,null===o)e.memoizedState=a;else{var l=s=null,u=null,c=t=o.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(au&f)===f:(ba&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===$o&&(d=!0);else{if((ba&p)===p){c=c.next,p===$o&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=f,s=a):u=u.next=f,wa.lanes|=p,pu|=p}f=c.action,Ca&&n(a,f),a=c.hasEagerState?c.eagerState:n(a,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=p,s=a):u=u.next=p,wa.lanes|=f,pu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?s=a:u.next=l,!Yn(a,e.memoizedState)&&(Ts=!0,d&&null!==(n=Io)))throw n;e.memoizedState=a,e.baseState=s,e.baseQueue=u,r.lastRenderedState=a}return null===o&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ka(e){var t=Ma(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var s=o=o.next;do{a=e(a,s.action),s=s.next}while(s!==o);Yn(a,t.memoizedState)||(Ts=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Qa(e,t,n){var r=wa,o=Ma(),a=ao;if(a){if(void 0===n)throw Error(i(407));n=n()}else n=t();var s=!Yn((xa||o).memoizedState,n);if(s&&(o.memoizedState=n,Ts=!0),o=o.queue,yi(2048,8,Ga.bind(null,r,o,e),[e]),o.getSnapshot!==t||s||null!==Sa&&1&Sa.memoizedState.tag){if(r.flags|=2048,hi(9,{destroy:void 0,resource:void 0},Ja.bind(null,r,o,n,t),null),null===ru)throw Error(i(349));a||0!==(124&ba)||Ya(r,t,n)}return n}function Ya(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wa.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wa.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ja(e,t,n,r){t.value=n,t.getSnapshot=r,Xa(t)&&Za(e)}function Ga(e,t,n){return n(function(){Xa(t)&&Za(e)})}function Xa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function Za(e){var t=Pr(e,2);null!==t&&zu(t,e,2)}function ei(e){var t=Ia();if("function"===typeof e){var n=e;if(e=n(),Ca){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Va,lastRenderedState:e},t}function ti(e,t,n,r){return e.baseState=n,qa(e,xa,"function"===typeof r?r:Va)}function ni(e,t,n,r,o){if(Bi(e))throw Error(i(485));if(null!==(e=t.action)){var a={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==A.T?n(!0):a.isTransition=!1,r(a),null===(n=t.pending)?(a.next=t.pending=a,ri(t,a)):(a.next=n.next,t.pending=n.next=a)}}function ri(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var a=A.T,i={};A.T=i;try{var s=n(o,r),l=A.S;null!==l&&l(i,s),oi(e,t,s)}catch(u){ii(e,t,u)}finally{A.T=a}}else try{oi(e,t,a=n(o,r))}catch(c){ii(e,t,c)}}function oi(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){ai(e,t,n)},function(n){return ii(e,t,n)}):ai(e,t,n)}function ai(e,t,n){t.status="fulfilled",t.value=n,si(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ri(e,n)))}function ii(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,si(t),t=t.next}while(t!==r)}e.action=null}function si(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function li(e,t){return t}function ui(e,t){if(ao){var n=ru.formState;if(null!==n){e:{var r=wa;if(ao){if(oo){t:{for(var o=oo,a=so;8!==o.nodeType;){if(!a){o=null;break t}if(null===(o=yd(o.nextSibling))){o=null;break t}}o="F!"===(a=o.data)||"F"===a?o:null}if(o){oo=yd(o.nextSibling),r="F!"===o.data;break e}}uo(r)}r=!1}r&&(t=n[0])}}return(n=Ia()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:li,lastRenderedState:t},n.queue=r,n=Ii.bind(null,wa,r),r.dispatch=n,r=ei(!1),a=Ui.bind(null,wa,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=Ia()).queue=o,n=ni.bind(null,wa,o,a,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function ci(e){return di(Ma(),xa,e)}function di(e,t,n){if(t=qa(e,t,li)[0],e=Wa(Va)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Ua(t)}catch(i){if(i===qo)throw Qo;throw i}else r=t;var o=(t=Ma()).queue,a=o.dispatch;return n!==t.memoizedState&&(wa.flags|=2048,hi(9,{destroy:void 0,resource:void 0},fi.bind(null,o,n),null)),[r,a,e]}function fi(e,t){e.action=t}function pi(e){var t=Ma(),n=xa;if(null!==n)return di(t,n,e);Ma(),t=t.memoizedState;var r=(n=Ma()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function hi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wa.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wa.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function mi(){return Ma().memoizedState}function gi(e,t,n,r){var o=Ia();r=void 0===r?null:r,wa.flags|=e,o.memoizedState=hi(1|t,{destroy:void 0,resource:void 0},n,r)}function yi(e,t,n,r){var o=Ma();r=void 0===r?null:r;var a=o.memoizedState.inst;null!==xa&&null!==r&&Pa(r,xa.memoizedState.deps)?o.memoizedState=hi(t,a,n,r):(wa.flags|=e,o.memoizedState=hi(1|t,a,n,r))}function vi(e,t){gi(8390656,8,e,t)}function bi(e,t){yi(2048,8,e,t)}function wi(e,t){return yi(4,2,e,t)}function xi(e,t){return yi(4,4,e,t)}function Si(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function ki(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,yi(4,4,Si.bind(null,t,e),n)}function Ei(){}function Ci(e,t){var n=Ma();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Pa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function _i(e,t){var n=Ma();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Pa(t,r[1]))return r[0];if(r=e(),Ca){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Ti(e,t,n){return void 0===n||0!==(1073741824&ba)?e.memoizedState=t:(e.memoizedState=n,e=Du(),wa.lanes|=e,pu|=e,n)}function Ri(e,t,n,r){return Yn(n,t)?n:null!==ha.current?(e=Ti(e,n,r),Yn(e,t)||(Ts=!0),e):0===(42&ba)?(Ts=!0,e.memoizedState=n):(e=Du(),wa.lanes|=e,pu|=e,t)}function Oi(e,t,n,r,o){var a=D.p;D.p=0!==a&&8>a?a:8;var i=A.T,s={};A.T=s,Ui(e,!1,t,n);try{var l=o(),u=A.S;if(null!==u&&u(s,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Mi(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(l,r),Au());else Mi(e,t,r,Au())}catch(c){Mi(e,t,{then:function(){},status:"rejected",reason:c},Au())}finally{D.p=a,A.T=i}}function Ni(){}function Pi(e,t,n,r){if(5!==e.tag)throw Error(i(476));var o=Li(e).queue;Oi(e,o,t,z,null===n?Ni:function(){return ji(e),n(r)})}function Li(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:z,baseState:z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Va,lastRenderedState:z},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Va,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function ji(e){Mi(e,Li(e).next.queue,{},Au())}function Ai(){return To(Qd)}function Di(){return Ma().memoizedState}function zi(){return Ma().memoizedState}function Fi(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Au(),r=ia(t,e=aa(n),n);return null!==r&&(zu(r,t,n),sa(r,t,n)),t={cache:Ao()},void(e.payload=t)}t=t.return}}function $i(e,t,n){var r=Au();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Bi(e)?Hi(t,n):null!==(n=Nr(e,t,n,r))&&(zu(n,e,r),Vi(n,t,r))}function Ii(e,t,n){Mi(e,t,n,Au())}function Mi(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Bi(e))Hi(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=a(i,n);if(o.hasEagerState=!0,o.eagerState=s,Yn(s,i))return Or(e,t,o,0),null===ru&&Rr(),!1}catch(l){}if(null!==(n=Nr(e,t,o,r)))return zu(n,e,r),Vi(n,t,r),!0}return!1}function Ui(e,t,n,r){if(r={lane:2,revertLane:Oc(),action:r,hasEagerState:!1,eagerState:null,next:null},Bi(e)){if(t)throw Error(i(479))}else null!==(t=Nr(e,n,r,2))&&zu(t,e,2)}function Bi(e){var t=e.alternate;return e===wa||null!==t&&t===wa}function Hi(e,t){Ea=ka=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Vi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Te(e,n)}}var Wi={readContext:To,use:Ba,useCallback:Na,useContext:Na,useEffect:Na,useImperativeHandle:Na,useLayoutEffect:Na,useInsertionEffect:Na,useMemo:Na,useReducer:Na,useRef:Na,useState:Na,useDebugValue:Na,useDeferredValue:Na,useTransition:Na,useSyncExternalStore:Na,useId:Na,useHostTransitionStatus:Na,useFormState:Na,useActionState:Na,useOptimistic:Na,useMemoCache:Na,useCacheRefresh:Na},qi={readContext:To,use:Ba,useCallback:function(e,t){return Ia().memoizedState=[e,void 0===t?null:t],e},useContext:To,useEffect:vi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4194308,4,Si.bind(null,t,e),n)},useLayoutEffect:function(e,t){return gi(4194308,4,e,t)},useInsertionEffect:function(e,t){gi(4,2,e,t)},useMemo:function(e,t){var n=Ia();t=void 0===t?null:t;var r=e();if(Ca){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ia();if(void 0!==n){var o=n(t);if(Ca){fe(!0);try{n(t)}finally{fe(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=$i.bind(null,wa,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ia().memoizedState=e},useState:function(e){var t=(e=ei(e)).queue,n=Ii.bind(null,wa,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ei,useDeferredValue:function(e,t){return Ti(Ia(),e,t)},useTransition:function(){var e=ei(!1);return e=Oi.bind(null,wa,e.queue,!0,!1),Ia().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=wa,o=Ia();if(ao){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===ru)throw Error(i(349));0!==(124&au)||Ya(r,t,n)}o.memoizedState=n;var a={value:n,getSnapshot:t};return o.queue=a,vi(Ga.bind(null,r,a,e),[e]),r.flags|=2048,hi(9,{destroy:void 0,resource:void 0},Ja.bind(null,r,a,n,t),null),n},useId:function(){var e=Ia(),t=ru.identifierPrefix;if(ao){var n=Xr;t="\xab"+t+"R"+(n=(Gr&~(1<<32-pe(Gr)-1)).toString(32)+n),0<(n=_a++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Oa++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Ai,useFormState:ui,useActionState:ui,useOptimistic:function(e){var t=Ia();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Ui.bind(null,wa,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ha,useCacheRefresh:function(){return Ia().memoizedState=Fi.bind(null,wa)}},Ki={readContext:To,use:Ba,useCallback:Ci,useContext:To,useEffect:bi,useImperativeHandle:ki,useInsertionEffect:wi,useLayoutEffect:xi,useMemo:_i,useReducer:Wa,useRef:mi,useState:function(){return Wa(Va)},useDebugValue:Ei,useDeferredValue:function(e,t){return Ri(Ma(),xa.memoizedState,e,t)},useTransition:function(){var e=Wa(Va)[0],t=Ma().memoizedState;return["boolean"===typeof e?e:Ua(e),t]},useSyncExternalStore:Qa,useId:Di,useHostTransitionStatus:Ai,useFormState:ci,useActionState:ci,useOptimistic:function(e,t){return ti(Ma(),0,e,t)},useMemoCache:Ha,useCacheRefresh:zi},Qi={readContext:To,use:Ba,useCallback:Ci,useContext:To,useEffect:bi,useImperativeHandle:ki,useInsertionEffect:wi,useLayoutEffect:xi,useMemo:_i,useReducer:Ka,useRef:mi,useState:function(){return Ka(Va)},useDebugValue:Ei,useDeferredValue:function(e,t){var n=Ma();return null===xa?Ti(n,e,t):Ri(n,xa.memoizedState,e,t)},useTransition:function(){var e=Ka(Va)[0],t=Ma().memoizedState;return["boolean"===typeof e?e:Ua(e),t]},useSyncExternalStore:Qa,useId:Di,useHostTransitionStatus:Ai,useFormState:pi,useActionState:pi,useOptimistic:function(e,t){var n=Ma();return null!==xa?ti(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ha,useCacheRefresh:zi},Yi=null,Ji=0;function Gi(e){var t=Ji;return Ji+=1,null===Yi&&(Yi=[]),Xo(Yi,e,t)}function Xi(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zi(e,t){if(t.$$typeof===p)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function es(e){return(0,e._init)(e._payload)}function ts(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(e,t){return(e=$r(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Br(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===_&&es(a)===t.type)?(Xi(t=o(t,n.props),n),t.return=e,t):(Xi(t=Mr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Ur(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Br(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case h:return Xi(n=Mr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Hr(t,e.mode,n)).return=e,t;case _:return f(e,t=(0,t._init)(t._payload),n)}if(j(t)||N(t))return(t=Ur(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Gi(t),n);if(t.$$typeof===x)return f(e,Ro(e,t),n);Zi(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==o?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===o?u(e,t,n,r):null;case m:return n.key===o?c(e,t,n,r):null;case _:return p(e,t,n=(o=n._init)(n._payload),r)}if(j(n)||N(n))return null!==o?null:d(e,t,n,r,null);if("function"===typeof n.then)return p(e,t,Gi(n),r);if(n.$$typeof===x)return p(e,t,Ro(e,n),r);Zi(e,n)}return null}function y(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case h:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case m:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case _:return y(e,t,n,r=(0,r._init)(r._payload),o)}if(j(r)||N(r))return d(t,e=e.get(n)||null,r,o,null);if("function"===typeof r.then)return y(e,t,n,Gi(r),o);if(r.$$typeof===x)return y(e,t,n,Ro(t,r),o);Zi(t,r)}return null}function v(l,u,c,d){if("object"===typeof c&&null!==c&&c.type===g&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case h:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===g){if(7===u.tag){n(l,u.sibling),(d=o(u,c.props.children)).return=l,l=d;break e}}else if(u.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===_&&es(b)===u.type){n(l,u.sibling),Xi(d=o(u,c.props),c),d.return=l,l=d;break e}n(l,u);break}t(l,u),u=u.sibling}c.type===g?((d=Ur(c.props.children,l.mode,d,c.key)).return=l,l=d):(Xi(d=Mr(c.type,c.key,c.props,null,l.mode,d),c),d.return=l,l=d)}return s(l);case m:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(l,u.sibling),(d=o(u,c.children||[])).return=l,l=d;break e}n(l,u);break}t(l,u),u=u.sibling}(d=Hr(c,l.mode,d)).return=l,l=d}return s(l);case _:return v(l,u,c=(b=c._init)(c._payload),d)}if(j(c))return function(o,i,s,l){for(var u=null,c=null,d=i,h=i=0,m=null;null!==d&&h<s.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var g=p(o,d,s[h],l);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(o,d),i=a(g,i,h),null===c?u=g:c.sibling=g,c=g,d=m}if(h===s.length)return n(o,d),ao&&Zr(o,h),u;if(null===d){for(;h<s.length;h++)null!==(d=f(o,s[h],l))&&(i=a(d,i,h),null===c?u=d:c.sibling=d,c=d);return ao&&Zr(o,h),u}for(d=r(d);h<s.length;h++)null!==(m=y(d,o,h,s[h],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),i=a(m,i,h),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach(function(e){return t(o,e)}),ao&&Zr(o,h),u}(l,u,c,d);if(N(c)){if("function"!==typeof(b=N(c)))throw Error(i(150));return function(o,s,l,u){if(null==l)throw Error(i(151));for(var c=null,d=null,h=s,m=s=0,g=null,v=l.next();null!==h&&!v.done;m++,v=l.next()){h.index>m?(g=h,h=null):g=h.sibling;var b=p(o,h,v.value,u);if(null===b){null===h&&(h=g);break}e&&h&&null===b.alternate&&t(o,h),s=a(b,s,m),null===d?c=b:d.sibling=b,d=b,h=g}if(v.done)return n(o,h),ao&&Zr(o,m),c;if(null===h){for(;!v.done;m++,v=l.next())null!==(v=f(o,v.value,u))&&(s=a(v,s,m),null===d?c=v:d.sibling=v,d=v);return ao&&Zr(o,m),c}for(h=r(h);!v.done;m++,v=l.next())null!==(v=y(h,o,m,v.value,u))&&(e&&null!==v.alternate&&h.delete(null===v.key?m:v.key),s=a(v,s,m),null===d?c=v:d.sibling=v,d=v);return e&&h.forEach(function(e){return t(o,e)}),ao&&Zr(o,m),c}(l,u,c=b.call(c),d)}if("function"===typeof c.then)return v(l,u,Gi(c),d);if(c.$$typeof===x)return v(l,u,Ro(l,c),d);Zi(l,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(l,u.sibling),(d=o(u,c)).return=l,l=d):(n(l,u),(d=Br(c,l.mode,d)).return=l,l=d),s(l)):n(l,u)}return function(e,t,n,r){try{Ji=0;var o=v(e,t,n,r);return Yi=null,o}catch(i){if(i===qo||i===Qo)throw i;var a=zr(29,i,null,e.mode);return a.lanes=r,a.return=e,a}}}var ns=ts(!0),rs=ts(!1),os=I(null),as=null;function is(e){var t=e.alternate;U(cs,1&cs.current),U(os,e),null===as&&(null===t||null!==ha.current||null!==t.memoizedState)&&(as=e)}function ss(e){if(22===e.tag){if(U(cs,cs.current),U(os,e),null===as){var t=e.alternate;null!==t&&null!==t.memoizedState&&(as=e)}}else ls()}function ls(){U(cs,cs.current),U(os,os.current)}function us(e){M(os),as===e&&(as=null),M(cs)}var cs=I(0);function ds(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ps={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Au(),o=aa(r);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=ia(e,o,r))&&(zu(t,e,r),sa(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Au(),o=aa(r);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=ia(e,o,r))&&(zu(t,e,r),sa(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Au(),r=aa(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ia(e,r,n))&&(zu(t,e,n),sa(t,e,n))}};function hs(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!Jn(n,r)||!Jn(o,a))}function ms(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ps.enqueueReplaceState(t,t.state,null)}function gs(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=f({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var ys="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vs(e){ys(e)}function bs(e){console.error(e)}function ws(e){ys(e)}function xs(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function Ss(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function ks(e,t,n){return(n=aa(n)).tag=3,n.payload={element:null},n.callback=function(){xs(e,t)},n}function Es(e){return(e=aa(e)).tag=3,e}function Cs(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"===typeof o){var a=r.value;e.payload=function(){return o(a)},e.callback=function(){Ss(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){Ss(t,n,r),"function"!==typeof o&&(null===Eu?Eu=new Set([this]):Eu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var _s=Error(i(461)),Ts=!1;function Rs(e,t,n,r){t.child=null===e?rs(t,null,n,r):ns(t,e.child,n,r)}function Os(e,t,n,r,o){n=n.render;var a=t.ref;if("ref"in r){var i={};for(var s in r)"ref"!==s&&(i[s]=r[s])}else i=r;return _o(t),r=La(e,t,n,i,a,o),s=za(),null===e||Ts?(ao&&s&&to(t),t.flags|=1,Rs(e,t,r,o),t.child):(Fa(e,t,o),Ys(e,t,o))}function Ns(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Fr(a)||void 0!==a.defaultProps||null!==n.compare?((e=Mr(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Ps(e,t,a,r,o))}if(a=e.child,!Js(e,o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:Jn)(i,r)&&e.ref===t.ref)return Ys(e,t,o)}return t.flags|=1,(e=$r(a,r)).ref=t.ref,e.return=t,t.child=e}function Ps(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(Jn(a,r)&&e.ref===t.ref){if(Ts=!1,t.pendingProps=r=a,!Js(e,o))return t.lanes=e.lanes,Ys(e,t,o);0!==(131072&e.flags)&&(Ts=!0)}}return Ds(e,t,n,r,o)}function Ls(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==a?a.baseLanes|n:n,null!==e){for(o=t.child=e.child,a=0;null!==o;)a=a|o.lanes|o.childLanes,o=o.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return js(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,js(e,t,null!==a?a.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Vo(0,null!==a?a.cachePool:null),null!==a?ga(t,a):ya(),ss(t)}else null!==a?(Vo(0,a.cachePool),ga(t,a),ls(),t.memoizedState=null):(null!==e&&Vo(0,null),ya(),ls());return Rs(e,t,o,n),t.child}function js(e,t,n,r){var o=Ho();return o=null===o?null:{parent:jo._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&Vo(0,null),ya(),ss(t),null!==e&&Eo(e,t,r,!0),null}function As(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ds(e,t,n,r,o){return _o(t),n=La(e,t,n,r,void 0,o),r=za(),null===e||Ts?(ao&&r&&to(t),t.flags|=1,Rs(e,t,n,o),t.child):(Fa(e,t,o),Ys(e,t,o))}function zs(e,t,n,r,o,a){return _o(t),t.updateQueue=null,n=Aa(t,r,n,o),ja(e),r=za(),null===e||Ts?(ao&&r&&to(t),t.flags|=1,Rs(e,t,n,a),t.child):(Fa(e,t,a),Ys(e,t,a))}function Fs(e,t,n,r,o){if(_o(t),null===t.stateNode){var a=Ar,i=n.contextType;"object"===typeof i&&null!==i&&(a=To(i)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=ps,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},ra(t),i=n.contextType,a.context="object"===typeof i&&null!==i?To(i):Ar,a.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(fs(t,n,i,r),a.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(i=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),i!==a.state&&ps.enqueueReplaceState(a,a.state,null),da(t,r,a,o),ca(),a.state=t.memoizedState),"function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var s=t.memoizedProps,l=gs(n,s);a.props=l;var u=a.context,c=n.contextType;i=Ar,"object"===typeof c&&null!==c&&(i=To(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof a.getSnapshotBeforeUpdate,s=t.pendingProps!==s,c||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s||u!==i)&&ms(t,a,r,i),na=!1;var f=t.memoizedState;a.state=f,da(t,r,a,o),ca(),u=t.memoizedState,s||f!==u||na?("function"===typeof d&&(fs(t,n,d,r),u=t.memoizedState),(l=na||hs(t,n,l,r,f,u,i))?(c||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),a.props=r,a.state=u,a.context=i,r=l):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,oa(e,t),c=gs(n,i=t.memoizedProps),a.props=c,d=t.pendingProps,f=a.context,u=n.contextType,l=Ar,"object"===typeof u&&null!==u&&(l=To(u)),(u="function"===typeof(s=n.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(i!==d||f!==l)&&ms(t,a,r,l),na=!1,f=t.memoizedState,a.state=f,da(t,r,a,o),ca();var p=t.memoizedState;i!==d||f!==p||na||null!==e&&null!==e.dependencies&&Co(e.dependencies)?("function"===typeof s&&(fs(t,n,s,r),p=t.memoizedState),(c=na||hs(t,n,c,r,f,p,l)||null!==e&&null!==e.dependencies&&Co(e.dependencies))?(u||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=l,r=c):("function"!==typeof a.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,As(e,t),r=0!==(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=ns(t,e.child,null,o),t.child=ns(t,null,n,o)):Rs(e,t,n,o),t.memoizedState=a.state,e=t.child):e=Ys(e,t,o),e}function $s(e,t,n,r){return ho(),t.flags|=256,Rs(e,t,n,r),t.child}var Is={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ms(e){return{baseLanes:e,cachePool:Wo()}}function Us(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gu),e}function Bs(e,t,n){var r,o=t.pendingProps,a=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&cs.current)),r&&(a=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(ao){if(a?is(t):ls(),ao){var l,u=oo;if(l=u){e:{for(l=u,u=so;8!==l.nodeType;){if(!u){u=null;break e}if(null===(l=yd(l.nextSibling))){u=null;break e}}u=l}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Jr?{id:Gr,overflow:Xr}:null,retryLane:536870912,hydrationErrors:null},(l=zr(18,null,null,0)).stateNode=u,l.return=t,t.child=l,ro=t,oo=null,l=!0):l=!1}l||uo(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return gd(u)?t.lanes=32:t.lanes=536870912,null;us(t)}return u=o.children,o=o.fallback,a?(ls(),u=Vs({mode:"hidden",children:u},a=t.mode),o=Ur(o,a,n,null),u.return=t,o.return=t,u.sibling=o,t.child=u,(a=t.child).memoizedState=Ms(n),a.childLanes=Us(e,r,n),t.memoizedState=Is,o):(is(t),Hs(t,u))}if(null!==(l=e.memoizedState)&&null!==(u=l.dehydrated)){if(s)256&t.flags?(is(t),t.flags&=-257,t=Ws(e,t,n)):null!==t.memoizedState?(ls(),t.child=e.child,t.flags|=128,t=null):(ls(),a=o.fallback,u=t.mode,o=Vs({mode:"visible",children:o.children},u),(a=Ur(a,u,n,null)).flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,ns(t,e.child,null,n),(o=t.child).memoizedState=Ms(n),o.childLanes=Us(e,r,n),t.memoizedState=Is,t=a);else if(is(t),gd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(o=Error(i(419))).stack="",o.digest=r,go({value:o,source:null,stack:null}),t=Ws(e,t,n)}else if(Ts||Eo(e,t,n,!1),r=0!==(n&e.childLanes),Ts||r){if(null!==(r=ru)&&(0!==(o=0!==((o=0!==(42&(o=n&-n))?1:Re(o))&(r.suspendedLanes|n))?0:o)&&o!==l.retryLane))throw l.retryLane=o,Pr(e,o),zu(r,e,o),_s;"$?"===u.data||Ku(),t=Ws(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,oo=yd(u.nextSibling),ro=t,ao=!0,io=null,so=!1,null!==e&&(Qr[Yr++]=Gr,Qr[Yr++]=Xr,Qr[Yr++]=Jr,Gr=e.id,Xr=e.overflow,Jr=t),(t=Hs(t,o.children)).flags|=4096);return t}return a?(ls(),a=o.fallback,u=t.mode,c=(l=e.child).sibling,(o=$r(l,{mode:"hidden",children:o.children})).subtreeFlags=65011712&l.subtreeFlags,null!==c?a=$r(c,a):(a=Ur(a,u,n,null)).flags|=2,a.return=t,o.return=t,o.sibling=a,t.child=o,o=a,a=t.child,null===(u=e.child.memoizedState)?u=Ms(n):(null!==(l=u.cachePool)?(c=jo._currentValue,l=l.parent!==c?{parent:c,pool:c}:l):l=Wo(),u={baseLanes:u.baseLanes|n,cachePool:l}),a.memoizedState=u,a.childLanes=Us(e,r,n),t.memoizedState=Is,o):(is(t),e=(n=e.child).sibling,(n=$r(n,{mode:"visible",children:o.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hs(e,t){return(t=Vs({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Vs(e,t){return(e=zr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Ws(e,t,n){return ns(t,e.child,null,n),(e=Hs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function qs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),So(e.return,t,n)}function Ks(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Qs(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(Rs(e,t,r.children,n),0!==(2&(r=cs.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&qs(e,n,t);else if(19===e.tag)qs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(U(cs,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ds(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ks(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ds(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ks(t,!0,n,null,a);break;case"together":Ks(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ys(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Eo(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=$r(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=$r(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Js(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Co(e))}function Gs(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Ts=!0;else{if(!Js(e,n)&&0===(128&t.flags))return Ts=!1,function(e,t,n){switch(t.tag){case 3:q(t,t.stateNode.containerInfo),wo(0,jo,e.memoizedState.cache),ho();break;case 27:case 5:Q(t);break;case 4:q(t,t.stateNode.containerInfo);break;case 10:wo(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(is(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Bs(e,t,n):(is(t),null!==(e=Ys(e,t,n))?e.sibling:null);is(t);break;case 19:var o=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Eo(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return Qs(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),U(cs,cs.current),r)break;return null;case 22:case 23:return t.lanes=0,Ls(e,t,n);case 24:wo(0,jo,e.memoizedState.cache)}return Ys(e,t,n)}(e,t,n);Ts=0!==(131072&e.flags)}else Ts=!1,ao&&0!==(1048576&t.flags)&&eo(t,Kr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((o=r.$$typeof)===S){t.tag=11,t=Os(null,t,r,e,n);break e}if(o===C){t.tag=14,t=Ns(null,t,r,e,n);break e}}throw t=L(r)||r,Error(i(306,t,""))}Fr(r)?(e=gs(r,e),t.tag=1,t=Fs(null,t,r,e,n)):(t.tag=0,t=Ds(null,t,r,e,n))}return t;case 0:return Ds(e,t,t.type,t.pendingProps,n);case 1:return Fs(e,t,r=t.type,o=gs(r,t.pendingProps),n);case 3:e:{if(q(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var a=t.memoizedState;o=a.element,oa(e,t),da(t,r,null,n);var s=t.memoizedState;if(r=s.cache,wo(0,jo,r),r!==a.cache&&ko(t,[jo],n,!0),ca(),r=s.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=$s(e,t,r,n);break e}if(r!==o){go(o=Er(Error(i(424)),t)),t=$s(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(oo=yd(e.firstChild),ro=t,ao=!0,io=null,so=!0,n=rs(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===o){t=Ys(e,t,n);break e}Rs(e,t,r,n)}t=t.child}return t;case 26:return As(e,t),null===e?(n=Rd(t.type,null,t.pendingProps,null))?t.memoizedState=n:ao||(n=t.type,e=t.pendingProps,(r=rd(V.current).createElement(n))[Le]=t,r[je]=e,ed(r,n,e),We(r),t.stateNode=r):t.memoizedState=Rd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Q(t),null===e&&ao&&(r=t.stateNode=wd(t.type,t.pendingProps,V.current),ro=t,so=!0,o=oo,pd(t.type)?(vd=o,oo=yd(r.firstChild)):oo=o),Rs(e,t,t.pendingProps.children,n),As(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ao&&((o=r=oo)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ie])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==o.rel||e.getAttribute("href")!==(null==o.href||""===o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===a)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,so))?(t.stateNode=r,ro=t,oo=yd(r.firstChild),so=!1,o=!0):o=!1),o||uo(t)),Q(t),o=t.type,a=t.pendingProps,s=null!==e?e.memoizedProps:null,r=a.children,id(o,a)?r=null:null!==s&&id(o,s)&&(t.flags|=32),null!==t.memoizedState&&(o=La(e,t,Da,null,null,n),Qd._currentValue=o),As(e,t),Rs(e,t,r,n),t.child;case 6:return null===e&&ao&&((e=n=oo)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,so))?(t.stateNode=n,ro=t,oo=null,e=!0):e=!1),e||uo(t)),null;case 13:return Bs(e,t,n);case 4:return q(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ns(t,null,r,n):Rs(e,t,r,n),t.child;case 11:return Os(e,t,t.type,t.pendingProps,n);case 7:return Rs(e,t,t.pendingProps,n),t.child;case 8:case 12:return Rs(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,wo(0,t.type,r.value),Rs(e,t,r.children,n),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,_o(t),r=r(o=To(o)),t.flags|=1,Rs(e,t,r,n),t.child;case 14:return Ns(e,t,t.type,t.pendingProps,n);case 15:return Ps(e,t,t.type,t.pendingProps,n);case 19:return Qs(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Vs(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=$r(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ls(e,t,n);case 24:return _o(t),r=To(jo),null===e?(null===(o=Ho())&&(o=ru,a=Ao(),o.pooledCache=a,a.refCount++,null!==a&&(o.pooledCacheLanes|=n),o=a),t.memoizedState={parent:r,cache:o},ra(t),wo(0,jo,o)):(0!==(e.lanes&n)&&(oa(e,t),da(t,null,null,n),ca()),o=e.memoizedState,a=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=o),wo(0,jo,r)):(r=a.cache,wo(0,jo,r),r!==o.cache&&ko(t,[jo],n,!0))),Rs(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Xs(e){e.flags|=4}function Zs(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Ud(t)){if(null!==(t=os.current)&&((4194048&au)===au?null!==as:(62914560&au)!==au&&0===(536870912&au)||t!==as))throw Zo=Yo,Ko;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?ke():536870912,e.lanes|=t,yu|=t)}function tl(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=65011712&o.subtreeFlags,r|=65011712&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(no(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),xo(jo),K(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(po(t)?Xs(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,mo())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Xs(t),null!==n?(nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Xs(t),nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Xs(t),nl(t),t.flags&=-16777217),null;case 27:Y(t),n=V.current;var o=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}e=B.current,po(t)?co(t):(e=wd(o,r,n),t.stateNode=e,Xs(t))}return nl(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}if(e=B.current,po(t))co(t);else{switch(o=rd(V.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?o.createElement(n,{is:r.is}):o.createElement(n)}}e[Le]=t,e[je]=r;e:for(o=t.child;null!==o;){if(5===o.tag||6===o.tag)e.appendChild(o.stateNode);else if(4!==o.tag&&27!==o.tag&&null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;null===o.sibling;){if(null===o.return||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xs(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=V.current,po(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(o=ro))switch(o.tag){case 27:case 5:r=o.memoizedProps}e[Le]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Jc(e.nodeValue,n)))||uo(t)}else(e=rd(e).createTextNode(r))[Le]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(o=po(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(i(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(i(317));o[Le]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),o=!1}else o=mo(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=o),o=!0;if(!o)return 256&t.flags?(us(t),t):(us(t),null)}if(us(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){o=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(o=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==o&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return K(),null===e&&Mc(t.stateNode.containerInfo),nl(t),null;case 10:return xo(t.type),nl(t),null;case 19:if(M(cs),null===(o=t.memoizedState))return nl(t),null;if(r=0!==(128&t.flags),null===(a=o.rendering))if(r)tl(o,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=ds(e))){for(t.flags|=128,tl(o,!1),e=a.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ir(n,e),n=n.sibling;return U(cs,1&cs.current|2),t.child}e=e.sibling}null!==o.tail&&te()>Su&&(t.flags|=128,r=!0,tl(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ds(a))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(o,!0),null===o.tail&&"hidden"===o.tailMode&&!a.alternate&&!ao)return nl(t),null}else 2*te()-o.renderingStartTime>Su&&536870912!==n&&(t.flags|=128,r=!0,tl(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=o.last)?e.sibling=a:t.child=a,o.last=a)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=te(),t.sibling=null,e=cs.current,U(cs,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return us(t),va(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&M(Bo),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),xo(jo),nl(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function ol(e,t){switch(no(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return xo(jo),K(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(us(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return M(cs),null;case 4:return K(),null;case 10:return xo(t.type),null;case 22:case 23:return us(t),va(),null!==e&&M(Bo),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return xo(jo),null;default:return null}}function al(e,t){switch(no(t),t.tag){case 3:xo(jo),K();break;case 26:case 27:case 5:Y(t);break;case 4:K();break;case 13:us(t);break;case 19:M(cs);break;case 10:xo(t.type);break;case 22:case 23:us(t),va(),null!==e&&M(Bo);break;case 24:xo(jo)}}function il(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var a=n.create,i=n.inst;r=a(),i.destroy=r}n=n.next}while(n!==o)}}catch(s){cc(t,t.return,s)}}function sl(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var a=o.next;r=a;do{if((r.tag&e)===e){var i=r.inst,s=i.destroy;if(void 0!==s){i.destroy=void 0,o=t;var l=n,u=s;try{u()}catch(c){cc(o,l,c)}}}r=r.next}while(r!==a)}}catch(c){cc(t,t.return,c)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{pa(t,n)}catch(r){cc(e,e.return,r)}}}function ul(e,t,n){n.props=gs(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){cc(e,t,r)}}function cl(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(o){cc(e,t,o)}}function dl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(o){cc(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(a){cc(e,t,a)}else n.current=null}function fl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){cc(e,e.return,o)}}function pl(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,a=null,s=null,l=null,u=null,c=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(h)||Xc(e,t,h,null,r,f)}}for(var p in r){var h=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":a=h;break;case"name":o=h;break;case"checked":c=h;break;case"defaultChecked":d=h;break;case"value":s=h;break;case"defaultValue":l=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(i(137,t));break;default:h!==f&&Xc(e,t,p,h,r,f)}}return void gt(e,s,l,u,c,d,a,o);case"select":for(a in h=s=l=p=null,n)if(u=n[a],n.hasOwnProperty(a)&&null!=u)switch(a){case"value":break;case"multiple":h=u;default:r.hasOwnProperty(a)||Xc(e,t,a,null,r,u)}for(o in r)if(a=r[o],u=n[o],r.hasOwnProperty(o)&&(null!=a||null!=u))switch(o){case"value":p=a;break;case"defaultValue":l=a;break;case"multiple":s=a;default:a!==u&&Xc(e,t,o,a,r,u)}return t=l,n=s,r=h,void(null!=p?bt(e,!!n,p,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in h=p=null,n)if(o=n[l],n.hasOwnProperty(l)&&null!=o&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Xc(e,t,l,null,r,o)}for(s in r)if(o=r[s],a=n[s],r.hasOwnProperty(s)&&(null!=o||null!=a))switch(s){case"value":p=o;break;case"defaultValue":h=o;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(i(91));break;default:o!==a&&Xc(e,t,s,o,r,a)}return void wt(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Xc(e,t,m,null,r,p);for(u in r)if(p=r[u],h=n[u],r.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))if("selected"===u)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Xc(e,t,u,p,r,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Xc(e,t,g,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:Xc(e,t,c,p,r,h)}return;default:if(_t(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zc(e,t,y,void 0,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Zc(e,t,d,p,r,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Xc(e,t,v,null,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Xc(e,t,f,p,r,h)}(r,e.type,n,t),r[je]=t}catch(o){cc(e,e.return,o)}}function hl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function ml(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Gc));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gl(e,t,n),e=e.sibling;null!==e;)gl(e,t,n),e=e.sibling}function yl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(yl(e,t,n),e=e.sibling;null!==e;)yl(e,t,n),e=e.sibling}function vl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);ed(t,r,n),t[Le]=e,t[je]=n}catch(a){cc(e,e.return,a)}}var bl=!1,wl=!1,xl=!1,Sl="function"===typeof WeakSet?WeakSet:Set,kl=null;function El(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Fl(e,n),4&r&&il(5,n);break;case 1:if(Fl(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){cc(n,n.return,i)}else{var o=gs(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){cc(n,n.return,s)}}64&r&&ll(n),512&r&&cl(n,n.return);break;case 3:if(Fl(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{pa(e,t)}catch(i){cc(n,n.return,i)}}break;case 27:null===t&&4&r&&vl(n);case 26:case 5:Fl(e,n),null===t&&4&r&&fl(n),512&r&&cl(n,n.return);break;case 12:Fl(e,n);break;case 13:Fl(e,n),4&r&&Nl(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=hc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||wl,o=bl;var a=wl;bl=r,(wl=t)&&!a?Il(e,n,0!==(8772&n.subtreeFlags)):Fl(e,n),bl=o,wl=a}break;case 30:break;default:Fl(e,n)}}function Cl(e){var t=e.alternate;null!==t&&(e.alternate=null,Cl(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Me(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var _l=null,Tl=!1;function Rl(e,t,n){for(n=n.child;null!==n;)Ol(e,t,n),n=n.sibling}function Ol(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(a){}switch(n.tag){case 26:wl||dl(n,t),Rl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||dl(n,t);var r=_l,o=Tl;pd(n.type)&&(_l=n.stateNode,Tl=!1),Rl(e,t,n),xd(n.stateNode),_l=r,Tl=o;break;case 5:wl||dl(n,t);case 6:if(r=_l,o=Tl,_l=null,Rl(e,t,n),Tl=o,null!==(_l=r))if(Tl)try{(9===_l.nodeType?_l.body:"HTML"===_l.nodeName?_l.ownerDocument.body:_l).removeChild(n.stateNode)}catch(i){cc(n,t,i)}else try{_l.removeChild(n.stateNode)}catch(i){cc(n,t,i)}break;case 18:null!==_l&&(Tl?(hd(9===(e=_l).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Rf(e)):hd(_l,n.stateNode));break;case 4:r=_l,o=Tl,_l=n.stateNode.containerInfo,Tl=!0,Rl(e,t,n),_l=r,Tl=o;break;case 0:case 11:case 14:case 15:wl||sl(2,n,t),wl||sl(4,n,t),Rl(e,t,n);break;case 1:wl||(dl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&ul(n,t,r)),Rl(e,t,n);break;case 21:Rl(e,t,n);break;case 22:wl=(r=wl)||null!==n.memoizedState,Rl(e,t,n),wl=r;break;default:Rl(e,t,n)}}function Nl(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Rf(e)}catch(n){cc(t,t.return,n)}}function Pl(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Sl),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Sl),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var r=mc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Ll(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r],a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 27:if(pd(l.type)){_l=l.stateNode,Tl=!1;break e}break;case 5:_l=l.stateNode,Tl=!1;break e;case 3:case 4:_l=l.stateNode.containerInfo,Tl=!0;break e}l=l.return}if(null===_l)throw Error(i(160));Ol(a,s,o),_l=null,Tl=!1,null!==(a=o.alternate)&&(a.return=null),o.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Al(t,e),t=t.sibling}var jl=null;function Al(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ll(t,e),Dl(e),4&r&&(sl(3,e,e.return),il(3,e),sl(5,e,e.return));break;case 1:Ll(t,e),Dl(e),512&r&&(wl||null===n||dl(n,n.return)),64&r&&bl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var o=jl;if(Ll(t,e),Dl(e),512&r&&(wl||null===n||dl(n,n.return)),4&r){var a=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":(!(a=o.getElementsByTagName("title")[0])||a[Ie]||a[Le]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=o.createElement(r),o.head.insertBefore(a,o.querySelector("head > title"))),ed(a,r,n),a[Le]=e,We(a),r=a;break e;case"link":var s=Id("link","href",o).get(r+(n.href||""));if(s)for(var l=0;l<s.length;l++)if((a=s[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){s.splice(l,1);break t}ed(a=o.createElement(r),r,n),o.head.appendChild(a);break;case"meta":if(s=Id("meta","content",o).get(r+(n.content||"")))for(l=0;l<s.length;l++)if((a=s[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){s.splice(l,1);break t}ed(a=o.createElement(r),r,n),o.head.appendChild(a);break;default:throw Error(i(468,r))}a[Le]=e,We(a),r=a}e.stateNode=r}else Md(o,e.type,e.stateNode);else e.stateNode=Ad(o,r,e.memoizedProps);else a!==r?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===r?Md(o,e.type,e.stateNode):Ad(o,r,e.memoizedProps)):null===r&&null!==e.stateNode&&pl(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ll(t,e),Dl(e),512&r&&(wl||null===n||dl(n,n.return)),null!==n&&4&r&&pl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ll(t,e),Dl(e),512&r&&(wl||null===n||dl(n,n.return)),32&e.flags){o=e.stateNode;try{St(o,"")}catch(h){cc(e,e.return,h)}}4&r&&null!=e.stateNode&&pl(e,o=e.memoizedProps,null!==n?n.memoizedProps:o),1024&r&&(xl=!0);break;case 6:if(Ll(t,e),Dl(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){cc(e,e.return,h)}}break;case 3:if($d=null,o=jl,jl=Ed(t.containerInfo),Ll(t,e),jl=o,Dl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Rf(t.containerInfo)}catch(h){cc(e,e.return,h)}xl&&(xl=!1,zl(e));break;case 4:r=jl,jl=Ed(e.stateNode.containerInfo),Ll(t,e),Dl(e),jl=r;break;case 12:default:Ll(t,e),Dl(e);break;case 13:Ll(t,e),Dl(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(xu=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Pl(e,r)));break;case 22:o=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=bl,d=wl;if(bl=c||o,wl=d||u,Ll(t,e),wl=d,bl=c,Dl(e),8192&r)e:for(t=e.stateNode,t._visibility=o?-2&t._visibility:1|t._visibility,o&&(null===n||u||bl||wl||$l(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(a=u.stateNode,o)"function"===typeof(s=a.style).setProperty?s.setProperty("display","none","important"):s.display="none";else{l=u.stateNode;var f=u.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;l.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(h){cc(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=o?"":u.memoizedProps}catch(h){cc(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Pl(e,n))));break;case 19:Ll(t,e),Dl(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Pl(e,r)));case 30:case 21:}}function Dl(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(hl(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var o=n.stateNode;yl(e,ml(e),o);break;case 5:var a=n.stateNode;32&n.flags&&(St(a,""),n.flags&=-33),yl(e,ml(e),a);break;case 3:case 4:var s=n.stateNode.containerInfo;gl(e,ml(e),s);break;default:throw Error(i(161))}}catch(l){cc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function zl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;zl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Fl(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)El(e,t.alternate,t),t=t.sibling}function $l(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:sl(4,t,t.return),$l(t);break;case 1:dl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&ul(t,t.return,n),$l(t);break;case 27:xd(t.stateNode);case 26:case 5:dl(t,t.return),$l(t);break;case 22:null===t.memoizedState&&$l(t);break;default:$l(t)}e=e.sibling}}function Il(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,a=t,i=a.flags;switch(a.tag){case 0:case 11:case 15:Il(o,a,n),il(4,a);break;case 1:if(Il(o,a,n),"function"===typeof(o=(r=a).stateNode).componentDidMount)try{o.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(o=(r=a).updateQueue)){var s=r.stateNode;try{var l=o.shared.hiddenCallbacks;if(null!==l)for(o.shared.hiddenCallbacks=null,o=0;o<l.length;o++)fa(l[o],s)}catch(u){cc(r,r.return,u)}}n&&64&i&&ll(a),cl(a,a.return);break;case 27:vl(a);case 26:case 5:Il(o,a,n),n&&null===r&&4&i&&fl(a),cl(a,a.return);break;case 12:Il(o,a,n);break;case 13:Il(o,a,n),n&&4&i&&Nl(o,a);break;case 22:null===a.memoizedState&&Il(o,a,n),cl(a,a.return);break;case 30:break;default:Il(o,a,n)}t=t.sibling}}function Ml(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Do(n))}function Ul(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Do(e))}function Bl(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hl(e,t,n,r),t=t.sibling}function Hl(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:Bl(e,t,n,r),2048&o&&il(9,t);break;case 1:case 13:default:Bl(e,t,n,r);break;case 3:Bl(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Do(e)));break;case 12:if(2048&o){Bl(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,i=a.id,s=a.onPostCommit;"function"===typeof s&&s(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){cc(t,t.return,l)}}else Bl(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,i=t.alternate,null!==t.memoizedState?2&a._visibility?Bl(e,t,n,r):Wl(e,t):2&a._visibility?Bl(e,t,n,r):(a._visibility|=2,Vl(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&o&&Ml(i,t);break;case 24:Bl(e,t,n,r),2048&o&&Ul(t.alternate,t)}}function Vl(e,t,n,r,o){for(o=o&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,i=t,s=n,l=r,u=i.flags;switch(i.tag){case 0:case 11:case 15:Vl(a,i,s,l,o),il(8,i);break;case 23:break;case 22:var c=i.stateNode;null!==i.memoizedState?2&c._visibility?Vl(a,i,s,l,o):Wl(a,i):(c._visibility|=2,Vl(a,i,s,l,o)),o&&2048&u&&Ml(i.alternate,i);break;case 24:Vl(a,i,s,l,o),o&&2048&u&&Ul(i.alternate,i);break;default:Vl(a,i,s,l,o)}t=t.sibling}}function Wl(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:Wl(n,r),2048&o&&Ml(r.alternate,r);break;case 24:Wl(n,r),2048&o&&Ul(r.alternate,r);break;default:Wl(n,r)}t=t.sibling}}var ql=8192;function Kl(e){if(e.subtreeFlags&ql)for(e=e.child;null!==e;)Ql(e),e=e.sibling}function Ql(e){switch(e.tag){case 26:Kl(e),e.flags&ql&&null!==e.memoizedState&&function(e,t,n){if(null===Bd)throw Error(i(475));var r=Bd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var o=Od(n.href),a=e.querySelector(Nd(o));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Vd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=a,void We(a);a=e.ownerDocument||e,n=Pd(n),(o=Sd.get(o))&&zd(n,o),We(a=a.createElement("link"));var s=a;s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(a,"link",n),t.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Vd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(jl,e.memoizedState,e.memoizedProps);break;case 5:default:Kl(e);break;case 3:case 4:var t=jl;jl=Ed(e.stateNode.containerInfo),Kl(e),jl=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=ql,ql=16777216,Kl(e),ql=t):Kl(e))}}function Yl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Jl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];kl=r,Zl(r,e)}Yl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Gl(e),e=e.sibling}function Gl(e){switch(e.tag){case 0:case 11:case 15:Jl(e),2048&e.flags&&sl(9,e,e.return);break;case 3:case 12:default:Jl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Xl(e)):Jl(e)}}function Xl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];kl=r,Zl(r,e)}Yl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:sl(8,t,t.return),Xl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Xl(t));break;default:Xl(t)}e=e.sibling}}function Zl(e,t){for(;null!==kl;){var n=kl;switch(n.tag){case 0:case 11:case 15:sl(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Do(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,kl=r;else e:for(n=e;null!==kl;){var o=(r=kl).sibling,a=r.return;if(Cl(r),r===n){kl=null;break e}if(null!==o){o.return=a,kl=o;break e}kl=a}}}var eu={getCacheForType:function(e){var t=To(jo),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"===typeof WeakMap?WeakMap:Map,nu=0,ru=null,ou=null,au=0,iu=0,su=null,lu=!1,uu=!1,cu=!1,du=0,fu=0,pu=0,hu=0,mu=0,gu=0,yu=0,vu=null,bu=null,wu=!1,xu=0,Su=1/0,ku=null,Eu=null,Cu=0,_u=null,Tu=null,Ru=0,Ou=0,Nu=null,Pu=null,Lu=0,ju=null;function Au(){if(0!==(2&nu)&&0!==au)return au&-au;if(null!==A.T){return 0!==$o?$o:Oc()}return Ne()}function Du(){0===gu&&(gu=0===(536870912&au)||ao?Se():536870912);var e=os.current;return null!==e&&(e.flags|=32),gu}function zu(e,t,n){(e!==ru||2!==iu&&9!==iu)&&null===e.cancelPendingCommit||(Hu(e,0),Mu(e,au,gu,!1)),Ce(e,n),0!==(2&nu)&&e===ru||(e===ru&&(0===(2&nu)&&(hu|=n),4===fu&&Mu(e,au,gu,!1)),Sc(e))}function Fu(e,t,n){if(0!==(6&nu))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),o=r?function(e,t){var n=nu;nu|=2;var r=Wu(),o=qu();ru!==e||au!==t?(ku=null,Su=te()+500,Hu(e,t)):uu=we(e,t);e:for(;;)try{if(0!==iu&&null!==ou){t=ou;var a=su;t:switch(iu){case 1:iu=0,su=null,Zu(e,t,a,1);break;case 2:case 9:if(Jo(a)){iu=0,su=null,Xu(t);break}t=function(){2!==iu&&9!==iu||ru!==e||(iu=7),Sc(e)},a.then(t,t);break e;case 3:iu=7;break e;case 4:iu=5;break e;case 7:Jo(a)?(iu=0,su=null,Xu(t)):(iu=0,su=null,Zu(e,t,a,7));break;case 5:var s=null;switch(ou.tag){case 26:s=ou.memoizedState;case 5:case 27:var l=ou;if(!s||Ud(s)){iu=0,su=null;var u=l.sibling;if(null!==u)ou=u;else{var c=l.return;null!==c?(ou=c,ec(c)):ou=null}break t}}iu=0,su=null,Zu(e,t,a,5);break;case 6:iu=0,su=null,Zu(e,t,a,6);break;case 8:Bu(),fu=6;break e;default:throw Error(i(462))}}Ju();break}catch(d){Vu(e,d)}return bo=vo=null,A.H=r,A.A=o,nu=n,null!==ou?0:(ru=null,au=0,Rr(),fu)}(e,t):Qu(e,t,!0),a=r;;){if(0===o){uu&&!r&&Mu(e,t,0,!1);break}if(n=e.current.alternate,!a||Iu(n)){if(2===o){if(a=t,e.errorRecoveryDisabledLanes&a)var s=0;else s=0!==(s=-536870913&e.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){t=s;e:{var l=e;o=vu;var u=l.current.memoizedState.isDehydrated;if(u&&(Hu(l,s).flags|=256),2!==(s=Qu(l,s,!1))){if(cu&&!u){l.errorRecoveryDisabledLanes|=a,hu|=a,o=4;break e}a=bu,bu=o,null!==a&&(null===bu?bu=a:bu.push.apply(bu,a))}o=s}if(a=!1,2!==o)continue}}if(1===o){Hu(e,0),Mu(e,t,0,!0);break}e:{switch(r=e,a=o){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Mu(r,t,gu,!lu);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(o=xu+300-te())){if(Mu(r,t,gu,!lu),0!==be(r,0,!0))break e;r.timeoutHandle=ld($u.bind(null,r,n,bu,ku,wu,t,gu,hu,yu,lu,a,2,-0,0),o)}else $u(r,n,bu,ku,wu,t,gu,hu,yu,lu,a,0,-0,0)}break}o=Qu(e,t,!1),a=!1}Sc(e)}function $u(e,t,n,r,o,a,s,l,u,c,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Bd={stylesheets:null,count:0,unsuspend:Hd},Ql(t),null!==(f=function(){if(null===Bd)throw Error(i(475));var e=Bd;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,a,n,r,o,s,l,u,d,1,p,h)),void Mu(e,a,s,!c);nc(e,t,a,n,r,o,s,l,u)}function Iu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!Yn(a(),o))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Mu(e,t,n,r){t&=~mu,t&=~hu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var a=31-pe(o),i=1<<a;r[a]=-1,o&=~i}0!==n&&_e(e,n,t)}function Uu(){return 0!==(6&nu)||(kc(0,!1),!1)}function Bu(){if(null!==ou){if(0===iu)var e=ou.return;else bo=vo=null,$a(e=ou),Yi=null,Ji=0,e=ou;for(;null!==e;)al(e.alternate,e),e=e.return;ou=null}}function Hu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Bu(),ru=e,ou=n=$r(e.current,null),au=t,iu=0,su=null,lu=!1,uu=we(e,t),cu=!1,yu=gu=mu=hu=pu=fu=0,bu=vu=null,wu=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-pe(r),a=1<<o;t|=e[o],r&=~a}return du=t,Rr(),n}function Vu(e,t){wa=null,A.H=Wi,t===qo||t===Qo?(t=ea(),iu=3):t===Ko?(t=ea(),iu=4):iu=t===_s?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,su=t,null===ou&&(fu=1,xs(e,Er(t,e.current)))}function Wu(){var e=A.H;return A.H=Wi,null===e?Wi:e}function qu(){var e=A.A;return A.A=eu,e}function Ku(){fu=4,lu||(4194048&au)!==au&&null!==os.current||(uu=!0),0===(134217727&pu)&&0===(134217727&hu)||null===ru||Mu(ru,au,gu,!1)}function Qu(e,t,n){var r=nu;nu|=2;var o=Wu(),a=qu();ru===e&&au===t||(ku=null,Hu(e,t)),t=!1;var i=fu;e:for(;;)try{if(0!==iu&&null!==ou){var s=ou,l=su;switch(iu){case 8:Bu(),i=6;break e;case 3:case 2:case 9:case 6:null===os.current&&(t=!0);var u=iu;if(iu=0,su=null,Zu(e,s,l,u),n&&uu){i=0;break e}break;default:u=iu,iu=0,su=null,Zu(e,s,l,u)}}Yu(),i=fu;break}catch(c){Vu(e,c)}return t&&e.shellSuspendCounter++,bo=vo=null,nu=r,A.H=o,A.A=a,null===ou&&(ru=null,au=0,Rr()),i}function Yu(){for(;null!==ou;)Gu(ou)}function Ju(){for(;null!==ou&&!Z();)Gu(ou)}function Gu(e){var t=Gs(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):ou=t}function Xu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=zs(n,t,t.pendingProps,t.type,void 0,au);break;case 11:t=zs(n,t,t.pendingProps,t.type.render,t.ref,au);break;case 5:$a(t);default:al(n,t),t=Gs(n,t=ou=Ir(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):ou=t}function Zu(e,t,n,r){bo=vo=null,$a(t),Yi=null,Ji=0;var o=t.return;try{if(function(e,t,n,r,o){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Eo(t,n,o,!0),null!==(n=os.current)){switch(n.tag){case 13:return null===as?Ku():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=o,r===Yo?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,o)),!1;case 22:return n.flags|=65536,r===Yo?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,o)),!1}throw Error(i(435,n.tag))}return dc(e,r,o),Ku(),!1}if(ao)return null!==(t=os.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==lo&&go(Er(e=Error(i(422),{cause:r}),n))):(r!==lo&&go(Er(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,o&=-o,e.lanes|=o,r=Er(r,n),la(e,o=ks(e.stateNode,r,o)),4!==fu&&(fu=2)),!1;var a=Error(i(520),{cause:r});if(a=Er(a,n),null===vu?vu=[a]:vu.push(a),4!==fu&&(fu=2),null===t)return!0;r=Er(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,la(n,e=ks(n.stateNode,r,e)),!1;case 1:if(t=n.type,a=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===Eu||!Eu.has(a))))return n.flags|=65536,o&=-o,n.lanes|=o,Cs(o=Es(o),e,n,r),la(n,o),!1}n=n.return}while(null!==n);return!1}(e,o,t,n,au))return fu=1,xs(e,Er(n,e.current)),void(ou=null)}catch(a){if(null!==o)throw ou=o,a;return fu=1,xs(e,Er(n,e.current)),void(ou=null)}32768&t.flags?(ao||1===r?e=!0:uu||0!==(536870912&au)?e=!1:(lu=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=os.current)&&13===r.tag&&(r.flags|=16384))),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(0!==(32768&t.flags))return void tc(t,lu);e=t.return;var n=rl(t.alternate,t,du);if(null!==n)return void(ou=n);if(null!==(t=t.sibling))return void(ou=t);ou=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=ol(e.alternate,e);if(null!==n)return n.flags&=32767,void(ou=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ou=e);ou=e=n}while(null!==e);fu=6,ou=null}function nc(e,t,n,r,o,a,s,l,u){e.cancelPendingCommit=null;do{sc()}while(0!==Cu);if(0!==(6&nu))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(a=t.lanes|t.childLanes,function(e,t,n,r,o,a){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var s=e.entanglements,l=e.expirationTimes,u=e.hiddenUpdates;for(n=i&~n;0<n;){var c=31-pe(n),d=1<<c;s[c]=0,l[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&_e(e,r,0),0!==a&&0===o&&0!==e.tag&&(e.suspendedLanes|=a&~(i&~t))}(e,n,a|=Tr,s,l,u),e===ru&&(ou=ru=null,au=0),Tu=t,_u=e,Ru=n,Ou=a,Nu=o,Pu=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,G(ae,function(){return lc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=A.T,A.T=null,o=D.p,D.p=2,s=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(l=s+o),f!==a||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===o&&(l=s),p===a&&++d===r&&(u=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,kl=t;null!==kl;)if(e=(t=kl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,kl=e;else for(;null!==kl;){switch(a=(t=kl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,n=t,o=a.memoizedProps,a=a.memoizedState,r=n.stateNode;try{var m=gs(n.type,o,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(y){cc(n,n.return,y)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,kl=e;break}kl=t.return}}(e,t)}finally{nu=s,D.p=o,A.T=r}}Cu=1,rc(),oc(),ac()}}function rc(){if(1===Cu){Cu=0;var e=_u,t=Tu,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=A.T,A.T=null;var r=D.p;D.p=2;var o=nu;nu|=4;try{Al(t,e);var a=nd,i=er(e.containerInfo),s=a.focusedElem,l=a.selectionRange;if(i!==s&&s&&s.ownerDocument&&Zn(s.ownerDocument.documentElement,s)){if(null!==l&&tr(s)){var u=l.start,c=l.end;if(void 0===c&&(c=u),"selectionStart"in s)s.selectionStart=u,s.selectionEnd=Math.min(c,s.value.length);else{var d=s.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=s.textContent.length,m=Math.min(l.start,h),g=void 0===l.end?m:Math.min(l.end,h);!p.extend&&m>g&&(i=g,g=m,m=i);var y=Xn(s,m),v=Xn(s,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(d=[],p=s;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof s.focus&&s.focus(),s=0;s<d.length;s++){var w=d[s];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nf=!!td,nd=td=null}finally{nu=o,D.p=r,A.T=n}}e.current=t,Cu=2}}function oc(){if(2===Cu){Cu=0;var e=_u,t=Tu,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=A.T,A.T=null;var r=D.p;D.p=2;var o=nu;nu|=4;try{El(e,t.alternate,t)}finally{nu=o,D.p=r,A.T=n}}Cu=3}}function ac(){if(4===Cu||3===Cu){Cu=0,ee();var e=_u,t=Tu,n=Ru,r=Pu;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Cu=5:(Cu=0,Tu=_u=null,ic(e,e.pendingLanes));var o=e.pendingLanes;if(0===o&&(Eu=null),Oe(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==r){t=A.T,o=D.p,D.p=2,A.T=null;try{for(var a=e.onRecoverableError,i=0;i<r.length;i++){var s=r[i];a(s.value,{componentStack:s.stack})}}finally{A.T=t,D.p=o}}0!==(3&Ru)&&sc(),Sc(e),o=e.pendingLanes,0!==(4194090&n)&&0!==(42&o)?e===ju?Lu++:(Lu=0,ju=e):Lu=0,kc(0,!1)}}function ic(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Do(t)))}function sc(e){return rc(),oc(),ac(),lc()}function lc(){if(5!==Cu)return!1;var e=_u,t=Ou;Ou=0;var n=Oe(Ru),r=A.T,o=D.p;try{D.p=32>n?32:n,A.T=null,n=Nu,Nu=null;var a=_u,s=Ru;if(Cu=0,Tu=_u=null,Ru=0,0!==(6&nu))throw Error(i(331));var l=nu;if(nu|=4,Gl(a.current),Hl(a,a.current,s,n),nu=l,kc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,a)}catch(u){}return!0}finally{D.p=o,A.T=r,ic(e,t)}}function uc(e,t,n){t=Er(n,t),null!==(e=ia(e,t=ks(e.stateNode,t,2),2))&&(Ce(e,2),Sc(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Eu||!Eu.has(r))){e=Er(n,e),null!==(r=ia(t,n=Es(2),2))&&(Cs(n,r,t,e),Ce(r,2),Sc(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(cu=!0,o.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(au&n)===n&&(4===fu||3===fu&&(62914560&au)===au&&300>te()-xu?0===(2&nu)&&Hu(e,0):mu|=n,yu===au&&(yu=0)),Sc(e)}function pc(e,t){0===t&&(t=ke()),null!==(e=Pr(e,t))&&(Ce(e,t),Sc(e))}function hc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pc(e,n)}function mc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),pc(e,n)}var gc=null,yc=null,vc=!1,bc=!1,wc=!1,xc=0;function Sc(e){e!==yc&&null===e.next&&(null===yc?gc=yc=e:yc=yc.next=e),bc=!0,vc||(vc=!0,dd(function(){0!==(6&nu)?G(re,Ec):Cc()}))}function kc(e,t){if(!wc&&bc){wc=!0;do{for(var n=!1,r=gc;null!==r;){if(!t)if(0!==e){var o=r.pendingLanes;if(0===o)var a=0;else{var i=r.suspendedLanes,s=r.pingedLanes;a=(1<<31-pe(42|e)+1)-1,a=201326741&(a&=o&~(i&~s))?201326741&a|1:a?2|a:0}0!==a&&(n=!0,Rc(r,a))}else a=au,0===(3&(a=be(r,r===ru?a:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,a)||(n=!0,Rc(r,a));r=r.next}}while(n);wc=!1}}function Ec(){Cc()}function Cc(){bc=vc=!1;var e=0;0!==xc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==sd&&(sd=e,!0);return sd=null,!1}()&&(e=xc),xc=0);for(var t=te(),n=null,r=gc;null!==r;){var o=r.next,a=_c(r,t);0===a?(r.next=null,null===n?gc=o:n.next=o,null===o&&(yc=n)):(n=r,(0!==e||0!==(3&a))&&(bc=!0)),r=o}kc(e,!1)}function _c(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var i=31-pe(a),s=1<<i,l=o[i];-1===l?0!==(s&n)&&0===(s&r)||(o[i]=xe(s,t)):l<=t&&(e.expiredLanes|=s),a&=~s}if(n=au,n=be(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===iu||9===iu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&X(r),Oe(n)){case 2:case 8:n=oe;break;case 32:default:n=ae;break;case 268435456:n=se}return r=Tc.bind(null,e),n=G(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function Tc(e,t){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(sc()&&e.callbackNode!==n)return null;var r=au;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Fu(e,r,t),_c(e,te()),null!=e.callbackNode&&e.callbackNode===n?Tc.bind(null,e):null)}function Rc(e,t){if(sc())return null;Fu(e,t,!0)}function Oc(){return 0===xc&&(xc=Se()),xc}function Nc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Ot(""+e)}function Pc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Lc=0;Lc<xr.length;Lc++){var jc=xr[Lc];Sr(jc.toLowerCase(),"on"+(jc[0].toUpperCase()+jc.slice(1)))}Sr(pr,"onAnimationEnd"),Sr(hr,"onAnimationIteration"),Sr(mr,"onAnimationStart"),Sr("dblclick","onDoubleClick"),Sr("focusin","onFocus"),Sr("focusout","onBlur"),Sr(gr,"onTransitionRun"),Sr(yr,"onTransitionStart"),Sr(vr,"onTransitionCancel"),Sr(br,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ac="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ac));function zc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==a&&o.isPropagationStopped())break e;a=s,o.currentTarget=u;try{a(o)}catch(c){ys(c)}o.currentTarget=null,a=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,u=s.currentTarget,s=s.listener,l!==a&&o.isPropagationStopped())break e;a=s,o.currentTarget=u;try{a(o)}catch(c){ys(c)}o.currentTarget=null,a=l}}}}function Fc(e,t){var n=t[De];void 0===n&&(n=t[De]=new Set);var r=e+"__bubble";n.has(r)||(Uc(t,e,2,!1),n.add(r))}function $c(e,t,n){var r=0;t&&(r|=4),Uc(n,e,r,t)}var Ic="_reactListening"+Math.random().toString(36).slice(2);function Mc(e){if(!e[Ic]){e[Ic]=!0,qe.forEach(function(t){"selectionchange"!==t&&(Dc.has(t)||$c(t,!1,e),$c(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ic]||(t[Ic]=!0,$c("selectionchange",!1,t))}}function Uc(e,t,n,r){switch(cf(t)){case 2:var o=rf;break;case 8:o=of;break;default:o=af}n=o.bind(null,t,n,e),o=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Bc(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===o)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&i.stateNode.containerInfo===o)return;i=i.return}for(;null!==s;){if(null===(i=Ue(s)))return;if(5===(u=i.tag)||6===u||26===u||27===u){r=a=i;continue e}s=s.parentNode}}r=r.return}zt(function(){var r=a,o=Pt(n),i=[];e:{var s=wr.get(e);if(void 0!==s){var u=Zt,c=e;switch(e){case"keypress":if(0===Wt(n))break e;case"keydown":case"keyup":u=mn;break;case"focusin":c="focus",u=an;break;case"focusout":c="blur",u=an;break;case"beforeblur":case"afterblur":u=an;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=on;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=yn;break;case pr:case hr:case mr:u=sn;break;case br:u=vn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=bn;break;case"copy":case"cut":case"paste":u=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=gn;break;case"toggle":case"beforetoggle":u=wn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==s?s+"Capture":null:s;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Ft(m,p))&&d.push(Hc(m,g,h)),f)break;m=m.return}0<d.length&&(s=new u(s,c,null,n,o),i.push({event:s,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Nt||!(c=n.relatedTarget||n.fromElement)||!Ue(c)&&!c[Ae])&&(u||s)&&(s=o.window===o?o:(s=o.ownerDocument)?s.defaultView||s.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Ue(c):null)&&(f=l(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==u?s:He(u),h=null==c?s:He(c),(s=new d(g,m+"leave",u,n,o)).target=f,s.relatedTarget=h,g=null,Ue(o)===r&&((d=new d(p,m+"enter",c,n,o)).target=h,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,m=0,h=d=u;h;h=Wc(h))m++;for(h=0,g=p;g;g=Wc(g))h++;for(;0<m-h;)d=Wc(d),m--;for(;0<h-m;)p=Wc(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=Wc(d),p=Wc(p)}d=null}else d=null;null!==u&&qc(i,s,u,d,!1),null!==c&&null!==f&&qc(i,f,c,d,!0)}if("select"===(u=(s=r?He(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===u&&"file"===s.type)var y=$n;else if(Ln(s))if(In)y=Qn;else{y=qn;var v=Wn}else!(u=s.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&_t(r.elementType)&&(y=$n):y=Kn;switch(y&&(y=y(e,r))?jn(i,y,n,o):(v&&v(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&vt(s,"number",s.value)),v=r?He(r):window,e){case"focusin":(Ln(v)||"true"===v.contentEditable)&&(rr=v,or=r,ar=null);break;case"focusout":ar=or=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,sr(i,n,o);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":sr(i,n,o)}var b;if(Sn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Nn?Rn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(Cn&&"ko"!==n.locale&&(Nn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Nn&&(b=Vt()):(Bt="value"in(Ut=o)?Ut.value:Ut.textContent,Nn=!0)),0<(v=Vc(r,w)).length&&(w=new un(w,e,null,n,o),i.push({event:w,listeners:v}),b?w.data=b:null!==(b=On(n))&&(w.data=b))),(b=En?function(e,t){switch(e){case"compositionend":return On(t);case"keypress":return 32!==t.which?null:(Tn=!0,_n);case"textInput":return(e=t.data)===_n&&Tn?null:e;default:return null}}(e,n):function(e,t){if(Nn)return"compositionend"===e||!Sn&&Rn(e,t)?(e=Vt(),Ht=Bt=Ut=null,Nn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Vc(r,"onBeforeInput")).length&&(v=new un("onBeforeInput","beforeinput",null,n,o),i.push({event:v,listeners:w}),v.data=b)),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var a=Nc((o[je]||null).action),i=r.submitter;i&&null!==(t=(t=i[je]||null)?Nc(t.formAction):i.getAttribute("formAction"))&&(a=t,i=null);var s=new Zt("action","action",null,r,o);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==xc){var e=i?Pc(o,i):new FormData(o);Pi(n,{pending:!0,data:e,method:o.method,action:a},null,e)}}else"function"===typeof a&&(s.preventDefault(),e=i?Pc(o,i):new FormData(o),Pi(n,{pending:!0,data:e,method:o.method,action:a},a,e))},currentTarget:o}]})}}(i,e,r,n,o)}zc(i,t)})}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vc(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;if(5!==(o=o.tag)&&26!==o&&27!==o||null===a||(null!=(o=Ft(e,n))&&r.unshift(Hc(e,o,a)),null!=(o=Ft(e,t))&&r.push(Hc(e,o,a))),3===e.tag)return r;e=e.return}return[]}function Wc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qc(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(s=s.tag,null!==l&&l===r)break;5!==s&&26!==s&&27!==s||null===u||(l=u,o?null!=(u=Ft(n,a))&&i.unshift(Hc(n,u,l)):o||null!=(u=Ft(n,a))&&i.push(Hc(n,u,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Kc=/\r\n?/g,Qc=/\u0000|\uFFFD/g;function Yc(e){return("string"===typeof e?e:""+e).replace(Kc,"\n").replace(Qc,"")}function Jc(e,t){return t=Yc(t),Yc(e)===t}function Gc(){}function Xc(e,t,n,r,o,a){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||St(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&St(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Ct(e,r,a);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Ot(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===n?("input"!==t&&Xc(e,t,"name",o.name,o,null),Xc(e,t,"formEncType",o.formEncType,o,null),Xc(e,t,"formMethod",o.formMethod,o,null),Xc(e,t,"formTarget",o.formTarget,o,null)):(Xc(e,t,"encType",o.encType,o,null),Xc(e,t,"method",o.method,o,null),Xc(e,t,"target",o.target,o,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Ot(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Gc);break;case"onScroll":null!=r&&Fc("scroll",e);break;case"onScrollEnd":null!=r&&Fc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Ot(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Fc("beforetoggle",e),Fc("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Tt.get(n)||n,r)}}function Zc(e,t,n,r,o,a){switch(n){case"style":Ct(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?St(e,r):("number"===typeof r||"bigint"===typeof r)&&St(e,""+r);break;case"onScroll":null!=r&&Fc("scroll",e);break;case"onScrollEnd":null!=r&&Fc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Gc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),"function"===typeof(a=null!=(a=e[je]||null)?a[n]:null)&&e.removeEventListener(t,a,o),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,o)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Fc("error",e),Fc("load",e);var r,o=!1,a=!1;for(r in n)if(n.hasOwnProperty(r)){var s=n[r];if(null!=s)switch(r){case"src":o=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Xc(e,t,r,s,n,null)}}return a&&Xc(e,t,"srcSet",n.srcSet,n,null),void(o&&Xc(e,t,"src",n.src,n,null));case"input":Fc("invalid",e);var l=r=s=a=null,u=null,c=null;for(o in n)if(n.hasOwnProperty(o)){var d=n[o];if(null!=d)switch(o){case"name":a=d;break;case"type":s=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Xc(e,t,o,d,n,null)}}return yt(e,r,l,u,c,s,a,!1),void dt(e);case"select":for(a in Fc("invalid",e),o=s=r=null,n)if(n.hasOwnProperty(a)&&null!=(l=n[a]))switch(a){case"value":r=l;break;case"defaultValue":s=l;break;case"multiple":o=l;default:Xc(e,t,a,l,n,null)}return t=r,n=s,e.multiple=!!o,void(null!=t?bt(e,!!o,t,!1):null!=n&&bt(e,!!o,n,!0));case"textarea":for(s in Fc("invalid",e),r=a=o=null,n)if(n.hasOwnProperty(s)&&null!=(l=n[s]))switch(s){case"value":o=l;break;case"defaultValue":a=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(i(91));break;default:Xc(e,t,s,l,n,null)}return xt(e,o,a,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(o=n[u]))if("selected"===u)e.selected=o&&"function"!==typeof o&&"symbol"!==typeof o;else Xc(e,t,u,o,n,null);return;case"dialog":Fc("beforetoggle",e),Fc("toggle",e),Fc("cancel",e),Fc("close",e);break;case"iframe":case"object":Fc("load",e);break;case"video":case"audio":for(o=0;o<Ac.length;o++)Fc(Ac[o],e);break;case"image":Fc("error",e),Fc("load",e);break;case"details":Fc("toggle",e);break;case"embed":case"source":case"link":Fc("error",e),Fc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(o=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Xc(e,t,c,o,n,null)}return;default:if(_t(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(o=n[d])&&Zc(e,t,d,o,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(o=n[l])&&Xc(e,t,l,o,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function od(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ad(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var sd=null;var ld="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:ld;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function hd(e,t){var n=t,r=0,o=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&xd(i.documentElement),2&n&&xd(i.body),4&n)for(xd(n=i.head),i=n.firstChild;i;){var s=i.nextSibling,l=i.nodeName;i[Ie]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=s}}if(0===o)return e.removeChild(a),void Rf(t);o--}else"$"===n||"$?"===n||"$!"===n?o++:r=n.charCodeAt(0)-48;else r=0;n=a}while(n);Rf(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Me(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function xd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Me(e)}var Sd=new Map,kd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=D.d;D.d={f:function(){var e=Cd.f(),t=Uu();return e||t},r:function(e){var t=Be(e);null!==t&&5===t.tag&&"form"===t.type?ji(t):Cd.r(e)},D:function(e){Cd.D(e),Td("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Td("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=_d;if(r&&e&&t){var o='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(o+='[imagesizes="'+mt(n.imageSizes)+'"]')):o+='[href="'+mt(e)+'"]';var a=o;switch(t){case"style":a=Od(e);break;case"script":a=Ld(e)}Sd.has(a)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Sd.set(a,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(Nd(a))||"script"===t&&r.querySelector(jd(a))||(ed(t=r.createElement("link"),"link",e),We(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=_d;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',a=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=Ld(e)}if(!Sd.has(a)&&(e=f({rel:"modulepreload",href:e},t),Sd.set(a,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(jd(a)))return}ed(r=n.createElement("link"),"link",e),We(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=_d;if(n&&e){var r=Ve(n).hoistableScripts,o=Ld(e),a=r.get(o);a||((a=n.querySelector(jd(o)))||(e=f({src:e,async:!0},t),(t=Sd.get(o))&&Fd(e,t),We(a=n.createElement("script")),ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}},S:function(e,t,n){Cd.S(e,t,n);var r=_d;if(r&&e){var o=Ve(r).hoistableStyles,a=Od(e);t=t||"default";var i=o.get(a);if(!i){var s={loading:0,preload:null};if(i=r.querySelector(Nd(a)))s.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Sd.get(a))&&zd(e,n);var l=i=r.createElement("link");We(l),ed(l,"link",e),l._p=new Promise(function(e,t){l.onload=e,l.onerror=t}),l.addEventListener("load",function(){s.loading|=1}),l.addEventListener("error",function(){s.loading|=2}),s.loading|=4,Dd(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:s},o.set(a,i)}}},M:function(e,t){Cd.M(e,t);var n=_d;if(n&&e){var r=Ve(n).hoistableScripts,o=Ld(e),a=r.get(o);a||((a=n.querySelector(jd(o)))||(e=f({src:e,async:!0,type:"module"},t),(t=Sd.get(o))&&Fd(e,t),We(a=n.createElement("script")),ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}}};var _d="undefined"===typeof document?null:document;function Td(e,t,n){var r=_d;if(r&&"string"===typeof t&&t){var o=mt(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"===typeof n&&(o+='[crossorigin="'+n+'"]'),kd.has(o)||(kd.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(ed(t=r.createElement("link"),"link",e),We(t),r.head.appendChild(t)))}}function Rd(e,t,n,r){var o,a,s,l,u=(u=V.current)?Ed(u):null;if(!u)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Od(n.href),(r=(n=Ve(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Od(n.href);var c=Ve(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Nd(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Sd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Sd.set(e,n),c||(o=u,a=e,s=n,l=d.state,o.querySelector('link[rel="preload"][as="style"]['+a+"]")?l.loading=1:(a=o.createElement("link"),l.preload=a,a.addEventListener("load",function(){return l.loading|=1}),a.addEventListener("error",function(){return l.loading|=2}),ed(a,"link",s),We(a),o.head.appendChild(a))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Ld(n),(r=(n=Ve(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Od(e){return'href="'+mt(e)+'"'}function Nd(e){return'link[rel="stylesheet"]['+e+"]"}function Pd(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Ld(e){return'[src="'+mt(e)+'"]'}function jd(e){return"script[async]"+e}function Ad(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,We(r),r;var o=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return We(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",o),Dd(r,n.precedence,e),t.instance=r;case"stylesheet":o=Od(n.href);var a=e.querySelector(Nd(o));if(a)return t.state.loading|=4,t.instance=a,We(a),a;r=Pd(n),(o=Sd.get(o))&&zd(r,o),We(a=(e.ownerDocument||e).createElement("link"));var s=a;return s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(a,"link",r),t.state.loading|=4,Dd(a,n.precedence,e),t.instance=a;case"script":return a=Ld(n.src),(o=e.querySelector(jd(a)))?(t.instance=o,We(o),o):(r=n,(o=Sd.get(a))&&Fd(r=f({},n),o),We(o=(e=e.ownerDocument||e).createElement("script")),ed(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Dd(r,n.precedence,e));return t.instance}function Dd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,a=o,i=0;i<r.length;i++){var s=r[i];if(s.dataset.precedence===t)a=s;else if(a!==o)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function zd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var $d=null;function Id(e,t,n){if(null===$d){var r=new Map,o=$d=new Map;o.set(n,r)}else(r=(o=$d).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var a=n[o];if(!(a[Ie]||a[Le]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var i=a.getAttribute(t)||"";i=e+i;var s=r.get(i);s?s.push(a):r.set(i,[a])}}return r}function Md(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Ud(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Bd=null;function Hd(){}function Vd(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Wd=null;function qd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Wd=new Map,t.forEach(Kd,e),Wd=null,Vd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=Wd.get(e);if(n)var r=n.get(null);else{n=new Map,Wd.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<o.length;a++){var i=o[a];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(o=t.instance).getAttribute("data-precedence"),(a=n.get(i)||r)===r&&n.set(null,o),n.set(i,o),this.count++,r=Vd.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),a?a.parentNode.insertBefore(o,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var Qd={$$typeof:x,Provider:null,Consumer:null,_currentValue:z,_currentValue2:z,_threadCount:0};function Yd(e,t,n,r,o,a,i,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=a,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function Jd(e,t,n,r,o,a,i,s,l,u,c,d){return e=new Yd(e,t,n,i,s,l,u,d),t=1,!0===a&&(t|=24),a=zr(3,null,null,t),e.current=a,a.stateNode=e,(t=Ao()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},ra(a),e}function Gd(e){return e?e=Ar:Ar}function Xd(e,t,n,r,o,a){o=Gd(o),null===r.context?r.context=o:r.pendingContext=o,(r=aa(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=ia(e,r,t))&&(zu(n,0,t),sa(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=Pr(e,67108864);null!==t&&zu(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var o=A.T;A.T=null;var a=D.p;try{D.p=2,af(e,t,n,r)}finally{D.p=a,A.T=o}}function of(e,t,n,r){var o=A.T;A.T=null;var a=D.p;try{D.p=8,af(e,t,n,r)}finally{D.p=a,A.T=o}}function af(e,t,n,r){if(nf){var o=sf(r);if(null===o)Bc(e,t,r,lf,n),bf(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return ff=wf(ff,e,t,n,r,o),!0;case"dragenter":return pf=wf(pf,e,t,n,r,o),!0;case"mouseover":return hf=wf(hf,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return mf.set(a,wf(mf.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,gf.set(a,wf(gf.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==o;){var a=Be(o);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var i=ve(a.pendingLanes);if(0!==i){var s=a;for(s.pendingLanes|=2,s.entangledLanes|=2;i;){var l=1<<31-pe(i);s.entanglements[1]|=l,i&=~l}Sc(a),0===(6&nu)&&(Su=te()+500,kc(0,!1))}}break;case 13:null!==(s=Pr(a,2))&&zu(s,0,2),Uu(),ef(a,2)}if(null===(a=sf(r))&&Bc(e,t,r,lf,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Bc(e,t,r,null,n)}}function sf(e){return uf(e=Pt(e))}var lf=null;function uf(e){if(lf=null,null!==(e=Ue(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case oe:return 8;case ae:case ie:return 32;case se:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,hf=null,mf=new Map,gf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":hf=null;break;case"pointerover":case"pointerout":mf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function wf(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=Be(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function xf(e){var t=Ue(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=D.p;try{return D.p=e,t()}finally{D.p=n}}(e.priority,function(){if(13===n.tag){var e=Au();e=Re(e);var t=Pr(n,e);null!==t&&zu(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Sf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=sf(e.nativeEvent);if(null!==n)return null!==(t=Be(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Nt=r,n.target.dispatchEvent(r),Nt=null,t.shift()}return!0}function kf(e,t,n){Sf(e)&&n.delete(t)}function Ef(){df=!1,null!==ff&&Sf(ff)&&(ff=null),null!==pf&&Sf(pf)&&(pf=null),null!==hf&&Sf(hf)&&(hf=null),mf.forEach(kf),gf.forEach(kf)}function Cf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var _f=null;function Tf(e){_f!==e&&(_f=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){_f===e&&(_f=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],o=e[t+2];if("function"!==typeof r){if(null===uf(r||n))continue;break}var a=Be(n);null!==a&&(e.splice(t,3),t-=3,Pi(a,{pending:!0,data:o,method:n.method,action:r},r,o))}}))}function Rf(e){function t(t){return Cf(t,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==hf&&Cf(hf,e),mf.forEach(t),gf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)xf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],a=n[r+1],i=o[je]||null;if("function"===typeof a)i||Tf(n);else if(i){var s=null;if(a&&a.hasAttribute("formAction")){if(o=a,i=a[je]||null)s=i.formAction;else if(null!==uf(o))continue}else s=i.action;"function"===typeof s?n[r+1]=s:(n.splice(r,3),r-=3),Tf(n)}}}function Of(e){this._internalRoot=e}function Nf(e){this._internalRoot=e}Nf.prototype.render=Of.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Xd(t.current,Au(),e,t,null,null)},Nf.prototype.unmount=Of.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Xd(e.current,2,null,e,null,null),Uu(),t[Ae]=null}},Nf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ne();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&xf(e)}};var Pf=o.version;if("19.1.0"!==Pf)throw Error(i(527,Pf,"19.1.0"));D.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return c(o),e;if(a===r)return c(o),t;a=a.sibling}throw Error(i(188))}if(n.return!==r.return)n=o,r=a;else{for(var s=!1,u=o.child;u;){if(u===n){s=!0,n=o,r=a;break}if(u===r){s=!0,r=o,n=a;break}u=u.sibling}if(!s){for(u=a.child;u;){if(u===n){s=!0,n=a,r=o;break}if(u===r){s=!0,r=a,n=o;break}u=u.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Lf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var jf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!jf.isDisabled&&jf.supportsFiber)try{ce=jf.inject(Lf),de=jf}catch(Df){}}t.createRoot=function(e,t){if(!s(e))throw Error(i(299));var n=!1,r="",o=vs,a=bs,l=ws;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(o=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Jd(e,1,!1,null,0,n,r,o,a,l,0,null),e[Ae]=t.current,Mc(e),new Of(t)},t.hydrateRoot=function(e,t,n){if(!s(e))throw Error(i(299));var r=!1,o="",a=vs,l=bs,u=ws,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Jd(e,1,!0,t,0,r,o,a,l,u,0,c)).context=Gd(null),n=t.current,(o=aa(r=Re(r=Au()))).callback=null,ia(n,o,r),n=r,t.current.lanes=n,Ce(t,n),Sc(t),e[Ae]=t.current,Mc(e),new Nf(t)},t.version="19.1.0"},43:(e,t,n)=>{"use strict";e.exports=n(288)},233:(e,t,n)=>{"use strict";e.exports=n(382)},288:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var x=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},k=Object.prototype.hasOwnProperty;function E(e,t,r,o,a,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function T(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function R(){}function O(e,t,o,a,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,u,c=!1;if(null===e)c=!0;else switch(s){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return O((c=e._init)(e._payload),t,o,a,i)}}if(c)return i=i(e),c=""===a?"."+T(e,0):a,x(i)?(o="",null!=c&&(o=c.replace(_,"$&/")+"/"),O(i,t,o,"",function(e){return e})):null!=i&&(C(i)&&(l=i,u=o+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(_,"$&/")+"/")+c,i=E(l.type,u,void 0,0,0,l.props)),t.push(i)),1;c=0;var d,h=""===a?".":a+":";if(x(e))for(var m=0;m<e.length;m++)c+=O(a=e[m],t,o,s=h+T(a,m),i);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(a=e.next()).done;)c+=O(a=a.value,t,o,s=h+T(a,m++),i);else if("object"===s){if("function"===typeof e.then)return O(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(R,R):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,o,a,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function N(e,t,n){if(null==e)return e;var r=[],o=0;return O(e,r,"","",function(e){return t.call(n,e,o++)}),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function j(){}t.Children={map:N,forEach:function(e,t,n){N(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return N(e,function(){t++}),t},toArray:function(e){return N(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!k.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var i=Array(a),s=0;s<a;s++)i[s]=arguments[s+2];r.children=i}return E(e.type,o,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)k.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var i=arguments.length-2;if(1===i)o.children=n;else if(1<i){for(var s=Array(i),l=0;l<i;l++)s[l]=arguments[l+2];o.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===o[r]&&(o[r]=i[r]);return E(e,a,void 0,0,0,o)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:P}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),o=S.S;null!==o&&o(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(j,L)}catch(a){L(a)}finally{S.T=t}},t.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},t.use=function(e){return S.H.use(e)},t.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},t.useCallback=function(e,t){return S.H.useCallback(e,t)},t.useContext=function(e){return S.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=S.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return S.H.useId()},t.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return S.H.useMemo(e,t)},t.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},t.useRef=function(e){return S.H.useRef(e)},t.useState=function(e){return S.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return S.H.useTransition()},t.version="19.1.0"},340:(e,t)=>{"use strict";const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,s=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function u(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},382:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var x=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},k=Object.prototype.hasOwnProperty;function E(e,t,r,o,a,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function T(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function R(){}function O(e,t,o,a,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,u,c=!1;if(null===e)c=!0;else switch(s){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return O((c=e._init)(e._payload),t,o,a,i)}}if(c)return i=i(e),c=""===a?"."+T(e,0):a,x(i)?(o="",null!=c&&(o=c.replace(_,"$&/")+"/"),O(i,t,o,"",function(e){return e})):null!=i&&(C(i)&&(l=i,u=o+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(_,"$&/")+"/")+c,i=E(l.type,u,void 0,0,0,l.props)),t.push(i)),1;c=0;var d,h=""===a?".":a+":";if(x(e))for(var m=0;m<e.length;m++)c+=O(a=e[m],t,o,s=h+T(a,m),i);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(a=e.next()).done;)c+=O(a=a.value,t,o,s=h+T(a,m++),i);else if("object"===s){if("function"===typeof e.then)return O(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(R,R):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,o,a,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function N(e,t,n){if(null==e)return e;var r=[],o=0;return O(e,r,"","",function(e){return t.call(n,e,o++)}),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function j(){}t.Children={map:N,forEach:function(e,t,n){N(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return N(e,function(){t++}),t},toArray:function(e){return N(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!k.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var i=Array(a),s=0;s<a;s++)i[s]=arguments[s+2];r.children=i}return E(e.type,o,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)k.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var i=arguments.length-2;if(1===i)o.children=n;else if(1<i){for(var s=Array(i),l=0;l<i;l++)s[l]=arguments[l+2];o.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===o[r]&&(o[r]=i[r]);return E(e,a,void 0,0,0,o)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:P}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),o=S.S;null!==o&&o(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(j,L)}catch(a){L(a)}finally{S.T=t}},t.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},t.use=function(e){return S.H.use(e)},t.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},t.useCallback=function(e,t){return S.H.useCallback(e,t)},t.useContext=function(e){return S.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=S.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return S.H.useId()},t.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return S.H.useMemo(e,t)},t.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},t.useRef=function(e){return S.H.useRef(e)},t.useState=function(e){return S.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return S.H.useTransition()},t.version="19.1.0"},391:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},514:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},570:e=>{e.exports=function(e,t,n,r){var o=n?n.call(r,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;var a=Object.keys(e),i=Object.keys(t);if(a.length!==i.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),l=0;l<a.length;l++){var u=a[l];if(!s(u))return!1;var c=e[u],d=t[u];if(!1===(o=n?n.call(r,c,d,u):void 0)||void 0===o&&c!==d)return!1}return!0}},579:(e,t,n)=>{"use strict";e.exports=n(799)},672:(e,t,n)=>{"use strict";var r=n(43);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var i={d:{f:a,r:function(){throw Error(o(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},s=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(o(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:s,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=i.p;try{if(l.T=null,i.p=2,e)return e()}finally{l.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),o="string"===typeof t.integrity?t.integrity:void 0,a="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:o,fetchPriority:a}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:o,fetchPriority:a,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(e,t,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==t.key&&(o=""+t.key),"key"in t)for(var a in r={},t)"key"!==a&&(r[a]=t[a]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:o,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=o,t.jsxs=o},853:(e,t,n)=>{"use strict";e.exports=n(896)},896:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>a(l,n))u<o&&0>a(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<o&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)o(c);else{if(!(t.startTime<=e))break;o(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function S(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,E||(E=!0,k());else{var t=r(c);null!==t&&L(S,t.startTime-e)}}var k,E=!1,C=-1,_=5,T=-1;function R(){return!!y||!(t.unstable_now()-T<_)}function O(){if(y=!1,E){var e=t.unstable_now();T=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(C),C=-1),h=!0;var a=p;try{t:{for(x(e),f=r(u);null!==f&&!(f.expirationTime>e&&R());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var s=i(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof s){f.callback=s,x(e),n=!0;break t}f===r(u)&&o(u),x(e)}else o(u);f=r(u)}if(null!==f)n=!0;else{var l=r(c);null!==l&&L(S,l.startTime-e),n=!1}}break e}finally{f=null,p=a,h=!1}n=void 0}}finally{n?k():E=!1}}}if("function"===typeof w)k=function(){w(O)};else if("undefined"!==typeof MessageChannel){var N=new MessageChannel,P=N.port2;N.port1.onmessage=O,k=function(){P.postMessage(null)}}else k=function(){v(O,0)};function L(e,n){C=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>i?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(g?(b(C),C=-1):g=!0,L(S,a-i))):(e.sortIndex=s,n(u,e),m||h||(m=!0,E||(E=!0,k()))),e},t.unstable_shouldYield=R,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".a3c1d153.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="client:";n.l=(r,o,a,i)=>{if(e[r])e[r].push(o);else{var s,l;if(void 0!==a)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+a),s.src=r),e[r]=[o];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),o&&o.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var a=new Promise((n,r)=>o=e[t]=[n,r]);r.push(o[2]=a);var i=n.p+n.u(t),s=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var a=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+a+": "+i+")",s.name="ChunkLoadError",s.type=a,s.request=i,o[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var o,a,i=r[0],s=r[1],l=r[2],u=0;if(i.some(t=>0!==e[t])){for(o in s)n.o(s,o)&&(n.m[o]=s[o]);if(l)l(n)}for(t&&t(r);u<i.length;u++)a=i[u],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self.webpackChunkclient=self.webpackChunkclient||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0,(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>yn,hasStandardBrowserEnv:()=>bn,hasStandardBrowserWebWorkerEnv:()=>wn,navigator:()=>vn,origin:()=>xn});var t={};n.r(t),n.d(t,{Decoder:()=>Md,Encoder:()=>$d,PacketType:()=>Fd,protocol:()=>zd});var r=n(43),o=n(391),a=n(233),i=(n(340),"popstate");function s(){return h(function(e,t){let{pathname:n,search:r,hash:o}=e.location;return d("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:f(t)},null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function l(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function u(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function c(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return{pathname:"string"===typeof e?e:e.pathname,search:"",hash:"",..."string"===typeof t?p(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function f(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function p(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function h(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:o=document.defaultView,v5Compat:a=!1}=r,s=o.history,l="POP",u=null,f=p();function p(){return(s.state||{idx:null}).idx}function h(){l="POP";let e=p(),t=null==e?null:e-f;f=e,u&&u({action:l,location:y.location,delta:t})}function g(e){return m(e)}null==f&&(f=0,s.replaceState({...s.state,idx:f},""));let y={get action(){return l},get location(){return e(o,s)},listen(e){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(i,h),u=e,()=>{o.removeEventListener(i,h),u=null}},createHref:e=>t(o,e),createURL:g,encodeLocation(e){let t=g(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l="PUSH";let r=d(y.location,e,t);n&&n(r,e),f=p()+1;let i=c(r,f),h=y.createHref(r);try{s.pushState(i,"",h)}catch(m){if(m instanceof DOMException&&"DataCloneError"===m.name)throw m;o.location.assign(h)}a&&u&&u({action:l,location:y.location,delta:1})},replace:function(e,t){l="REPLACE";let r=d(y.location,e,t);n&&n(r,e),f=p();let o=c(r,f),i=y.createHref(r);s.replaceState(o,"",i),a&&u&&u({action:l,location:y.location,delta:0})},go:e=>s.go(e)};return y}function m(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),l(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:f(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function g(e,t){return y(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function y(e,t,n,r){let o=L(("string"===typeof t?p(t):t).pathname||"/",n);if(null==o)return null;let a=v(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(a);let i=null;for(let s=0;null==i&&s<a.length;++s){let e=P(o);i=R(a[s],e,r)}return i}function v(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(l(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let s=F([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(l(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),v(e.children,t,u,s)),(null!=e.path||e.index)&&t.push({path:s,score:T(s,e.index),routesMeta:u})};return e.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of b(e.path))o(e,t,n);else o(e,t)}),t}function b(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=b(r.join("/")),s=[];return s.push(...i.map(e=>""===e?a:[a,e].join("/"))),o&&s.push(...i),s.map(t=>e.startsWith("/")&&""===t?"/":t)}var w=/^:[\w-]+$/,x=3,S=2,k=1,E=10,C=-2,_=e=>"*"===e;function T(e,t){let n=e.split("/"),r=n.length;return n.some(_)&&(r+=C),t&&(r+=S),n.filter(e=>!_(e)).reduce((e,t)=>e+(w.test(t)?x:""===t?k:E),r)}function R(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,o={},a="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===a?t:t.slice(a.length)||"/",c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(o,c.params),i.push({params:o,pathname:F([a,c.pathname]),pathnameBase:$(F([a,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(a=F([a,c.pathnameBase]))}return i}function O(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=N(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),s=o.slice(1),l=r.reduce((e,t,n)=>{let{paramName:r,isOptional:o}=t;if("*"===r){let e=s[n]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=o&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{});return{params:l,pathname:a,pathnameBase:i,pattern:e}}function N(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];u("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function P(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return u(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function j(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function A(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function D(e){let t=A(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function z(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=p(e):(r={...e},l(!r.pathname||!r.pathname.includes("?"),j("?","pathname","search",r)),l(!r.pathname||!r.pathname.includes("#"),j("#","pathname","hash",r)),l(!r.search||!r.search.includes("#"),j("#","search","hash",r)));let a,i=""===e||""===r.pathname,s=i?"/":r.pathname;if(null==s)a=n;else{let e=t.length-1;if(!o&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}a=e>=0?t[e]:"/"}let u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:o=""}="string"===typeof e?p(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:I(r),hash:M(o)}}(r,a),c=s&&"/"!==s&&s.endsWith("/"),d=(i||"."===s)&&n.endsWith("/");return u.pathname.endsWith("/")||!c&&!d||(u.pathname+="/"),u}var F=e=>e.join("/").replace(/\/\/+/g,"/"),$=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",M=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function U(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var B=["POST","PUT","PATCH","DELETE"],H=(new Set(B),["GET",...B]);new Set(H),Symbol("ResetLoaderData");var V=a.createContext(null);V.displayName="DataRouter";var W=a.createContext(null);W.displayName="DataRouterState";var q=a.createContext({isTransitioning:!1});q.displayName="ViewTransition";var K=a.createContext(new Map);K.displayName="Fetchers";var Q=a.createContext(null);Q.displayName="Await";var Y=a.createContext(null);Y.displayName="Navigation";var J=a.createContext(null);J.displayName="Location";var G=a.createContext({outlet:null,matches:[],isDataRoute:!1});G.displayName="Route";var X=a.createContext(null);X.displayName="RouteError";function Z(){return null!=a.useContext(J)}function ee(){return l(Z(),"useLocation() may be used only in the context of a <Router> component."),a.useContext(J).location}var te="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ne(e){a.useContext(Y).static||a.useLayoutEffect(e)}function re(){let{isDataRoute:e}=a.useContext(G);return e?function(){let{router:e}=pe("useNavigate"),t=me("useNavigate"),n=a.useRef(!1);ne(()=>{n.current=!0});let r=a.useCallback(async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};u(n.current,te),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...o}))},[e,t]);return r}():function(){l(Z(),"useNavigate() may be used only in the context of a <Router> component.");let e=a.useContext(V),{basename:t,navigator:n}=a.useContext(Y),{matches:r}=a.useContext(G),{pathname:o}=ee(),i=JSON.stringify(D(r)),s=a.useRef(!1);ne(()=>{s.current=!0});let c=a.useCallback(function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(u(s.current,te),!s.current)return;if("number"===typeof r)return void n.go(r);let l=z(r,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:F([t,l.pathname])),(a.replace?n.replace:n.push)(l,a.state,a)},[t,n,i,o,e]);return c}()}a.createContext(null);function oe(){let{matches:e}=a.useContext(G),t=e[e.length-1];return t?t.params:{}}function ae(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=a.useContext(G),{pathname:r}=ee(),o=JSON.stringify(D(n));return a.useMemo(()=>z(e,JSON.parse(o),r,"path"===t),[e,o,r,t])}function ie(e,t,n,r){l(Z(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=a.useContext(Y),{matches:i}=a.useContext(G),s=i[i.length-1],c=s?s.params:{},d=s?s.pathname:"/",f=s?s.pathnameBase:"/",h=s&&s.route;{let e=h&&h.path||"";ve(d,!h||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${d}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let m,y=ee();if(t){let e="string"===typeof t?p(t):t;l("/"===f||e.pathname?.startsWith(f),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${f}" but pathname "${e.pathname}" was given in the \`location\` prop.`),m=e}else m=y;let v=m.pathname||"/",b=v;if("/"!==f){let e=f.replace(/^\//,"").split("/");b="/"+v.replace(/^\//,"").split("/").slice(e.length).join("/")}let w=g(e,{pathname:b});u(h||null!=w,`No routes matched location "${m.pathname}${m.search}${m.hash}" `),u(null==w||void 0!==w[w.length-1].route.element||void 0!==w[w.length-1].route.Component||void 0!==w[w.length-1].route.lazy,`Matched leaf route at location "${m.pathname}${m.search}${m.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let x=de(w&&w.map(e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:F([f,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?f:F([f,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,r);return t&&x?a.createElement(J.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...m},navigationType:"POP"}},x):x}function se(){let e=ge(),t=U(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=a.createElement(a.Fragment,null,a.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),a.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",a.createElement("code",{style:i},"ErrorBoundary")," or"," ",a.createElement("code",{style:i},"errorElement")," prop on your route.")),a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,s)}var le=a.createElement(se,null),ue=class extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(G.Provider,{value:this.props.routeContext},a.createElement(X.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ce(e){let{routeContext:t,match:n,children:r}=e,o=a.useContext(V);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(G.Provider,{value:t},r)}function de(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=n?.errors;if(null!=o){let e=r.findIndex(e=>e.route.id&&void 0!==o?.[e.route.id]);l(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let i=!1,s=-1;if(n)for(let a=0;a<r.length;a++){let e=r[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(s=a),e.route.id){let{loaderData:t,errors:o}=n,a=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!o||void 0===o[e.route.id]);if(e.route.lazy||a){i=!0,r=s>=0?r.slice(0,s+1):[r[0]];break}}}return r.reduceRight((e,l,u)=>{let c,d=!1,f=null,p=null;n&&(c=o&&l.route.id?o[l.route.id]:void 0,f=l.route.errorElement||le,i&&(s<0&&0===u?(ve("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):s===u&&(d=!0,p=l.route.hydrateFallbackElement||null)));let h=t.concat(r.slice(0,u+1)),m=()=>{let t;return t=c?f:d?p:l.route.Component?a.createElement(l.route.Component,null):l.route.element?l.route.element:e,a.createElement(ce,{match:l,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(l.route.ErrorBoundary||l.route.errorElement||0===u)?a.createElement(ue,{location:n.location,revalidation:n.revalidation,component:f,error:c,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()},null)}function fe(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function pe(e){let t=a.useContext(V);return l(t,fe(e)),t}function he(e){let t=a.useContext(W);return l(t,fe(e)),t}function me(e){let t=function(e){let t=a.useContext(G);return l(t,fe(e)),t}(e),n=t.matches[t.matches.length-1];return l(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function ge(){let e=a.useContext(X),t=he("useRouteError"),n=me("useRouteError");return void 0!==e?e:t.errors?.[n]}var ye={};function ve(e,t,n){t||ye[e]||(ye[e]=!0,u(!1,n))}a.memo(function(e){let{routes:t,future:n,state:r}=e;return ie(t,void 0,r,n)});function be(e){l(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function we(e){let{basename:t="/",children:n=null,location:r,navigationType:o="POP",navigator:i,static:s=!1}=e;l(!Z(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let c=t.replace(/^\/*/,"/"),d=a.useMemo(()=>({basename:c,navigator:i,static:s,future:{}}),[c,i,s]);"string"===typeof r&&(r=p(r));let{pathname:f="/",search:h="",hash:m="",state:g=null,key:y="default"}=r,v=a.useMemo(()=>{let e=L(f,c);return null==e?null:{location:{pathname:e,search:h,hash:m,state:g,key:y},navigationType:o}},[c,f,h,m,g,y,o]);return u(null!=v,`<Router basename="${c}"> is not able to match the URL "${f}${h}${m}" because it does not start with the basename, so the <Router> won't render anything.`),null==v?null:a.createElement(Y.Provider,{value:d},a.createElement(J.Provider,{children:n,value:v}))}function xe(e){let{children:t,location:n}=e;return ie(Se(t),n)}a.Component;function Se(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return a.Children.forEach(e,(e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,Se(e.props.children,o));l(e.type===be,`[${"string"===typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),l(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Se(e.props.children,o)),n.push(i)}),n}var ke="get",Ee="application/x-www-form-urlencoded";function Ce(e){return null!=e&&"string"===typeof e.tagName}var _e=null;var Te=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Re(e){return null==e||Te.has(e)?e:(u(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ee}"`),null)}function Oe(e,t){let n,r,o,a,i;if(Ce(s=e)&&"form"===s.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?L(i,t):null,n=e.getAttribute("method")||ke,o=Re(e.getAttribute("enctype"))||Ee,a=new FormData(e)}else if(function(e){return Ce(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Ce(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(r=s?L(s,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||ke,o=Re(e.getAttribute("formenctype"))||Re(i.getAttribute("enctype"))||Ee,a=new FormData(i,e),!function(){if(null===_e)try{new FormData(document.createElement("form"),0),_e=!1}catch(e){_e=!0}return _e}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";a.append(`${e}x`,"0"),a.append(`${e}y`,"0")}else t&&a.append(t,r)}}else{if(Ce(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ke,r=null,o=Ee,i=e}var s;return a&&"text/plain"===o&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:a,body:i}}function Ne(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Pe(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Le(e){return null!=e&&"string"===typeof e.page}function je(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Ae(e,t,n,r,o,a){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,s=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===a?t.filter((e,t)=>i(e,t)||s(e,t)):"data"===a?t.filter((t,a)=>{let l=r.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(i(t,a)||s(t,a))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0}):[]}function De(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o}).flat(1),[...new Set(r)];var r}function ze(e,t){let n=new Set,r=new Set(t);return e.reduce((e,o)=>{if(t&&!Le(o)&&"script"===o.as&&o.href&&r.has(o.href))return e;let a=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(o));return n.has(a)||(n.add(a),e.push({key:a,link:o})),e},[])}function Fe(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var $e=new Set([100,101,204,205]);function Ie(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===L(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}a.Component;function Me(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let r,o=a.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(U(t))return a.createElement(Ue,{title:"Unhandled Thrown Response!"},a.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),o);if(t instanceof Error)0;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);new Error(e)}return a.createElement(Ue,{title:"Application Error!",isOutsideRemixApp:n},a.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),a.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),o)}function Ue(e){let{title:t,renderScripts:n,isOutsideRemixApp:r,children:o}=e,{routeModules:i}=qe();return i.root?.Layout&&!r?o:a.createElement("html",{lang:"en"},a.createElement("head",null,a.createElement("meta",{charSet:"utf-8"}),a.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),a.createElement("title",null,t)),a.createElement("body",null,a.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},o,n?a.createElement(Ze,null):null)))}function Be(e,t){return"lazy"===e.mode&&!0===t}function He(){let e=a.useContext(V);return Ne(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Ve(){let e=a.useContext(W);return Ne(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var We=a.createContext(void 0);function qe(){let e=a.useContext(We);return Ne(e,"You must render this element inside a <HydratedRouter> element"),e}function Ke(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Qe(e,t,n){if(n&&!Xe)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}function Ye(e){let{page:t,...n}=e,{router:r}=He(),o=a.useMemo(()=>g(r.routes,t,r.basename),[r.routes,t,r.basename]);return o?a.createElement(Ge,{page:t,matches:o,...n}):null}function Je(e){let{manifest:t,routeModules:n}=qe(),[r,o]=a.useState([]);return a.useEffect(()=>{let r=!1;return async function(e,t,n){let r=await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await Pe(r,n);return e.links?e.links():[]}return[]}));return ze(r.flat(1).filter(je).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}(e,t,n).then(e=>{r||o(e)}),()=>{r=!0}},[e,t,n]),r}function Ge(e){let{page:t,matches:n,...r}=e,o=ee(),{manifest:i,routeModules:s}=qe(),{basename:l}=He(),{loaderData:u,matches:c}=Ve(),d=a.useMemo(()=>Ae(t,n,c,i,o,"data"),[t,n,c,i,o]),f=a.useMemo(()=>Ae(t,n,c,i,o,"assets"),[t,n,c,i,o]),p=a.useMemo(()=>{if(t===o.pathname+o.search+o.hash)return[];let e=new Set,r=!1;if(n.forEach(t=>{let n=i.routes[t.route.id];n&&n.hasLoader&&(!d.some(e=>e.route.id===t.route.id)&&t.route.id in u&&s[t.route.id]?.shouldRevalidate||n.hasClientLoader?r=!0:e.add(t.route.id))}),0===e.size)return[];let a=Ie(t,l);return r&&e.size>0&&a.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[a.pathname+a.search]},[l,u,o,i,d,n,t,s]),h=a.useMemo(()=>De(f,i),[f,i]),m=Je(f);return a.createElement(a.Fragment,null,p.map(e=>a.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r})),h.map(e=>a.createElement("link",{key:e,rel:"modulepreload",href:e,...r})),m.map(e=>{let{key:t,link:n}=e;return a.createElement("link",{key:t,...n})}))}We.displayName="FrameworkContext";var Xe=!1;function Ze(e){let{manifest:t,serverHandoffString:n,isSpaMode:r,renderMeta:o,routeDiscovery:i,ssr:s}=qe(),{router:l,static:u,staticContext:c}=He(),{matches:d}=Ve(),f=Be(i,s);o&&(o.didRenderScripts=!0);let p=Qe(d,null,r);a.useEffect(()=>{0},[]);let h=a.useMemo(()=>{let r=c?`window.__reactRouterContext = ${n};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",o=u?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${f?"":`import ${JSON.stringify(t.url)}`};\n${p.map((e,n)=>{let r=`route${n}`,o=t.routes[e.route.id];Ne(o,`Route ${e.route.id} not found in manifest`);let{clientActionModule:a,clientLoaderModule:i,clientMiddlewareModule:s,hydrateFallbackModule:l,module:u}=o,c=[...a?[{module:a,varName:`${r}_clientAction`}]:[],...i?[{module:i,varName:`${r}_clientLoader`}]:[],...s?[{module:s,varName:`${r}_clientMiddleware`}]:[],...l?[{module:l,varName:`${r}_HydrateFallback`}]:[],{module:u,varName:`${r}_main`}];return 1===c.length?`import * as ${r} from ${JSON.stringify(u)};`:[c.map(e=>`import * as ${e.varName} from "${e.module}";`).join("\n"),`const ${r} = {${c.map(e=>`...${e.varName}`).join(",")}};`].join("\n")}).join("\n")}\n  ${f?`window.__reactRouterManifest = ${JSON.stringify(function(e,t){let{sri:n,...r}=e,o=new Set(t.state.matches.map(e=>e.route.id)),a=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(a.pop();a.length>0;)i.push(`/${a.join("/")}`),a.pop();i.forEach(e=>{let n=g(t.routes,e,t.basename);n&&n.forEach(e=>o.add(e.route.id))});let s=[...o].reduce((e,t)=>Object.assign(e,{[t]:r.routes[t]}),{});return{...r,routes:s,sri:!!n||void 0}}(t,l),null,2)};`:""}\n  window.__reactRouterRouteModules = {${p.map((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`).join(",")}};\n\nimport(${JSON.stringify(t.entry.module)});`:" ";return a.createElement(a.Fragment,null,a.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Fe(r),type:void 0}),a.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Fe(o),type:"module",async:!0}))},[]),m=Xe?[]:(t.entry.imports.concat(De(p,t,{includeHydrateFallback:!0})),[...new Set(y)]);var y;let v="object"===typeof t.sri?t.sri:{};return Xe?null:a.createElement(a.Fragment,null,"object"===typeof t.sri?a.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:v})}}):null,f?null:a.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:v[t.url],suppressHydrationWarning:!0}),a.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:v[t.entry.module],suppressHydrationWarning:!0}),m.map(t=>a.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:v[t],suppressHydrationWarning:!0})),h)}function et(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}var tt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{tt&&(window.__reactRouterVersion="7.6.3")}catch(ff){}function nt(e){let{basename:t,children:n,window:r}=e,o=a.useRef();null==o.current&&(o.current=s({window:r,v5Compat:!0}));let i=o.current,[l,u]=a.useState({action:i.action,location:i.location}),c=a.useCallback(e=>{a.startTransition(()=>u(e))},[u]);return a.useLayoutEffect(()=>i.listen(c),[i,c]),a.createElement(we,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i})}var rt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ot=a.forwardRef(function(e,t){let n,{onClick:r,discover:o="render",prefetch:i="none",relative:s,reloadDocument:c,replace:d,state:p,target:h,to:m,preventScrollReset:g,viewTransition:y,...v}=e,{basename:b}=a.useContext(Y),w="string"===typeof m&&rt.test(m),x=!1;if("string"===typeof m&&w&&(n=m,tt))try{let e=new URL(window.location.href),t=m.startsWith("//")?new URL(e.protocol+m):new URL(m),n=L(t.pathname,b);t.origin===e.origin&&null!=n?m=n+t.search+t.hash:x=!0}catch(ff){u(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let S=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};l(Z(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=a.useContext(Y),{hash:o,pathname:i,search:s}=ae(e,{relative:t}),u=i;return"/"!==n&&(u="/"===i?n:F([n,i])),r.createHref({pathname:u,search:s,hash:o})}(m,{relative:s}),[k,E,C]=function(e,t){let n=a.useContext(We),[r,o]=a.useState(!1),[i,s]=a.useState(!1),{onFocus:l,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,p=a.useRef(null);a.useEffect(()=>{if("render"===e&&s(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{s(e.isIntersecting)})},{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}},[e]),a.useEffect(()=>{if(r){let e=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(e)}}},[r]);let h=()=>{o(!0)},m=()=>{o(!1),s(!1)};return n?"intent"!==e?[i,p,{}]:[i,p,{onFocus:Ke(l,h),onBlur:Ke(u,m),onMouseEnter:Ke(c,h),onMouseLeave:Ke(d,m),onTouchStart:Ke(f,h)}]:[!1,p,{}]}(i,v),_=function(e){let{target:t,replace:n,state:r,preventScrollReset:o,relative:i,viewTransition:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=re(),u=ee(),c=ae(e,{relative:i});return a.useCallback(a=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(a,t)){a.preventDefault();let t=void 0!==n?n:f(u)===f(c);l(e,{replace:t,state:r,preventScrollReset:o,relative:i,viewTransition:s})}},[u,l,c,n,r,t,e,o,i,s])}(m,{replace:d,state:p,target:h,preventScrollReset:g,relative:s,viewTransition:y});let T=a.createElement("a",{...v,...C,href:n||S,onClick:x||c?r:function(e){r&&r(e),e.defaultPrevented||_(e)},ref:et(t,E),target:h,"data-discover":w||"render"!==o?void 0:"true"});return k&&!w?a.createElement(a.Fragment,null,T,a.createElement(Ye,{page:S})):T});ot.displayName="Link",a.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:o="",end:i=!1,style:s,to:u,viewTransition:c,children:d,...f}=e,p=ae(u,{relative:f.relative}),h=ee(),m=a.useContext(W),{navigator:g,basename:y}=a.useContext(Y),v=null!=m&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=a.useContext(q);l(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=st("useViewTransitionState"),o=ae(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=L(n.currentLocation.pathname,r)||n.currentLocation.pathname,s=L(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=O(o.pathname,s)||null!=O(o.pathname,i)}(p)&&!0===c,b=g.encodeLocation?g.encodeLocation(p).pathname:p.pathname,w=h.pathname,x=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;r||(w=w.toLowerCase(),x=x?x.toLowerCase():null,b=b.toLowerCase()),x&&y&&(x=L(x,y)||x);const S="/"!==b&&b.endsWith("/")?b.length-1:b.length;let k,E=w===b||!i&&w.startsWith(b)&&"/"===w.charAt(S),C=null!=x&&(x===b||!i&&x.startsWith(b)&&"/"===x.charAt(b.length)),_={isActive:E,isPending:C,isTransitioning:v},T=E?n:void 0;k="function"===typeof o?o(_):[o,E?"active":null,C?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let R="function"===typeof s?s(_):s;return a.createElement(ot,{...f,"aria-current":T,className:k,ref:t,style:R,to:u,viewTransition:c},"function"===typeof d?d(_):d)}).displayName="NavLink";var at=a.forwardRef((e,t)=>{let{discover:n="render",fetcherKey:r,navigate:o,reloadDocument:i,replace:s,state:u,method:c=ke,action:d,onSubmit:p,relative:h,preventScrollReset:m,viewTransition:g,...y}=e,v=ct(),b=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=a.useContext(Y),r=a.useContext(G);l(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),i={...ae(e||".",{relative:t})},s=ee();if(null==e){i.search=s.search;let e=new URLSearchParams(i.search),t=e.getAll("index"),n=t.some(e=>""===e);if(n){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();i.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:F([n,i.pathname]));return f(i)}(d,{relative:h}),w="get"===c.toLowerCase()?"get":"post",x="string"===typeof d&&rt.test(d);return a.createElement("form",{ref:t,method:w,action:b,onSubmit:i?p:e=>{if(p&&p(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=t?.getAttribute("formmethod")||c;v(t||e.currentTarget,{fetcherKey:r,method:n,navigate:o,replace:s,state:u,relative:h,preventScrollReset:m,viewTransition:g})},...y,"data-discover":x||"render"!==n?void 0:"true"})});function it(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function st(e){let t=a.useContext(V);return l(t,it(e)),t}at.displayName="Form";var lt=0,ut=()=>`__${String(++lt)}__`;function ct(){let{router:e}=st("useSubmit"),{basename:t}=a.useContext(Y),n=me("useRouteId");return a.useCallback(async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:a,method:i,encType:s,formData:l,body:u}=Oe(r,t);if(!1===o.navigate){let t=o.fetcherKey||ut();await e.fetch(t,n,o.action||a,{preventScrollReset:o.preventScrollReset,formData:l,body:u,formMethod:o.method||i,formEncType:o.encType||s,flushSync:o.flushSync})}else await e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:l,body:u,formMethod:o.method||i,formEncType:o.encType||s,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})},[e,t,n])}function dt(e,t){return function(){return e.apply(t,arguments)}}const{toString:ft}=Object.prototype,{getPrototypeOf:pt}=Object,{iterator:ht,toStringTag:mt}=Symbol,gt=(e=>t=>{const n=ft.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),yt=e=>(e=e.toLowerCase(),t=>gt(t)===e),vt=e=>t=>typeof t===e,{isArray:bt}=Array,wt=vt("undefined");const xt=yt("ArrayBuffer");const St=vt("string"),kt=vt("function"),Et=vt("number"),Ct=e=>null!==e&&"object"===typeof e,_t=e=>{if("object"!==gt(e))return!1;const t=pt(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(mt in e)&&!(ht in e)},Tt=yt("Date"),Rt=yt("File"),Ot=yt("Blob"),Nt=yt("FileList"),Pt=yt("URLSearchParams"),[Lt,jt,At,Dt]=["ReadableStream","Request","Response","Headers"].map(yt);function zt(e,t){let n,r,{allOwnKeys:o=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),bt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=o?Object.getOwnPropertyNames(e):Object.keys(e),a=r.length;let i;for(n=0;n<a;n++)i=r[n],t.call(null,e[i],i,e)}}function Ft(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const $t="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,It=e=>!wt(e)&&e!==$t;const Mt=(Ut="undefined"!==typeof Uint8Array&&pt(Uint8Array),e=>Ut&&e instanceof Ut);var Ut;const Bt=yt("HTMLFormElement"),Ht=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Vt=yt("RegExp"),Wt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};zt(n,(n,o)=>{let a;!1!==(a=t(n,o,e))&&(r[o]=a||n)}),Object.defineProperties(e,r)};const qt=yt("AsyncFunction"),Kt=(Qt="function"===typeof setImmediate,Yt=kt($t.postMessage),Qt?setImmediate:Yt?((e,t)=>($t.addEventListener("message",n=>{let{source:r,data:o}=n;r===$t&&o===e&&t.length&&t.shift()()},!1),n=>{t.push(n),$t.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e));var Qt,Yt;const Jt="undefined"!==typeof queueMicrotask?queueMicrotask.bind($t):"undefined"!==typeof process&&process.nextTick||Kt,Gt={isArray:bt,isArrayBuffer:xt,isBuffer:function(e){return null!==e&&!wt(e)&&null!==e.constructor&&!wt(e.constructor)&&kt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||kt(e.append)&&("formdata"===(t=gt(e))||"object"===t&&kt(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&xt(e.buffer),t},isString:St,isNumber:Et,isBoolean:e=>!0===e||!1===e,isObject:Ct,isPlainObject:_t,isReadableStream:Lt,isRequest:jt,isResponse:At,isHeaders:Dt,isUndefined:wt,isDate:Tt,isFile:Rt,isBlob:Ot,isRegExp:Vt,isFunction:kt,isStream:e=>Ct(e)&&kt(e.pipe),isURLSearchParams:Pt,isTypedArray:Mt,isFileList:Nt,forEach:zt,merge:function e(){const{caseless:t}=It(this)&&this||{},n={},r=(r,o)=>{const a=t&&Ft(n,o)||o;_t(n[a])&&_t(r)?n[a]=e(n[a],r):_t(r)?n[a]=e({},r):bt(r)?n[a]=r.slice():n[a]=r};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&zt(arguments[o],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return zt(t,(t,r)=>{n&&kt(t)?e[r]=dt(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,a,i;const s={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)i=o[a],r&&!r(i,e,t)||s[i]||(t[i]=e[i],s[i]=!0);e=!1!==n&&pt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:gt,kindOfTest:yt,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(bt(e))return e;let t=e.length;if(!Et(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[ht]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Bt,hasOwnProperty:Ht,hasOwnProp:Ht,reduceDescriptors:Wt,freezeMethods:e=>{Wt(e,(t,n)=>{if(kt(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];kt(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return bt(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Ft,global:$t,isContextDefined:It,isSpecCompliantForm:function(e){return!!(e&&kt(e.append)&&"FormData"===e[mt]&&e[ht])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Ct(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=bt(e)?[]:{};return zt(e,(e,t)=>{const a=n(e,r+1);!wt(a)&&(o[t]=a)}),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:qt,isThenable:e=>e&&(Ct(e)||kt(e))&&kt(e.then)&&kt(e.catch),setImmediate:Kt,asap:Jt,isIterable:e=>null!=e&&kt(e[ht])};function Xt(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}Gt.inherits(Xt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Gt.toJSONObject(this.config),code:this.code,status:this.status}}});const Zt=Xt.prototype,en={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{en[e]={value:e}}),Object.defineProperties(Xt,en),Object.defineProperty(Zt,"isAxiosError",{value:!0}),Xt.from=(e,t,n,r,o,a)=>{const i=Object.create(Zt);return Gt.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Xt.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};const tn=Xt;function nn(e){return Gt.isPlainObject(e)||Gt.isArray(e)}function rn(e){return Gt.endsWith(e,"[]")?e.slice(0,-2):e}function on(e,t,n){return e?e.concat(t).map(function(e,t){return e=rn(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const an=Gt.toFlatObject(Gt,{},null,function(e){return/^is[A-Z]/.test(e)});const sn=function(e,t,n){if(!Gt.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Gt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Gt.isUndefined(t[e])})).metaTokens,o=n.visitor||u,a=n.dots,i=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Gt.isSpecCompliantForm(t);if(!Gt.isFunction(o))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Gt.isDate(e))return e.toISOString();if(Gt.isBoolean(e))return e.toString();if(!s&&Gt.isBlob(e))throw new tn("Blob is not supported. Use a Buffer instead.");return Gt.isArrayBuffer(e)||Gt.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let s=e;if(e&&!o&&"object"===typeof e)if(Gt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Gt.isArray(e)&&function(e){return Gt.isArray(e)&&!e.some(nn)}(e)||(Gt.isFileList(e)||Gt.endsWith(n,"[]"))&&(s=Gt.toArray(e)))return n=rn(n),s.forEach(function(e,r){!Gt.isUndefined(e)&&null!==e&&t.append(!0===i?on([n],r,a):null===i?n:n+"[]",l(e))}),!1;return!!nn(e)||(t.append(on(o,n,a),l(e)),!1)}const c=[],d=Object.assign(an,{defaultVisitor:u,convertValue:l,isVisitable:nn});if(!Gt.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Gt.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Gt.forEach(n,function(n,a){!0===(!(Gt.isUndefined(n)||null===n)&&o.call(t,n,Gt.isString(a)?a.trim():a,r,d))&&e(n,r?r.concat(a):[a])}),c.pop()}}(e),t};function ln(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function un(e,t){this._pairs=[],e&&sn(e,this,t)}const cn=un.prototype;cn.append=function(e,t){this._pairs.push([e,t])},cn.toString=function(e){const t=e?function(t){return e.call(this,t,ln)}:ln;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const dn=un;function fn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function pn(e,t,n){if(!t)return e;const r=n&&n.encode||fn;Gt.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let a;if(a=o?o(t,n):Gt.isURLSearchParams(t)?t.toString():new dn(t,n).toString(r),a){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}const hn=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Gt.forEach(this.handlers,function(t){null!==t&&e(t)})}},mn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},gn={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:dn,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},yn="undefined"!==typeof window&&"undefined"!==typeof document,vn="object"===typeof navigator&&navigator||void 0,bn=yn&&(!vn||["ReactNative","NativeScript","NS"].indexOf(vn.product)<0),wn="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,xn=yn&&window.location.href||"http://localhost",Sn={...e,...gn};const kn=function(e){function t(e,n,r,o){let a=e[o++];if("__proto__"===a)return!0;const i=Number.isFinite(+a),s=o>=e.length;if(a=!a&&Gt.isArray(r)?r.length:a,s)return Gt.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!i;r[a]&&Gt.isObject(r[a])||(r[a]=[]);return t(e,n,r[a],o)&&Gt.isArray(r[a])&&(r[a]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let a;for(r=0;r<o;r++)a=n[r],t[a]=e[a];return t}(r[a])),!i}if(Gt.isFormData(e)&&Gt.isFunction(e.entries)){const n={};return Gt.forEachEntry(e,(e,r)=>{t(function(e){return Gt.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const En={transitional:mn,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=Gt.isObject(e);o&&Gt.isHTMLForm(e)&&(e=new FormData(e));if(Gt.isFormData(e))return r?JSON.stringify(kn(e)):e;if(Gt.isArrayBuffer(e)||Gt.isBuffer(e)||Gt.isStream(e)||Gt.isFile(e)||Gt.isBlob(e)||Gt.isReadableStream(e))return e;if(Gt.isArrayBufferView(e))return e.buffer;if(Gt.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return sn(e,new Sn.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Sn.isNode&&Gt.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((a=Gt.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return sn(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(Gt.isString(e))try{return(t||JSON.parse)(e),Gt.trim(e)}catch(ff){if("SyntaxError"!==ff.name)throw ff}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||En.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Gt.isResponse(e)||Gt.isReadableStream(e))return e;if(e&&Gt.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(ff){if(n){if("SyntaxError"===ff.name)throw tn.from(ff,tn.ERR_BAD_RESPONSE,this,null,this.response);throw ff}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Sn.classes.FormData,Blob:Sn.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Gt.forEach(["delete","get","head","post","put","patch"],e=>{En.headers[e]={}});const Cn=En,_n=Gt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Tn=Symbol("internals");function Rn(e){return e&&String(e).trim().toLowerCase()}function On(e){return!1===e||null==e?e:Gt.isArray(e)?e.map(On):String(e)}function Nn(e,t,n,r,o){return Gt.isFunction(r)?r.call(this,t,n):(o&&(t=n),Gt.isString(t)?Gt.isString(r)?-1!==t.indexOf(r):Gt.isRegExp(r)?r.test(t):void 0:void 0)}class Pn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=Rn(t);if(!o)throw new Error("header name must be a non-empty string");const a=Gt.findKey(r,o);(!a||void 0===r[a]||!0===n||void 0===n&&!1!==r[a])&&(r[a||t]=On(e))}const a=(e,t)=>Gt.forEach(e,(e,n)=>o(e,n,t));if(Gt.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(Gt.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))a((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach(function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&_n[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(Gt.isObject(e)&&Gt.isIterable(e)){let n,r,o={};for(const t of e){if(!Gt.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?Gt.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}a(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=Rn(e)){const n=Gt.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Gt.isFunction(t))return t.call(this,e,n);if(Gt.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Rn(e)){const n=Gt.findKey(this,e);return!(!n||void 0===this[n]||t&&!Nn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=Rn(e)){const o=Gt.findKey(n,e);!o||t&&!Nn(0,n[o],o,t)||(delete n[o],r=!0)}}return Gt.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Nn(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return Gt.forEach(this,(r,o)=>{const a=Gt.findKey(n,o);if(a)return t[a]=On(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(o):String(o).trim();i!==o&&delete t[o],t[i]=On(r),n[i]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Gt.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Gt.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[Tn]=this[Tn]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Rn(e);t[r]||(!function(e,t){const n=Gt.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})})}(n,e),t[r]=!0)}return Gt.isArray(e)?e.forEach(r):r(e),this}}Pn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Gt.reduceDescriptors(Pn.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),Gt.freezeMethods(Pn);const Ln=Pn;function jn(e,t){const n=this||Cn,r=t||n,o=Ln.from(r.headers);let a=r.data;return Gt.forEach(e,function(e){a=e.call(n,a,o.normalize(),t?t.status:void 0)}),o.normalize(),a}function An(e){return!(!e||!e.__CANCEL__)}function Dn(e,t,n){tn.call(this,null==e?"canceled":e,tn.ERR_CANCELED,t,n),this.name="CanceledError"}Gt.inherits(Dn,tn,{__CANCEL__:!0});const zn=Dn;function Fn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new tn("Request failed with status code "+n.status,[tn.ERR_BAD_REQUEST,tn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const $n=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,a=0,i=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),u=r[i];o||(o=l),n[a]=s,r[a]=l;let c=i,d=0;for(;c!==a;)d+=n[c++],c%=e;if(a=(a+1)%e,a===i&&(i=(i+1)%e),l-o<t)return;const f=u&&l-u;return f?Math.round(1e3*d/f):void 0}};const In=function(e,t){let n,r,o=0,a=1e3/t;const i=function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();o=a,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-o;for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];t>=a?i(l,e):(n=l,r||(r=setTimeout(()=>{r=null,i(n)},a-t)))},()=>n&&i(n)]},Mn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const o=$n(50,250);return In(n=>{const a=n.loaded,i=n.lengthComputable?n.total:void 0,s=a-r,l=o(s);r=a;e({loaded:a,total:i,progress:i?a/i:void 0,bytes:s,rate:l||void 0,estimated:l&&i&&a<=i?(i-a)/l:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},Un=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Bn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Gt.asap(()=>e(...n))},Hn=Sn.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Sn.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Sn.origin),Sn.navigator&&/(msie|trident)/i.test(Sn.navigator.userAgent)):()=>!0,Vn=Sn.hasStandardBrowserEnv?{write(e,t,n,r,o,a){const i=[e+"="+encodeURIComponent(t)];Gt.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),Gt.isString(r)&&i.push("path="+r),Gt.isString(o)&&i.push("domain="+o),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Wn(e,t,n){let r=!function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const qn=e=>e instanceof Ln?{...e}:e;function Kn(e,t){t=t||{};const n={};function r(e,t,n,r){return Gt.isPlainObject(e)&&Gt.isPlainObject(t)?Gt.merge.call({caseless:r},e,t):Gt.isPlainObject(t)?Gt.merge({},t):Gt.isArray(t)?t.slice():t}function o(e,t,n,o){return Gt.isUndefined(t)?Gt.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function a(e,t){if(!Gt.isUndefined(t))return r(void 0,t)}function i(e,t){return Gt.isUndefined(t)?Gt.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,o,a){return a in t?r(n,o):a in e?r(void 0,n):void 0}const l={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s,headers:(e,t,n)=>o(qn(e),qn(t),0,!0)};return Gt.forEach(Object.keys(Object.assign({},e,t)),function(r){const a=l[r]||o,i=a(e[r],t[r],r);Gt.isUndefined(i)&&a!==s||(n[r]=i)}),n}const Qn=e=>{const t=Kn({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:i,headers:s,auth:l}=t;if(t.headers=s=Ln.from(s),t.url=pn(Wn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),Gt.isFormData(r))if(Sn.hasStandardBrowserEnv||Sn.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}if(Sn.hasStandardBrowserEnv&&(o&&Gt.isFunction(o)&&(o=o(t)),o||!1!==o&&Hn(t.url))){const e=a&&i&&Vn.read(i);e&&s.set(a,e)}return t},Yn="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Qn(e);let o=r.data;const a=Ln.from(r.headers).normalize();let i,s,l,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Ln.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Fn(function(e){t(e),h()},function(e){n(e),h()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new tn("Request aborted",tn.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new tn("Network Error",tn.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||mn;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new tn(t,o.clarifyTimeoutError?tn.ETIMEDOUT:tn.ECONNABORTED,e,m)),m=null},void 0===o&&a.setContentType(null),"setRequestHeader"in m&&Gt.forEach(a.toJSON(),function(e,t){m.setRequestHeader(t,e)}),Gt.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([l,c]=Mn(p,!0),m.addEventListener("progress",l)),f&&m.upload&&([s,u]=Mn(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new zn(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Sn.protocols.indexOf(y)?n(new tn("Unsupported protocol "+y+":",tn.ERR_BAD_REQUEST,e)):m.send(o||null)})},Jn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof tn?t:new zn(t instanceof Error?t.message:t))}};let a=t&&setTimeout(()=>{a=null,o(new tn(`timeout ${t} of ms exceeded`,tn.ETIMEDOUT))},t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));const{signal:s}=r;return s.unsubscribe=()=>Gt.asap(i),s}},Gn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},Xn=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Zn=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of Xn(e))yield*Gn(n,t)}(e,t);let a,i=0,s=e=>{a||(a=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return s(),void e.close();let a=r.byteLength;if(n){let e=i+=a;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw s(t),t}},cancel:e=>(s(e),o.return())},{highWaterMark:2})},er="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,tr=er&&"function"===typeof ReadableStream,nr=er&&("function"===typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),rr=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(ff){return!1}},or=tr&&rr(()=>{let e=!1;const t=new Request(Sn.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ar=tr&&rr(()=>Gt.isReadableStream(new Response("").body)),ir={stream:ar&&(e=>e.body)};var sr;er&&(sr=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!ir[e]&&(ir[e]=Gt.isFunction(sr[e])?t=>t[e]():(t,n)=>{throw new tn(`Response type '${e}' is not supported`,tn.ERR_NOT_SUPPORT,n)})}));const lr=async(e,t)=>{const n=Gt.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Gt.isBlob(e))return e.size;if(Gt.isSpecCompliantForm(e)){const t=new Request(Sn.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Gt.isArrayBufferView(e)||Gt.isArrayBuffer(e)?e.byteLength:(Gt.isURLSearchParams(e)&&(e+=""),Gt.isString(e)?(await nr(e)).byteLength:void 0)})(t):n},ur=er&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:a,timeout:i,onDownloadProgress:s,onUploadProgress:l,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Qn(e);u=u?(u+"").toLowerCase():"text";let p,h=Jn([o,a&&a.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(l&&or&&"get"!==n&&"head"!==n&&0!==(g=await lr(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Gt.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=Un(g,Mn(Bn(l)));r=Zn(n.body,65536,e,t)}}Gt.isString(d)||(d=d?"include":"omit");const o="credentials"in Request.prototype;p=new Request(t,{...f,signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:o?d:void 0});let a=await fetch(p,f);const i=ar&&("stream"===u||"response"===u);if(ar&&(s||i&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});const t=Gt.toFiniteNumber(a.headers.get("content-length")),[n,r]=s&&Un(t,Mn(Bn(s),!0))||[];a=new Response(Zn(a.body,65536,n,()=>{r&&r(),m&&m()}),e)}u=u||"text";let y=await ir[Gt.findKey(ir,u)||"text"](a,e);return!i&&m&&m(),await new Promise((t,n)=>{Fn(t,n,{data:y,headers:Ln.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:p})})}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new tn("Network Error",tn.ERR_NETWORK,e,p),{cause:y.cause||y});throw tn.from(y,y&&y.code,e,p)}}),cr={http:null,xhr:Yn,fetch:ur};Gt.forEach(cr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(ff){}Object.defineProperty(e,"adapterName",{value:t})}});const dr=e=>`- ${e}`,fr=e=>Gt.isFunction(e)||null===e||!1===e,pr=e=>{e=Gt.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let a=0;a<t;a++){let t;if(n=e[a],r=n,!fr(n)&&(r=cr[(t=String(n)).toLowerCase()],void 0===r))throw new tn(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+a]=r}if(!r){const e=Object.entries(o).map(e=>{let[t,n]=e;return`adapter ${t} `+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(dr).join("\n"):" "+dr(e[0]):"as no adapter specified";throw new tn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function hr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new zn(null,e)}function mr(e){hr(e),e.headers=Ln.from(e.headers),e.data=jn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return pr(e.adapter||Cn.adapter)(e).then(function(t){return hr(e),t.data=jn.call(e,e.transformResponse,t),t.headers=Ln.from(t.headers),t},function(t){return An(t)||(hr(e),t&&t.response&&(t.response.data=jn.call(e,e.transformResponse,t.response),t.response.headers=Ln.from(t.response.headers))),Promise.reject(t)})}const gr="1.10.0",yr={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{yr[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const vr={};yr.transitional=function(e,t,n){function r(e,t){return"[Axios v"+gr+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,a)=>{if(!1===e)throw new tn(r(o," has been removed"+(t?" in "+t:"")),tn.ERR_DEPRECATED);return t&&!vr[o]&&(vr[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,a)}},yr.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const br={assertOptions:function(e,t,n){if("object"!==typeof e)throw new tn("options must be an object",tn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const a=r[o],i=t[a];if(i){const t=e[a],n=void 0===t||i(t,a,e);if(!0!==n)throw new tn("option "+a+" must be "+n,tn.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new tn("Unknown option "+a,tn.ERR_BAD_OPTION)}},validators:yr},wr=br.validators;class xr{constructor(e){this.defaults=e||{},this.interceptors={request:new hn,response:new hn}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(ff){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Kn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&br.assertOptions(n,{silentJSONParsing:wr.transitional(wr.boolean),forcedJSONParsing:wr.transitional(wr.boolean),clarifyTimeoutError:wr.transitional(wr.boolean)},!1),null!=r&&(Gt.isFunction(r)?t.paramsSerializer={serialize:r}:br.assertOptions(r,{encode:wr.function,serialize:wr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),br.assertOptions(t,{baseUrl:wr.spelling("baseURL"),withXsrfToken:wr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=o&&Gt.merge(o.common,o[t.method]);o&&Gt.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=Ln.concat(a,o);const i=[];let s=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const l=[];let u;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let c,d=0;if(!s){const e=[mr.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,l),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;for(d=0;d<c;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=mr.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=l.length;d<c;)u=u.then(l[d++],l[d++]);return u}getUri(e){return pn(Wn((e=Kn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Gt.forEach(["delete","get","head","options"],function(e){xr.prototype[e]=function(t,n){return this.request(Kn(n||{},{method:e,url:t,data:(n||{}).data}))}}),Gt.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,o){return this.request(Kn(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}xr.prototype[e]=t(),xr.prototype[e+"Form"]=t(!0)});const Sr=xr;class kr{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,o){n.reason||(n.reason=new zn(e,r,o),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new kr(function(t){e=t}),cancel:e}}}const Er=kr;const Cr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Cr).forEach(e=>{let[t,n]=e;Cr[n]=t});const _r=Cr;const Tr=function e(t){const n=new Sr(t),r=dt(Sr.prototype.request,n);return Gt.extend(r,Sr.prototype,n,{allOwnKeys:!0}),Gt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Kn(t,n))},r}(Cn);Tr.Axios=Sr,Tr.CanceledError=zn,Tr.CancelToken=Er,Tr.isCancel=An,Tr.VERSION=gr,Tr.toFormData=sn,Tr.AxiosError=tn,Tr.Cancel=Tr.CanceledError,Tr.all=function(e){return Promise.all(e)},Tr.spread=function(e){return function(t){return e.apply(null,t)}},Tr.isAxiosError=function(e){return Gt.isObject(e)&&!0===e.isAxiosError},Tr.mergeConfig=Kn,Tr.AxiosHeaders=Ln,Tr.formToJSON=e=>kn(Gt.isHTMLForm(e)?new FormData(e):e),Tr.getAdapter=pr,Tr.HttpStatusCode=_r,Tr.default=Tr;const Rr={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:3001/api",Or=Tr.create({baseURL:Rr,timeout:1e4,headers:{"Content-Type":"application/json"}});Or.interceptors.request.use(e=>{const t=localStorage.getItem("authToken");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),Or.interceptors.response.use(e=>e.data,e=>{var t,n;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("authToken"),localStorage.removeItem("userInfo"),window.location.href="/"),Promise.reject((null===(n=e.response)||void 0===n?void 0:n.data)||e.message)});const Nr=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ar";return Or.post("/auth/send-otp",{phoneNumber:e,email:t,language:n})},Pr=(e,t)=>Or.post("/auth/verify-otp",{phoneNumber:e,otpCode:t}),Lr=e=>Or.post("/auth/owner-login",{password:e}),jr=()=>Or.get("/auth/verify-token"),Ar=()=>Or.get("/displays"),Dr=e=>Or.get(`/displays/${e}`),zr=(e,t)=>Or.post(`/displays/${e}/reserve`,{customerName:t}),Fr=(e,t,n,r)=>Or.post("/transactions",{displayId:e,customerName:t,amount:n,duration:r}),$r=(e,t)=>Or.post(`/transactions/${e}/confirm`,{paymentIntentId:t}),Ir=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"****************";return Or.post("/payment/simulate-nfc-payment",{transactionId:e,cardNumber:t})},Mr=()=>Or.get("/owner/dashboard-stats"),Ur=e=>{let t=e.replace(/[\s\-\(\)]/g,"");return t.startsWith("+966")?t=t.substring(4):t.startsWith("966")?t=t.substring(3):t.startsWith("0")&&(t=t.substring(1)),`+966${t}`},Br=e=>{if(e<=0)return"00:00";const t=Math.floor(e/3600),n=Math.floor(e%3600/60),r=e%60;return t>0?`${t.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${n.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`},Hr=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"SAR",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ar-SA";return new Intl.NumberFormat(n,{style:"currency",currency:t,minimumFractionDigits:2}).format(e)},Vr=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ar-SA";return new Intl.DateTimeFormat(t,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZone:"Asia/Riyadh"}).format(new Date(e))},Wr=e=>e.replace(/[<>\"'&]/g,""),qr=(e,t)=>{try{return localStorage.setItem(e,JSON.stringify(t)),!0}catch(n){return console.error("Error saving to localStorage:",n),!1}},Kr=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{const n=localStorage.getItem(e);return n?JSON.parse(n):t}catch(n){return console.error("Error reading from localStorage:",n),t}},Qr=e=>{try{return localStorage.removeItem(e),!0}catch(t){return console.error("Error removing from localStorage:",t),!1}};var Yr=n(579);const Jr=(0,r.createContext)(),Gr=e=>{let{children:t}=e;const[n,o]=(0,r.useState)(null),[a,i]=(0,r.useState)(!0),[s,l]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{try{const e=localStorage.getItem("authToken"),t=Kr("userInfo");if(e&&t){(await jr()).success?(o(t),l(!0)):u()}}catch(e){console.error("Error checking auth:",e),u()}finally{i(!1)}})()},[]);const u=()=>{Qr("authToken"),Qr("userInfo"),o(null),l(!1)},c={user:n,loading:a,isAuthenticated:s,login:(e,t)=>{localStorage.setItem("authToken",t),qr("userInfo",e),o(e),l(!0)},logout:u,updateUser:e=>{qr("userInfo",e),o(e)}};return(0,Yr.jsx)(Jr.Provider,{value:c,children:t})},Xr=()=>{const e=(0,r.useContext)(Jr);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e},Zr=e=>"string"===typeof e,eo=()=>{let e,t;const n=new Promise((n,r)=>{e=n,t=r});return n.resolve=e,n.reject=t,n},to=e=>null==e?"":""+e,no=/###/g,ro=e=>e&&e.indexOf("###")>-1?e.replace(no,"."):e,oo=e=>!e||Zr(e),ao=(e,t,n)=>{const r=Zr(t)?t.split("."):t;let o=0;for(;o<r.length-1;){if(oo(e))return{};const t=ro(r[o]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++o}return oo(e)?{}:{obj:e,k:ro(r[o])}},io=(e,t,n)=>{const{obj:r,k:o}=ao(e,t,Object);if(void 0!==r||1===t.length)return void(r[o]=n);let a=t[t.length-1],i=t.slice(0,t.length-1),s=ao(e,i,Object);for(;void 0===s.obj&&i.length;)a=`${i[i.length-1]}.${a}`,i=i.slice(0,i.length-1),s=ao(e,i,Object),s?.obj&&"undefined"!==typeof s.obj[`${s.k}.${a}`]&&(s.obj=void 0);s.obj[`${s.k}.${a}`]=n},so=(e,t)=>{const{obj:n,k:r}=ao(e,t);if(n&&Object.prototype.hasOwnProperty.call(n,r))return n[r]},lo=(e,t,n)=>{for(const r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?Zr(e[r])||e[r]instanceof String||Zr(t[r])||t[r]instanceof String?n&&(e[r]=t[r]):lo(e[r],t[r],n):e[r]=t[r]);return e},uo=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var co={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const fo=e=>Zr(e)?e.replace(/[&<>"'\/]/g,e=>co[e]):e;const po=[" ",",","?","!",";"],ho=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}(20),mo=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}const r=t.split(n);let o=e;for(let a=0;a<r.length;){if(!o||"object"!==typeof o)return;let e,t="";for(let i=a;i<r.length;++i)if(i!==a&&(t+=n),t+=r[i],e=o[t],void 0!==e){if(["string","number","boolean"].indexOf(typeof e)>-1&&i<r.length-1)continue;a+=i-a+1;break}o=e}return o},go=e=>e?.replace("_","-"),yo={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}};class vo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||yo,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,r){return r&&!this.debug?null:(Zr(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new vo(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new vo(this.logger,e)}}var bo=new vo;class wo{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);const n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)}),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(this.observers[e]){Array.from(this.observers[e].entries()).forEach(e=>{let[t,r]=e;for(let o=0;o<r;o++)t(...n)})}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach(t=>{let[r,o]=t;for(let a=0;a<o;a++)r.apply(r,[e,...n])})}}}class xo extends wo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,a=void 0!==r.ignoreJSONStructure?r.ignoreJSONStructure:this.options.ignoreJSONStructure;let i;e.indexOf(".")>-1?i=e.split("."):(i=[e,t],n&&(Array.isArray(n)?i.push(...n):Zr(n)&&o?i.push(...n.split(o)):i.push(n)));const s=so(this.data,i);return!s&&!t&&!n&&e.indexOf(".")>-1&&(e=i[0],t=i[1],n=i.slice(2).join(".")),!s&&a&&Zr(n)?mo(this.data?.[e]?.[t],n,o):s}addResource(e,t,n,r){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1};const a=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator;let i=[e,t];n&&(i=i.concat(a?n.split(a):n)),e.indexOf(".")>-1&&(i=e.split("."),r=t,t=i[1]),this.addNamespaces(t),io(this.data,i,r),o.silent||this.emit("added",e,t,n,r)}addResources(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(const o in n)(Zr(n[o])||Array.isArray(n[o]))&&this.addResource(e,t,o,n[o],{silent:!0});r.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,r,o){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},i=[e,t];e.indexOf(".")>-1&&(i=e.split("."),r=n,n=t,t=i[1]),this.addNamespaces(t);let s=so(this.data,i)||{};a.skipCopy||(n=JSON.parse(JSON.stringify(n))),r?lo(s,n,o):s={...s,...n},io(this.data,i,s),a.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var So={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,o){return e.forEach(e=>{t=this.processors[e]?.process(t,n,r,o)??t}),t}};const ko={},Eo=e=>!Zr(e)&&"boolean"!==typeof e&&"number"!==typeof e;class Co extends wo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),((e,t,n)=>{e.forEach(e=>{t[e]&&(n[e]=t[e])})})(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=bo.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){const t={...arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}}};if(null==e)return!1;const n=this.resolve(e,t);return void 0!==n?.res}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");const r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator;let o=t.ns||this.options.defaultNS||[];const a=n&&e.indexOf(n)>-1,i=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!((e,t,n)=>{t=t||"",n=n||"";const r=po.filter(e=>t.indexOf(e)<0&&n.indexOf(e)<0);if(0===r.length)return!0;const o=ho.getRegExp(`(${r.map(e=>"?"===e?"\\?":e).join("|")})`);let a=!o.test(e);if(!a){const t=e.indexOf(n);t>0&&!o.test(e.substring(0,t))&&(a=!0)}return a})(e,n,r);if(a&&!i){const t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:Zr(o)?[o]:o};const a=e.split(n);(n!==r||n===r&&this.options.ns.indexOf(a[0])>-1)&&(o=a.shift()),e=a.join(r)}return{key:e,namespaces:Zr(o)?[o]:o}}translate(e,t,n){let r="object"===typeof t?{...t}:t;if("object"!==typeof r&&this.options.overloadTranslationOptionHandler&&(r=this.options.overloadTranslationOptionHandler(arguments)),"object"===typeof options&&(r={...r}),r||(r={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);const o=void 0!==r.returnDetails?r.returnDetails:this.options.returnDetails,a=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,{key:i,namespaces:s}=this.extractFromKey(e[e.length-1],r),l=s[s.length-1];let u=void 0!==r.nsSeparator?r.nsSeparator:this.options.nsSeparator;void 0===u&&(u=":");const c=r.lng||this.language,d=r.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if("cimode"===c?.toLowerCase())return d?o?{res:`${l}${u}${i}`,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(r)}:`${l}${u}${i}`:o?{res:i,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(r)}:i;const f=this.resolve(e,r);let p=f?.res;const h=f?.usedKey||i,m=f?.exactUsedKey||i,g=void 0!==r.joinArrays?r.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,v=void 0!==r.count&&!Zr(r.count),b=Co.hasDefaultValue(r),w=v?this.pluralResolver.getSuffix(c,r.count,r):"",x=r.ordinal&&v?this.pluralResolver.getSuffix(c,r.count,{ordinal:!1}):"",S=v&&!r.ordinal&&0===r.count,k=S&&r[`defaultValue${this.options.pluralSeparator}zero`]||r[`defaultValue${w}`]||r[`defaultValue${x}`]||r.defaultValue;let E=p;y&&!p&&b&&(E=k);const C=Eo(E),_=Object.prototype.toString.apply(E);if(!(y&&E&&C&&["[object Number]","[object Function]","[object RegExp]"].indexOf(_)<0)||Zr(g)&&Array.isArray(E))if(y&&Zr(g)&&Array.isArray(p))p=p.join(g),p&&(p=this.extendTranslation(p,e,r,n));else{let t=!1,o=!1;!this.isValidLookup(p)&&b&&(t=!0,p=k),this.isValidLookup(p)||(o=!0,p=i);const s=(r.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&o?void 0:p,d=b&&k!==p&&this.options.updateMissing;if(o||t||d){if(this.logger.log(d?"updateKey":"missingKey",c,l,i,d?k:p),a){const e=this.resolve(i,{...r,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,r.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let r=0;r<t.length;r++)e.push(t[r]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(r.lng||this.language):e.push(r.lng||this.language);const n=(e,t,n)=>{const o=b&&n!==p?n:s;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,t,o,d,r):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,l,t,o,d,r),this.emit("missingKey",e,l,t,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&v?e.forEach(e=>{const t=this.pluralResolver.getSuffixes(e,r);S&&r[`defaultValue${this.options.pluralSeparator}zero`]&&t.indexOf(`${this.options.pluralSeparator}zero`)<0&&t.push(`${this.options.pluralSeparator}zero`),t.forEach(t=>{n([e],i+t,r[`defaultValue${t}`]||k)})}):n(e,i,k))}p=this.extendTranslation(p,e,r,f,n),o&&p===i&&this.options.appendNamespaceToMissingKey&&(p=`${l}${u}${i}`),(o||t)&&this.options.parseMissingKeyHandler&&(p=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}${u}${i}`:i,t?p:void 0,r))}else{if(!r.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(h,E,{...r,ns:s}):`key '${i} (${this.language})' returned an object instead of string.`;return o?(f.res=e,f.usedParams=this.getUsedParamsDetails(r),f):e}if(a){const e=Array.isArray(E),t=e?[]:{},n=e?m:h;for(const o in E)if(Object.prototype.hasOwnProperty.call(E,o)){const e=`${n}${a}${o}`;t[o]=b&&!p?this.translate(e,{...r,defaultValue:Eo(k)?k[o]:void 0,joinArrays:!1,ns:s}):this.translate(e,{...r,joinArrays:!1,ns:s}),t[o]===e&&(t[o]=E[o])}p=t}}return o?(f.res=p,f.usedParams=this.getUsedParamsDetails(r),f):p}extendTranslation(e,t,n,r,o){var a=this;if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});const i=Zr(e)&&(void 0!==n?.interpolation?.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let s;if(i){const t=e.match(this.interpolator.nestingRegexp);s=t&&t.length}let l=n.replace&&!Zr(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),e=this.interpolator.interpolate(e,l,n.lng||this.language||r.usedLng,n),i){const t=e.match(this.interpolator.nestingRegexp);s<(t&&t.length)&&(n.nest=!1)}!n.lng&&r&&r.res&&(n.lng=this.language||r.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return o?.[0]!==r[0]||n.context?a.translate(...r,t):(a.logger.warn(`It seems you are nesting recursively key: ${r[0]} in key: ${t[0]}`),null)},n)),n.interpolation&&this.interpolator.reset()}const i=n.postProcess||this.options.postProcess,s=Zr(i)?[i]:i;return null!=e&&s?.length&&!1!==n.applyPostProcessor&&(e=So.handle(s,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t,n,r,o,a,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Zr(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;const s=this.extractFromKey(e,i),l=s.key;n=l;let u=s.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const c=void 0!==i.count&&!Zr(i.count),d=c&&!i.ordinal&&0===i.count,f=void 0!==i.context&&(Zr(i.context)||"number"===typeof i.context)&&""!==i.context,p=i.lngs?i.lngs:this.languageUtils.toResolveHierarchy(i.lng||this.language,i.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(a=e,ko[`${p[0]}-${e}`]||!this.utils?.hasLoadedNamespace||this.utils?.hasLoadedNamespace(a)||(ko[`${p[0]}-${e}`]=!0,this.logger.warn(`key "${n}" for languages "${p.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(n=>{if(this.isValidLookup(t))return;o=n;const a=[l];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(a,l,n,e,i);else{let e;c&&(e=this.pluralResolver.getSuffix(n,i.count,i));const t=`${this.options.pluralSeparator}zero`,r=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(a.push(l+e),i.ordinal&&0===e.indexOf(r)&&a.push(l+e.replace(r,this.options.pluralSeparator)),d&&a.push(l+t)),f){const n=`${l}${this.options.contextSeparator}${i.context}`;a.push(n),c&&(a.push(n+e),i.ordinal&&0===e.indexOf(r)&&a.push(n+e.replace(r,this.options.pluralSeparator)),d&&a.push(n+t))}}let s;for(;s=a.pop();)this.isValidLookup(t)||(r=s,t=this.getResource(n,e,s,i))}))})}),{res:t,usedKey:n,exactUsedKey:r,usedLng:o,usedNS:a}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!Zr(e.replace);let r=n?e.replace:e;if(n&&"undefined"!==typeof e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!n){r={...r};for(const e of t)delete r[e]}return r}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,12)&&void 0!==e[n])return!0;return!1}}class _o{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=bo.create("languageUtils")}getScriptPartFromCode(e){if(!(e=go(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=go(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(Zr(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch(ff){}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(e=>{if(t)return;const n=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(n)||(t=n)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;const n=this.getScriptPartFromCode(e);if(this.isSupportedCode(n))return t=n;const r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return t=r;t=this.options.supportedLngs.find(e=>e===r?e:e.indexOf("-")<0&&r.indexOf("-")<0?void 0:e.indexOf("-")>0&&r.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&r.length>1?e:void 0)}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if("function"===typeof e&&(e=e(t)),Zr(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes((!1===t?[]:t)||this.options.fallbackLng||[],e),r=[],o=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return Zr(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&o(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&o(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&o(this.getLanguagePartFromCode(e))):Zr(e)&&o(this.formatLanguageCode(e)),n.forEach(e=>{r.indexOf(e)<0&&o(this.formatLanguageCode(e))}),r}}const To={zero:0,one:1,two:2,few:3,many:4,other:5},Ro={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Oo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=bo.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=go("dev"===e?"en":e),r=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:n,type:r});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let a;try{a=new Intl.PluralRules(n,{type:r})}catch(i){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Ro;if(!e.match(/-|_/))return Ro;const n=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(n,t)}return this.pluralRulesCache[o]=a,a}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),n?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),n?n.resolvedOptions().pluralCategories.sort((e,t)=>To[e]-To[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):[]}getSuffix(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=this.getRule(e,n);return r?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${r.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,n))}}const No=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=((e,t,n)=>{const r=so(e,n);return void 0!==r?r:so(t,n)})(e,t,n);return!a&&o&&Zr(n)&&(a=mo(e,n,r),void 0===a&&(a=mo(t,n,r))),a},Po=e=>e.replace(/\$/g,"$$$$");class Lo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=bo.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:r,prefix:o,prefixEscaped:a,suffix:i,suffixEscaped:s,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:f,nestingSuffix:p,nestingSuffixEscaped:h,nestingOptionsSeparator:m,maxReplaces:g,alwaysFormat:y}=e.interpolation;this.escape=void 0!==t?t:fo,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==r&&r,this.prefix=o?uo(o):a||"{{",this.suffix=i?uo(i):s||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?uo(d):f||uo("$t("),this.nestingSuffix=p?uo(p):h||uo(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=g||1e3,this.alwaysFormat=void 0!==y&&y,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e?.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,r){let o,a,i;const s=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=e=>{if(e.indexOf(this.formatSeparator)<0){const o=No(t,s,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(o,void 0,n,{...r,...t,interpolationkey:e}):o}const o=e.split(this.formatSeparator),a=o.shift().trim(),i=o.join(this.formatSeparator).trim();return this.format(No(t,s,a,this.options.keySeparator,this.options.ignoreJSONStructure),i,n,{...r,...t,interpolationkey:a})};this.resetRegExp();const u=r?.missingInterpolationHandler||this.options.missingInterpolationHandler,c=void 0!==r?.interpolation?.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>Po(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?Po(this.escape(e)):Po(e)}].forEach(t=>{for(i=0;o=t.regex.exec(e);){const n=o[1].trim();if(a=l(n),void 0===a)if("function"===typeof u){const t=u(e,o,r);a=Zr(t)?t:""}else if(r&&Object.prototype.hasOwnProperty.call(r,n))a="";else{if(c){a=o[0];continue}this.logger.warn(`missed to pass in variable ${n} for interpolating ${e}`),a=""}else Zr(a)||this.useRawValueToEscape||(a=to(a));const s=t.safeValue(a);if(e=e.replace(o[0],s),c?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,i++,i>=this.maxReplaces)break}}),e}nest(e,t){let n,r,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=(e,t)=>{const n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;const r=e.split(new RegExp(`${n}[ ]*{`));let a=`{${r[1]}`;e=r[0],a=this.interpolate(a,o);const i=a.match(/'/g),s=a.match(/"/g);((i?.length??0)%2===0&&!s||s.length%2!==0)&&(a=a.replace(/'/g,'"'));try{o=JSON.parse(a),t&&(o={...t,...o})}catch(ff){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,ff),`${e}${n}${a}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,e};for(;n=this.nestingRegexp.exec(e);){let s=[];o={...a},o=o.replace&&!Zr(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;const l=/{.*}/.test(n[1])?n[1].lastIndexOf("}")+1:n[1].indexOf(this.formatSeparator);if(-1!==l&&(s=n[1].slice(l).split(this.formatSeparator).map(e=>e.trim()).filter(Boolean),n[1]=n[1].slice(0,l)),r=t(i.call(this,n[1].trim(),o),o),r&&n[0]===e&&!Zr(r))return r;Zr(r)||(r=to(r)),r||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),r=""),s.length&&(r=s.reduce((e,t)=>this.format(e,t,a.lng,{...a,interpolationkey:n[1].trim()}),r.trim())),e=e.replace(n[0],r),this.regexp.lastIndex=0}return e}}const jo=e=>{const t={};return(n,r,o)=>{let a=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(a={...a,[o.interpolationkey]:void 0});const i=r+JSON.stringify(a);let s=t[i];return s||(s=e(go(r),o),t[i]=s),s(n)}},Ao=e=>(t,n,r)=>e(go(n),r)(t);class Do{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=bo.create("formatter"),this.options=e,this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||",";const n=t.cacheInBuiltFormats?jo:Ao;this.formats={number:n((e,t)=>{const n=new Intl.NumberFormat(e,{...t});return e=>n.format(e)}),currency:n((e,t)=>{const n=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>n.format(e)}),datetime:n((e,t)=>{const n=new Intl.DateTimeFormat(e,{...t});return e=>n.format(e)}),relativetime:n((e,t)=>{const n=new Intl.RelativeTimeFormat(e,{...t});return e=>n.format(e,t.range||"day")}),list:n((e,t)=>{const n=new Intl.ListFormat(e,{...t});return e=>n.format(e)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=jo(t)}format(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find(e=>e.indexOf(")")>-1)){const e=o.findIndex(e=>e.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}const a=o.reduce((e,t)=>{const{formatName:o,formatOptions:a}=(e=>{let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const o=r[1].substring(0,r[1].length-1);"currency"===t&&o.indexOf(":")<0?n.currency||(n.currency=o.trim()):"relativetime"===t&&o.indexOf(":")<0?n.range||(n.range=o.trim()):o.split(";").forEach(e=>{if(e){const[t,...r]=e.split(":"),o=r.join(":").trim().replace(/^'+|'+$/g,""),a=t.trim();n[a]||(n[a]=o),"false"===o&&(n[a]=!1),"true"===o&&(n[a]=!0),isNaN(o)||(n[a]=parseInt(o,10))}})}return{formatName:t,formatOptions:n}})(t);if(this.formats[o]){let t=e;try{const i=r?.formatParams?.[r.interpolationkey]||{},s=i.locale||i.lng||r.locale||r.lng||n;t=this.formats[o](e,s,{...a,...r,...i})}catch(i){this.logger.warn(i)}return t}return this.logger.warn(`there was no format function for ${o}`),e},e);return a}}class zo extends wo{constructor(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=r,this.logger=bo.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(n,r.backend,r)}queueLoad(e,t,n,r){const o={},a={},i={},s={};return e.forEach(e=>{let r=!0;t.forEach(t=>{const i=`${e}|${t}`;!n.reload&&this.store.hasResourceBundle(e,t)?this.state[i]=2:this.state[i]<0||(1===this.state[i]?void 0===a[i]&&(a[i]=!0):(this.state[i]=1,r=!1,void 0===a[i]&&(a[i]=!0),void 0===o[i]&&(o[i]=!0),void 0===s[t]&&(s[t]=!0)))}),r||(i[e]=!0)}),(Object.keys(o).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(o),pending:Object.keys(a),toLoadLanguages:Object.keys(i),toLoadNamespaces:Object.keys(s)}}loaded(e,t,n){const r=e.split("|"),o=r[0],a=r[1];t&&this.emit("failedLoading",o,a,t),!t&&n&&this.store.addResourceBundle(o,a,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const i={};this.queue.forEach(n=>{((e,t,n)=>{const{obj:r,k:o}=ao(e,t,Object);r[o]=r[o]||[],r[o].push(n)})(n.loaded,[o],a),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach(e=>{i[e]||(i[e]={});const t=n.loaded[e];t.length&&t.forEach(t=>{void 0===i[e][t]&&(i[e][t]=!0)})}),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())}),this.emit("loaded",i),this.queue=this.queue.filter(e=>!e.done)}read(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:n,tried:r,wait:o,callback:a});this.readingCalls++;const i=(i,s)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}i&&s&&r<this.maxRetries?setTimeout(()=>{this.read.call(this,e,t,n,r+1,2*o,a)},o):a(i,s)},s=this.backend[n].bind(this.backend);if(2!==s.length)return s(e,t,i);try{const n=s(e,t);n&&"function"===typeof n.then?n.then(e=>i(null,e)).catch(i):i(null,n)}catch(l){i(l)}}prepareLoading(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();Zr(e)&&(e=this.languageUtils.toResolveHierarchy(e)),Zr(t)&&(t=[t]);const o=this.queueLoad(e,t,n,r);if(!o.toLoad.length)return o.pending.length||r(),null;o.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const n=e.split("|"),r=n[0],o=n[1];this.read(r,o,"read",void 0,void 0,(n,a)=>{n&&this.logger.warn(`${t}loading namespace ${o} for language ${r} failed`,n),!n&&a&&this.logger.log(`${t}loaded namespace ${o} for language ${r}`,a),this.loaded(e,n,a)})}saveMissing(e,t,n,r,o){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(!this.services?.utils?.hasLoadedNamespace||this.services?.utils?.hasLoadedNamespace(t)){if(void 0!==n&&null!==n&&""!==n){if(this.backend?.create){const l={...a,isUpdate:o},u=this.backend.create.bind(this.backend);if(u.length<6)try{let o;o=5===u.length?u(e,t,n,r,l):u(e,t,n,r),o&&"function"===typeof o.then?o.then(e=>i(null,e)).catch(i):i(null,o)}catch(s){i(s)}else u(e,t,n,r,i,l)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}else this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")}}const Fo=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"===typeof e[1]&&(t=e[1]),Zr(e[1])&&(t.defaultValue=e[1]),Zr(e[2])&&(t.tDescription=e[2]),"object"===typeof e[2]||"object"===typeof e[3]){const n=e[3]||e[2];Object.keys(n).forEach(e=>{t[e]=n[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),$o=e=>(Zr(e.ns)&&(e.ns=[e.ns]),Zr(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),Zr(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"===typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),Io=()=>{};class Mo extends wo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;var n;if(super(),this.options=$o(e),this.services={},this.logger=bo,this.modules={external:[]},n=this,Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach(e=>{"function"===typeof n[e]&&(n[e]=n[e].bind(n))}),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"===typeof t&&(n=t,t={}),null==t.defaultNS&&t.ns&&(Zr(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const r=Fo();this.options={...r,...this.options,...$o(t)},this.options.interpolation={...r.interpolation,...this.options.interpolation},void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);const o=e=>e?"function"===typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?bo.init(o(this.modules.logger),this.options):bo.init(null,this.options),t=this.modules.formatter?this.modules.formatter:Do;const n=new _o(this.options);this.store=new xo(this.options.resources,this.options);const a=this.services;a.logger=bo,a.resourceStore=this.store,a.languageUtils=n,a.pluralResolver=new Oo(n,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix});this.options.interpolation.format&&this.options.interpolation.format!==r.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),!t||this.options.interpolation.format&&this.options.interpolation.format!==r.interpolation.format||(a.formatter=o(t),a.formatter.init&&a.formatter.init(a,this.options),this.options.interpolation.format=a.formatter.format.bind(a.formatter)),a.interpolator=new Lo(this.options),a.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},a.backendConnector=new zo(o(this.modules.backend),a.resourceStore,a,this.options),a.backendConnector.on("*",function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];e.emit(t,...r)}),this.modules.languageDetector&&(a.languageDetector=o(this.modules.languageDetector),a.languageDetector.init&&a.languageDetector.init(a,this.options.detection,this.options)),this.modules.i18nFormat&&(a.i18nFormat=o(this.modules.i18nFormat),a.i18nFormat.init&&a.i18nFormat.init(this)),this.translator=new Co(this.services,this.options),this.translator.on("*",function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];e.emit(t,...r)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,n||(n=Io),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}});["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});const a=eo(),i=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),n(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?i():setTimeout(i,0),a}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Io;const n=Zr(e)?e:this.language;if("function"===typeof e&&(t=e),!this.options.resources||this.options.partialBundledLanguages){if("cimode"===n?.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return t();const e=[],r=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)})};if(n)r(n);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>r(e))}this.options.preload?.forEach?.(e=>r(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),t(e)})}else t(null)}reloadResources(e,t,n){const r=eo();return"function"===typeof e&&(n=e,e=void 0),"function"===typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=Io),this.services.backendConnector.reload(e,t,e=>{r.resolve(),n(e)}),r}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&So.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){const t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const r=eo();this.emit("languageChanging",e);const o=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(a,i)=>{i?this.isLanguageChangingTo===e&&(o(i),this.translator.changeLanguage(i),this.isLanguageChangingTo=void 0,this.emit("languageChanged",i),this.logger.log("languageChanged",i)):this.isLanguageChangingTo=void 0,r.resolve(function(){return n.t(...arguments)}),t&&t(a,function(){return n.t(...arguments)})},i=t=>{e||t||!this.services.languageDetector||(t=[]);const n=Zr(t)?t:t&&t[0],r=this.store.hasLanguageSomeTranslations(n)?n:this.services.languageUtils.getBestMatchFromCodes(Zr(t)?[t]:t);r&&(this.language||o(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector?.cacheUserLanguage?.(r)),this.loadResources(r,e=>{a(e,r)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(i):this.services.languageDetector.detect(i):i(e):i(this.services.languageDetector.detect()),r}getFixedT(e,t,n){var r=this;const o=function(e,t){let a;if("object"!==typeof t){for(var i=arguments.length,s=new Array(i>2?i-2:0),l=2;l<i;l++)s[l-2]=arguments[l];a=r.options.overloadTranslationOptionHandler([e,t].concat(s))}else a={...t};a.lng=a.lng||o.lng,a.lngs=a.lngs||o.lngs,a.ns=a.ns||o.ns,""!==a.keyPrefix&&(a.keyPrefix=a.keyPrefix||n||o.keyPrefix);const u=r.options.keySeparator||".";let c;return c=a.keyPrefix&&Array.isArray(e)?e.map(e=>`${a.keyPrefix}${u}${e}`):a.keyPrefix?`${a.keyPrefix}${u}${e}`:e,r.t(c,a)};return Zr(e)?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=n,o}t(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.translator?.translate(...t)}exists(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.translator?.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;const a=(e,t)=>{const n=this.services.backendConnector.state[`${e}|${t}`];return-1===n||0===n||2===n};if(t.precheck){const e=t.precheck(this,a);if(void 0!==e)return e}return!!this.hasResourceBundle(n,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!a(n,e)||r&&!a(o,e)))}loadNamespaces(e,t){const n=eo();return this.options.ns?(Zr(e)&&(e=[e]),e.forEach(e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=eo();Zr(e)&&(e=[e]);const r=this.options.preload||[],o=e.filter(e=>r.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e));return o.length?(this.options.preload=r.concat(o),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!e)return"rtl";try{const t=new Intl.Locale(e);if(t&&t.getTextInfo){const e=t.getTextInfo();if(e&&e.direction)return e.direction}}catch(ff){}const t=this.services?.languageUtils||new _o(Fo());return e.toLowerCase().indexOf("-latn")>1?"ltr":["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new Mo(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Io;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const r={...this.options,...e,isClone:!0},o=new Mo(r);void 0===e.debug&&void 0===e.prefix||(o.logger=o.logger.clone(e));if(["store","services","language"].forEach(e=>{o[e]=this[e]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},n){const e=Object.keys(this.store.data).reduce((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce((n,r)=>(n[r]={...e[t][r]},n),e[t]),e),{});o.store=new xo(e,r),o.services.resourceStore=o.store}return o.translator=new Co(o.services,r),o.translator.on("*",function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];o.emit(e,...n)}),o.init(r,t),o.translator.options=r,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Uo=Mo.createInstance();Uo.createInstance=Mo.createInstance;Uo.createInstance,Uo.dir,Uo.init,Uo.loadResources,Uo.reloadResources,Uo.use,Uo.changeLanguage,Uo.getFixedT,Uo.t,Uo.exists,Uo.setDefaultNamespace,Uo.hasLoadedNamespace,Uo.loadNamespaces,Uo.loadLanguages;n(514);Object.create(null);const Bo={},Ho=(e,t,n,r)=>{Ko(n)&&Bo[n]||(Ko(n)&&(Bo[n]=new Date),((e,t,n,r)=>{const o=[n,{code:t,...r||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(o,"warn","react-i18next::",!0);Ko(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...o):console?.warn&&console.warn(...o)})(e,t,n,r))},Vo=(e,t)=>()=>{if(e.isInitialized)t();else{const n=()=>{setTimeout(()=>{e.off("initialized",n)},0),t()};e.on("initialized",n)}},Wo=(e,t,n)=>{e.loadNamespaces(t,Vo(e,n))},qo=(e,t,n,r)=>{if(Ko(n)&&(n=[n]),e.options.preload&&e.options.preload.indexOf(t)>-1)return Wo(e,n,r);n.forEach(t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)}),e.loadLanguages(t,Vo(e,r))},Ko=e=>"string"===typeof e,Qo=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Yo={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},Jo=e=>Yo[e];let Go={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(Qo,Jo)};let Xo;const Zo={type:"3rdParty",init(e){!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Go={...Go,...e}}(e.options.react),(e=>{Xo=e})(e)}},ea=(0,a.createContext)();class ta{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const na=(e,t,n,r)=>e.getFixedT(t,n,r),ra=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:n}=t,{i18n:r,defaultNS:o}=(0,a.useContext)(ea)||{},i=n||r||Xo;if(i&&!i.reportNamespaces&&(i.reportNamespaces=new ta),!i){Ho(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>{return Ko(t)?t:"object"===typeof(n=t)&&null!==n&&Ko(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e;var n},t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}i.options.react?.wait&&Ho(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const s={...Go,...i.options.react,...t},{useSuspense:l,keyPrefix:u}=s;let c=e||o||i.options?.defaultNS;c=Ko(c)?[c]:c||["translation"],i.reportNamespaces.addUsedNamespaces?.(c);const d=(i.isInitialized||i.initializedStoreOnce)&&c.every(e=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:n.lng,precheck:(t,r)=>{if(n.bindI18n?.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e))return!1}}):(Ho(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0)}(e,i,s)),f=((e,t,n,r)=>(0,a.useCallback)(na(e,t,n,r),[e,t,n,r]))(i,t.lng||null,"fallback"===s.nsMode?c:c[0],u),p=()=>f,h=()=>na(i,t.lng||null,"fallback"===s.nsMode?c:c[0],u),[m,g]=(0,a.useState)(p);let y=c.join();t.lng&&(y=`${t.lng}${y}`);const v=((e,t)=>{const n=(0,a.useRef)();return(0,a.useEffect)(()=>{n.current=t?n.current:e},[e,t]),n.current})(y),b=(0,a.useRef)(!0);(0,a.useEffect)(()=>{const{bindI18n:e,bindI18nStore:n}=s;b.current=!0,d||l||(t.lng?qo(i,t.lng,c,()=>{b.current&&g(h)}):Wo(i,c,()=>{b.current&&g(h)})),d&&v&&v!==y&&b.current&&g(h);const r=()=>{b.current&&g(h)};return e&&i?.on(e,r),n&&i?.store.on(n,r),()=>{b.current=!1,i&&e?.split(" ").forEach(e=>i.off(e,r)),n&&i&&n.split(" ").forEach(e=>i.store.off(e,r))}},[i,y]),(0,a.useEffect)(()=>{b.current&&d&&g(p)},[i,u,d]);const w=[m,i,d];if(w.t=m,w.i18n=i,w.ready=d,d)return w;if(!d&&!l)return w;throw new Promise(e=>{t.lng?qo(i,t.lng,c,()=>e()):Wo(i,c,()=>e())})};Uo.use(Zo).init({resources:{ar:{translation:{welcome:"\u0623\u0647\u0644\u0627\u064b \u0648\u0633\u0647\u0644\u0627\u064b \u0628\u0643\u0645",companyName:"\u0646\u0638\u0627\u0645 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0634\u0627\u0634\u0627\u062a",selectLanguage:"\u0627\u062e\u062a\u0631 \u0627\u0644\u0644\u063a\u0629",startTransaction:"\u0627\u0628\u062f\u0623 \u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0629",selectDisplay:"\u0627\u062e\u062a\u0631 \u0627\u0644\u0634\u0627\u0634\u0629",displayNumber:"\u0634\u0627\u0634\u0629 {{number}}",available:"\u0645\u062a\u0627\u062d\u0629",occupied:"\u0645\u0634\u063a\u0648\u0644\u0629",reserved:"\u0645\u062d\u062c\u0648\u0632\u0629",maintenance:"\u0635\u064a\u0627\u0646\u0629",timeRemaining:"\u0627\u0644\u0648\u0642\u062a \u0627\u0644\u0645\u062a\u0628\u0642\u064a: {{time}}",phoneNumber:"\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641",email:"\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a",enterPhoneNumber:"\u0623\u062f\u062e\u0644 \u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641",enterEmail:"\u0623\u062f\u062e\u0644 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a",sendOTP:"\u0625\u0631\u0633\u0627\u0644 \u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642",otpCode:"\u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642",enterOTP:"\u0623\u062f\u062e\u0644 \u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642",verifyOTP:"\u062a\u062d\u0642\u0642 \u0645\u0646 \u0627\u0644\u0631\u0645\u0632",otpSent:"\u062a\u0645 \u0625\u0631\u0633\u0627\u0644 \u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642 \u0625\u0644\u0649 \u0628\u0631\u064a\u062f\u0643 \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a",customerName:"\u0627\u0633\u0645 \u0627\u0644\u0639\u0645\u064a\u0644",enterName:"\u0623\u062f\u062e\u0644 \u0627\u0633\u0645\u0643",displayPrice:"\u0627\u0644\u0633\u0639\u0631: {{price}} \u0631\u064a\u0627\u0644",displayDuration:"\u0645\u062f\u0629 \u0627\u0644\u0639\u0631\u0636: {{duration}} \u062f\u0642\u064a\u0642\u0629",proceedToPayment:"\u0627\u0644\u0645\u062a\u0627\u0628\u0639\u0629 \u0644\u0644\u062f\u0641\u0639",payment:"\u0627\u0644\u062f\u0641\u0639",paymentMethod:"\u0637\u0631\u064a\u0642\u0629 \u0627\u0644\u062f\u0641\u0639",nfcPayment:"\u062f\u0641\u0639 \u0628\u0627\u0644\u0628\u0637\u0627\u0642\u0629 \u0627\u0644\u0644\u0627\u0633\u0644\u0643\u064a\u0629",cardPayment:"\u062f\u0641\u0639 \u0628\u0627\u0644\u0628\u0637\u0627\u0642\u0629",placeCardOnReader:"\u0636\u0639 \u0628\u0637\u0627\u0642\u062a\u0643 \u0639\u0644\u0649 \u0627\u0644\u0642\u0627\u0631\u0626",processing:"\u062c\u0627\u0631\u064a \u0627\u0644\u0645\u0639\u0627\u0644\u062c\u0629...",paymentSuccessful:"\u062a\u0645 \u0627\u0644\u062f\u0641\u0639 \u0628\u0646\u062c\u0627\u062d",paymentFailed:"\u0641\u0634\u0644 \u0641\u064a \u0627\u0644\u062f\u0641\u0639",transactionNumber:"\u0631\u0642\u0645 \u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0629",transactionDetails:"\u062a\u0641\u0627\u0635\u064a\u0644 \u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0629",amount:"\u0627\u0644\u0645\u0628\u0644\u063a",duration:"\u0627\u0644\u0645\u062f\u0629",startTime:"\u0648\u0642\u062a \u0627\u0644\u0628\u062f\u0627\u064a\u0629",endTime:"\u0648\u0642\u062a \u0627\u0644\u0627\u0646\u062a\u0647\u0627\u0621",next:"\u0627\u0644\u062a\u0627\u0644\u064a",back:"\u0627\u0644\u0633\u0627\u0628\u0642",cancel:"\u0625\u0644\u063a\u0627\u0621",confirm:"\u062a\u0623\u0643\u064a\u062f",retry:"\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629",close:"\u0625\u063a\u0644\u0627\u0642",error:"\u062e\u0637\u0623",invalidPhoneNumber:"\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641 \u063a\u064a\u0631 \u0635\u062d\u064a\u062d",invalidEmail:"\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u063a\u064a\u0631 \u0635\u062d\u064a\u062d",invalidOTP:"\u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642 \u063a\u064a\u0631 \u0635\u062d\u064a\u062d",networkError:"\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629",serverError:"\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u062e\u0627\u062f\u0645",ownerDashboard:"\u0644\u0648\u062d\u0629 \u062a\u062d\u0643\u0645 \u0627\u0644\u0645\u0627\u0644\u0643",statistics:"\u0627\u0644\u0625\u062d\u0635\u0627\u0626\u064a\u0627\u062a",todayTransactions:"\u0645\u0639\u0627\u0645\u0644\u0627\u062a \u0627\u0644\u064a\u0648\u0645",todayRevenue:"\u0625\u064a\u0631\u0627\u062f\u0627\u062a \u0627\u0644\u064a\u0648\u0645",totalTransactions:"\u0625\u062c\u0645\u0627\u0644\u064a \u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0627\u062a",totalRevenue:"\u0625\u062c\u0645\u0627\u0644\u064a \u0627\u0644\u0625\u064a\u0631\u0627\u062f\u0627\u062a",activeTransactions:"\u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0627\u062a \u0627\u0644\u0646\u0634\u0637\u0629",displayStatus:"\u062d\u0627\u0644\u0629 \u0627\u0644\u0634\u0627\u0634\u0627\u062a",settings:"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a",reports:"\u0627\u0644\u062a\u0642\u0627\u0631\u064a\u0631",minutes:"\u062f\u0642\u064a\u0642\u0629",seconds:"\u062b\u0627\u0646\u064a\u0629",hours:"\u0633\u0627\u0639\u0629",days:"\u064a\u0648\u0645"}},en:{translation:{welcome:"Welcome",companyName:"Display Management System",selectLanguage:"Select Language",startTransaction:"Start Transaction",selectDisplay:"Select Display",displayNumber:"Display {{number}}",available:"Available",occupied:"Occupied",reserved:"Reserved",maintenance:"Maintenance",timeRemaining:"Time remaining: {{time}}",phoneNumber:"Phone Number",email:"Email",enterPhoneNumber:"Enter phone number",enterEmail:"Enter email address",sendOTP:"Send Verification Code",otpCode:"Verification Code",enterOTP:"Enter verification code",verifyOTP:"Verify Code",otpSent:"Verification code sent to your email",customerName:"Customer Name",enterName:"Enter your name",displayPrice:"Price: {{price}} SAR",displayDuration:"Duration: {{duration}} minutes",proceedToPayment:"Proceed to Payment",payment:"Payment",paymentMethod:"Payment Method",nfcPayment:"NFC Card Payment",cardPayment:"Card Payment",placeCardOnReader:"Place your card on the reader",processing:"Processing...",paymentSuccessful:"Payment Successful",paymentFailed:"Payment Failed",transactionNumber:"Transaction Number",transactionDetails:"Transaction Details",amount:"Amount",duration:"Duration",startTime:"Start Time",endTime:"End Time",next:"Next",back:"Back",cancel:"Cancel",confirm:"Confirm",retry:"Retry",close:"Close",error:"Error",invalidPhoneNumber:"Invalid phone number",invalidEmail:"Invalid email address",invalidOTP:"Invalid verification code",networkError:"Network error",serverError:"Server error",ownerDashboard:"Owner Dashboard",statistics:"Statistics",todayTransactions:"Today's Transactions",todayRevenue:"Today's Revenue",totalTransactions:"Total Transactions",totalRevenue:"Total Revenue",activeTransactions:"Active Transactions",displayStatus:"Display Status",settings:"Settings",reports:"Reports",minutes:"minutes",seconds:"seconds",hours:"hours",days:"days"}}},lng:localStorage.getItem("language")||"ar",fallbackLng:"ar",interpolation:{escapeValue:!1},react:{useSuspense:!1}});var oa=function(){return oa=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},oa.apply(this,arguments)};Object.create;function aa(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"===typeof SuppressedError&&SuppressedError;var ia=n(570),sa=n.n(ia),la="-ms-",ua="-moz-",ca="-webkit-",da="comm",fa="rule",pa="decl",ha="@keyframes",ma=Math.abs,ga=String.fromCharCode,ya=Object.assign;function va(e){return e.trim()}function ba(e,t){return(e=t.exec(e))?e[0]:e}function wa(e,t,n){return e.replace(t,n)}function xa(e,t,n){return e.indexOf(t,n)}function Sa(e,t){return 0|e.charCodeAt(t)}function ka(e,t,n){return e.slice(t,n)}function Ea(e){return e.length}function Ca(e){return e.length}function _a(e,t){return t.push(e),e}function Ta(e,t){return e.filter(function(e){return!ba(e,t)})}var Ra=1,Oa=1,Na=0,Pa=0,La=0,ja="";function Aa(e,t,n,r,o,a,i,s){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:Ra,column:Oa,length:i,return:"",siblings:s}}function Da(e,t){return ya(Aa("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function za(e){for(;e.root;)e=Da(e.root,{children:[e]});_a(e,e.siblings)}function Fa(){return La=Pa>0?Sa(ja,--Pa):0,Oa--,10===La&&(Oa=1,Ra--),La}function $a(){return La=Pa<Na?Sa(ja,Pa++):0,Oa++,10===La&&(Oa=1,Ra++),La}function Ia(){return Sa(ja,Pa)}function Ma(){return Pa}function Ua(e,t){return ka(ja,e,t)}function Ba(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Ha(e){return Ra=Oa=1,Na=Ea(ja=e),Pa=0,[]}function Va(e){return ja="",e}function Wa(e){return va(Ua(Pa-1,Qa(91===e?e+2:40===e?e+1:e)))}function qa(e){for(;(La=Ia())&&La<33;)$a();return Ba(e)>2||Ba(La)>3?"":" "}function Ka(e,t){for(;--t&&$a()&&!(La<48||La>102||La>57&&La<65||La>70&&La<97););return Ua(e,Ma()+(t<6&&32==Ia()&&32==$a()))}function Qa(e){for(;$a();)switch(La){case e:return Pa;case 34:case 39:34!==e&&39!==e&&Qa(La);break;case 40:41===e&&Qa(e);break;case 92:$a()}return Pa}function Ya(e,t){for(;$a()&&e+La!==57&&(e+La!==84||47!==Ia()););return"/*"+Ua(t,Pa-1)+"*"+ga(47===e?e:$a())}function Ja(e){for(;!Ba(Ia());)$a();return Ua(e,Pa)}function Ga(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function Xa(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case pa:return e.return=e.return||e.value;case da:return"";case ha:return e.return=e.value+"{"+Ga(e.children,r)+"}";case fa:if(!Ea(e.value=e.props.join(",")))return""}return Ea(n=Ga(e.children,r))?e.return=e.value+"{"+n+"}":""}function Za(e,t,n){switch(function(e,t){return 45^Sa(e,0)?(((t<<2^Sa(e,0))<<2^Sa(e,1))<<2^Sa(e,2))<<2^Sa(e,3):0}(e,t)){case 5103:return ca+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return ca+e+e;case 4789:return ua+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return ca+e+ua+e+la+e+e;case 5936:switch(Sa(e,t+11)){case 114:return ca+e+la+wa(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return ca+e+la+wa(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return ca+e+la+wa(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return ca+e+la+e+e;case 6165:return ca+e+la+"flex-"+e+e;case 5187:return ca+e+wa(e,/(\w+).+(:[^]+)/,ca+"box-$1$2"+la+"flex-$1$2")+e;case 5443:return ca+e+la+"flex-item-"+wa(e,/flex-|-self/g,"")+(ba(e,/flex-|baseline/)?"":la+"grid-row-"+wa(e,/flex-|-self/g,""))+e;case 4675:return ca+e+la+"flex-line-pack"+wa(e,/align-content|flex-|-self/g,"")+e;case 5548:return ca+e+la+wa(e,"shrink","negative")+e;case 5292:return ca+e+la+wa(e,"basis","preferred-size")+e;case 6060:return ca+"box-"+wa(e,"-grow","")+ca+e+la+wa(e,"grow","positive")+e;case 4554:return ca+wa(e,/([^-])(transform)/g,"$1"+ca+"$2")+e;case 6187:return wa(wa(wa(e,/(zoom-|grab)/,ca+"$1"),/(image-set)/,ca+"$1"),e,"")+e;case 5495:case 3959:return wa(e,/(image-set\([^]*)/,ca+"$1$`$1");case 4968:return wa(wa(e,/(.+:)(flex-)?(.*)/,ca+"box-pack:$3"+la+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+ca+e+e;case 4200:if(!ba(e,/flex-|baseline/))return la+"grid-column-align"+ka(e,t)+e;break;case 2592:case 3360:return la+wa(e,"template-","")+e;case 4384:case 3616:return n&&n.some(function(e,n){return t=n,ba(e.props,/grid-\w+-end/)})?~xa(e+(n=n[t].value),"span",0)?e:la+wa(e,"-start","")+e+la+"grid-row-span:"+(~xa(n,"span",0)?ba(n,/\d+/):+ba(n,/\d+/)-+ba(e,/\d+/))+";":la+wa(e,"-start","")+e;case 4896:case 4128:return n&&n.some(function(e){return ba(e.props,/grid-\w+-start/)})?e:la+wa(wa(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return wa(e,/(.+)-inline(.+)/,ca+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Ea(e)-1-t>6)switch(Sa(e,t+1)){case 109:if(45!==Sa(e,t+4))break;case 102:return wa(e,/(.+:)(.+)-([^]+)/,"$1"+ca+"$2-$3$1"+ua+(108==Sa(e,t+3)?"$3":"$2-$3"))+e;case 115:return~xa(e,"stretch",0)?Za(wa(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return wa(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(t,n,r,o,a,i,s){return la+n+":"+r+s+(o?la+n+"-span:"+(a?i:+i-+r)+s:"")+e});case 4949:if(121===Sa(e,t+6))return wa(e,":",":"+ca)+e;break;case 6444:switch(Sa(e,45===Sa(e,14)?18:11)){case 120:return wa(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+ca+(45===Sa(e,14)?"inline-":"")+"box$3$1"+ca+"$2$3$1"+la+"$2box$3")+e;case 100:return wa(e,":",":"+la)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return wa(e,"scroll-","scroll-snap-")+e}return e}function ei(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case pa:return void(e.return=Za(e.value,e.length,n));case ha:return Ga([Da(e,{value:wa(e.value,"@","@"+ca)})],r);case fa:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,function(t){switch(ba(t,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":za(Da(e,{props:[wa(t,/:(read-\w+)/,":-moz-$1")]})),za(Da(e,{props:[t]})),ya(e,{props:Ta(n,r)});break;case"::placeholder":za(Da(e,{props:[wa(t,/:(plac\w+)/,":"+ca+"input-$1")]})),za(Da(e,{props:[wa(t,/:(plac\w+)/,":-moz-$1")]})),za(Da(e,{props:[wa(t,/:(plac\w+)/,la+"input-$1")]})),za(Da(e,{props:[t]})),ya(e,{props:Ta(n,r)})}return""})}}function ti(e){return Va(ni("",null,null,null,[""],e=Ha(e),0,[0],e))}function ni(e,t,n,r,o,a,i,s,l){for(var u=0,c=0,d=i,f=0,p=0,h=0,m=1,g=1,y=1,v=0,b="",w=o,x=a,S=r,k=b;g;)switch(h=v,v=$a()){case 40:if(108!=h&&58==Sa(k,d-1)){-1!=xa(k+=wa(Wa(v),"&","&\f"),"&\f",ma(u?s[u-1]:0))&&(y=-1);break}case 34:case 39:case 91:k+=Wa(v);break;case 9:case 10:case 13:case 32:k+=qa(h);break;case 92:k+=Ka(Ma()-1,7);continue;case 47:switch(Ia()){case 42:case 47:_a(oi(Ya($a(),Ma()),t,n,l),l);break;default:k+="/"}break;case 123*m:s[u++]=Ea(k)*y;case 125*m:case 59:case 0:switch(v){case 0:case 125:g=0;case 59+c:-1==y&&(k=wa(k,/\f/g,"")),p>0&&Ea(k)-d&&_a(p>32?ai(k+";",r,n,d-1,l):ai(wa(k," ","")+";",r,n,d-2,l),l);break;case 59:k+=";";default:if(_a(S=ri(k,t,n,u,c,o,s,b,w=[],x=[],d,a),a),123===v)if(0===c)ni(k,t,S,S,w,a,d,s,x);else switch(99===f&&110===Sa(k,3)?100:f){case 100:case 108:case 109:case 115:ni(e,S,S,r&&_a(ri(e,S,S,0,0,o,s,b,o,w=[],d,x),x),o,x,d,s,r?w:x);break;default:ni(k,S,S,S,[""],x,0,s,x)}}u=c=p=0,m=y=1,b=k="",d=i;break;case 58:d=1+Ea(k),p=h;default:if(m<1)if(123==v)--m;else if(125==v&&0==m++&&125==Fa())continue;switch(k+=ga(v),v*m){case 38:y=c>0?1:(k+="\f",-1);break;case 44:s[u++]=(Ea(k)-1)*y,y=1;break;case 64:45===Ia()&&(k+=Wa($a())),f=Ia(),c=d=Ea(b=k+=Ja(Ma())),v++;break;case 45:45===h&&2==Ea(k)&&(m=0)}}return a}function ri(e,t,n,r,o,a,i,s,l,u,c,d){for(var f=o-1,p=0===o?a:[""],h=Ca(p),m=0,g=0,y=0;m<r;++m)for(var v=0,b=ka(e,f+1,f=ma(g=i[m])),w=e;v<h;++v)(w=va(g>0?p[v]+" "+b:wa(b,/&\f/g,p[v])))&&(l[y++]=w);return Aa(e,t,n,0===o?fa:s,l,u,c,d)}function oi(e,t,n,r){return Aa(e,t,n,da,ga(La),ka(e,2,-2),0,r)}function ai(e,t,n,r,o){return Aa(e,t,n,pa,ka(e,0,r),ka(e,r+1,-1),r,o)}var ii={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},si="undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_ATTR)||"data-styled",li="active",ui="data-styled-version",ci="6.1.19",di="/*!sc*/\n",fi="undefined"!=typeof window&&"undefined"!=typeof document,pi=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY)),hi=(new Set,Object.freeze([])),mi=Object.freeze({});function gi(e,t,n){return void 0===n&&(n=mi),e.theme!==n.theme&&e.theme||t||n.theme}var yi=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),vi=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,bi=/(^-|-$)/g;function wi(e){return e.replace(vi,"-").replace(bi,"")}var xi=/(a)(d)/gi,Si=function(e){return String.fromCharCode(e+(e>25?39:97))};function ki(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=Si(t%52)+n;return(Si(t%52)+n).replace(xi,"$1-$2")}var Ei,Ci=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},_i=function(e){return Ci(5381,e)};function Ti(e){return ki(_i(e)>>>0)}function Ri(e){return e.displayName||e.name||"Component"}function Oi(e){return"string"==typeof e&&!0}var Ni="function"==typeof Symbol&&Symbol.for,Pi=Ni?Symbol.for("react.memo"):60115,Li=Ni?Symbol.for("react.forward_ref"):60112,ji={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ai={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Di={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},zi=((Ei={})[Li]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Ei[Pi]=Di,Ei);function Fi(e){return("type"in(t=e)&&t.type.$$typeof)===Pi?Di:"$$typeof"in e?zi[e.$$typeof]:ji;var t}var $i=Object.defineProperty,Ii=Object.getOwnPropertyNames,Mi=Object.getOwnPropertySymbols,Ui=Object.getOwnPropertyDescriptor,Bi=Object.getPrototypeOf,Hi=Object.prototype;function Vi(e,t,n){if("string"!=typeof t){if(Hi){var r=Bi(t);r&&r!==Hi&&Vi(e,r,n)}var o=Ii(t);Mi&&(o=o.concat(Mi(t)));for(var a=Fi(e),i=Fi(t),s=0;s<o.length;++s){var l=o[s];if(!(l in Ai||n&&n[l]||i&&l in i||a&&l in a)){var u=Ui(t,l);try{$i(e,l,u)}catch(e){}}}}return e}function Wi(e){return"function"==typeof e}function qi(e){return"object"==typeof e&&"styledComponentId"in e}function Ki(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Qi(e,t){if(0===e.length)return"";for(var n=e[0],r=1;r<e.length;r++)n+=t?t+e[r]:e[r];return n}function Yi(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Ji(e,t,n){if(void 0===n&&(n=!1),!n&&!Yi(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=Ji(e[r],t[r]);else if(Yi(t))for(var r in t)e[r]=Ji(e[r],t[r]);return e}function Gi(e,t){Object.defineProperty(e,"toString",{value:t})}function Xi(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Zi=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,o=r;e>=o;)if((o<<=1)<0)throw Xi(16,"".concat(e));this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var a=r;a<o;a++)this.groupSizes[a]=0}for(var i=this.indexOfGroup(e+1),s=(a=0,t.length);a<s;a++)this.tag.insertRule(i,t[a])&&(this.groupSizes[e]++,i++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var o=n;o<r;o++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),o=r+n,a=r;a<o;a++)t+="".concat(this.tag.getRule(a)).concat(di);return t},e}(),es=new Map,ts=new Map,ns=1,rs=function(e){if(es.has(e))return es.get(e);for(;ts.has(ns);)ns++;var t=ns++;return es.set(e,t),ts.set(t,e),t},os=function(e,t){ns=t+1,es.set(e,t),ts.set(t,e)},as="style[".concat(si,"][").concat(ui,'="').concat(ci,'"]'),is=new RegExp("^".concat(si,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),ss=function(e,t,n){for(var r,o=n.split(","),a=0,i=o.length;a<i;a++)(r=o[a])&&e.registerName(t,r)},ls=function(e,t){for(var n,r=(null!==(n=t.textContent)&&void 0!==n?n:"").split(di),o=[],a=0,i=r.length;a<i;a++){var s=r[a].trim();if(s){var l=s.match(is);if(l){var u=0|parseInt(l[1],10),c=l[2];0!==u&&(os(c,u),ss(e,c,l[3]),e.getTag().insertRules(u,o)),o.length=0}else o.push(s)}}},us=function(e){for(var t=document.querySelectorAll(as),n=0,r=t.length;n<r;n++){var o=t[n];o&&o.getAttribute(si)!==li&&(ls(e,o),o.parentNode&&o.parentNode.removeChild(o))}};function cs(){return n.nc}var ds=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(e){var t=Array.from(e.querySelectorAll("style[".concat(si,"]")));return t[t.length-1]}(n),a=void 0!==o?o.nextSibling:null;r.setAttribute(si,li),r.setAttribute(ui,ci);var i=cs();return i&&r.setAttribute("nonce",i),n.insertBefore(r,a),r},fs=function(){function e(e){this.element=ds(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var o=t[n];if(o.ownerNode===e)return o}throw Xi(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),ps=function(){function e(e){this.element=ds(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),hs=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),ms=fi,gs={isServer:!fi,useCSSOMInjection:!pi},ys=function(){function e(e,t,n){void 0===e&&(e=mi),void 0===t&&(t={});var r=this;this.options=oa(oa({},gs),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&fi&&ms&&(ms=!1,us(this)),Gi(this,function(){return function(e){for(var t=e.getTag(),n=t.length,r="",o=function(n){var o=function(e){return ts.get(e)}(n);if(void 0===o)return"continue";var a=e.names.get(o),i=t.getGroup(n);if(void 0===a||!a.size||0===i.length)return"continue";var s="".concat(si,".g").concat(n,'[id="').concat(o,'"]'),l="";void 0!==a&&a.forEach(function(e){e.length>0&&(l+="".concat(e,","))}),r+="".concat(i).concat(s,'{content:"').concat(l,'"}').concat(di)},a=0;a<n;a++)o(a);return r}(r)})}return e.registerId=function(e){return rs(e)},e.prototype.rehydrate=function(){!this.server&&fi&&us(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(oa(oa({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new hs(n):t?new fs(n):new ps(n)}(this.options),new Zi(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(rs(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(rs(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(rs(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),vs=/&/g,bs=/^\s*\/\/.*$/gm;function ws(e,t){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map(function(e){return"".concat(t," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=ws(e.children,t)),e})}function xs(e){var t,n,r,o=void 0===e?mi:e,a=o.options,i=void 0===a?mi:a,s=o.plugins,l=void 0===s?hi:s,u=function(e,r,o){return o.startsWith(n)&&o.endsWith(n)&&o.replaceAll(n,"").length>0?".".concat(t):e},c=l.slice();c.push(function(e){e.type===fa&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(vs,n).replace(r,u))}),i.prefix&&c.push(ei),c.push(Xa);var d=function(e,o,a,s){void 0===o&&(o=""),void 0===a&&(a=""),void 0===s&&(s="&"),t=s,n=o,r=new RegExp("\\".concat(n,"\\b"),"g");var l=e.replace(bs,""),u=ti(a||o?"".concat(a," ").concat(o," { ").concat(l," }"):l);i.namespace&&(u=ws(u,i.namespace));var d,f=[];return Ga(u,function(e){var t=Ca(e);return function(n,r,o,a){for(var i="",s=0;s<t;s++)i+=e[s](n,r,o,a)||"";return i}}(c.concat((d=function(e){return f.push(e)},function(e){e.root||(e=e.return)&&d(e)})))),f};return d.hash=l.length?l.reduce(function(e,t){return t.name||Xi(15),Ci(e,t.name)},5381).toString():"",d}var Ss=new ys,ks=xs(),Es=a.createContext({shouldForwardProp:void 0,styleSheet:Ss,stylis:ks}),Cs=(Es.Consumer,a.createContext(void 0));function _s(){return(0,a.useContext)(Es)}function Ts(e){var t=(0,a.useState)(e.stylisPlugins),n=t[0],r=t[1],o=_s().styleSheet,i=(0,a.useMemo)(function(){var t=o;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t},[e.disableCSSOMInjection,e.sheet,e.target,o]),s=(0,a.useMemo)(function(){return xs({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:n})},[e.enableVendorPrefixes,e.namespace,n]);(0,a.useEffect)(function(){sa()(n,e.stylisPlugins)||r(e.stylisPlugins)},[e.stylisPlugins]);var l=(0,a.useMemo)(function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:i,stylis:s}},[e.shouldForwardProp,i,s]);return a.createElement(Es.Provider,{value:l},a.createElement(Cs.Provider,{value:s},e.children))}var Rs=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=ks);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Gi(this,function(){throw Xi(12,String(n.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=ks),this.name+e.hash},e}(),Os=function(e){return e>="A"&&e<="Z"};function Ns(e){for(var t="",n=0;n<e.length;n++){var r=e[n];if(1===n&&"-"===r&&"-"===e[0])return e;Os(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}var Ps=function(e){return null==e||!1===e||""===e},Ls=function(e){var t,n,r=[];for(var o in e){var a=e[o];e.hasOwnProperty(o)&&!Ps(a)&&(Array.isArray(a)&&a.isCss||Wi(a)?r.push("".concat(Ns(o),":"),a,";"):Yi(a)?r.push.apply(r,aa(aa(["".concat(o," {")],Ls(a),!1),["}"],!1)):r.push("".concat(Ns(o),": ").concat((t=o,null==(n=a)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in ii||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return r};function js(e,t,n,r){return Ps(e)?[]:qi(e)?[".".concat(e.styledComponentId)]:Wi(e)?!Wi(o=e)||o.prototype&&o.prototype.isReactComponent||!t?[e]:js(e(t),t,n,r):e instanceof Rs?n?(e.inject(n,r),[e.getName(r)]):[e]:Yi(e)?Ls(e):Array.isArray(e)?Array.prototype.concat.apply(hi,e.map(function(e){return js(e,t,n,r)})):[e.toString()];var o}function As(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Wi(n)&&!qi(n))return!1}return!0}var Ds=_i(ci),zs=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&As(e),this.componentId=t,this.baseHash=Ci(Ds,t),this.baseStyle=n,ys.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))r=Ki(r,this.staticRulesId);else{var o=Qi(js(this.rules,e,t,n)),a=ki(Ci(this.baseHash,o)>>>0);if(!t.hasNameForId(this.componentId,a)){var i=n(o,".".concat(a),void 0,this.componentId);t.insertRules(this.componentId,a,i)}r=Ki(r,a),this.staticRulesId=a}else{for(var s=Ci(this.baseHash,n.hash),l="",u=0;u<this.rules.length;u++){var c=this.rules[u];if("string"==typeof c)l+=c;else if(c){var d=Qi(js(c,e,t,n));s=Ci(s,d+u),l+=d}}if(l){var f=ki(s>>>0);t.hasNameForId(this.componentId,f)||t.insertRules(this.componentId,f,n(l,".".concat(f),void 0,this.componentId)),r=Ki(r,f)}}return r},e}(),Fs=a.createContext(void 0);Fs.Consumer;var $s={};new Set;function Is(e,t,n){var r=qi(e),o=e,i=!Oi(e),s=t.attrs,l=void 0===s?hi:s,u=t.componentId,c=void 0===u?function(e,t){var n="string"!=typeof e?"sc":wi(e);$s[n]=($s[n]||0)+1;var r="".concat(n,"-").concat(Ti(ci+n+$s[n]));return t?"".concat(t,"-").concat(r):r}(t.displayName,t.parentComponentId):u,d=t.displayName,f=void 0===d?function(e){return Oi(e)?"styled.".concat(e):"Styled(".concat(Ri(e),")")}(e):d,p=t.displayName&&t.componentId?"".concat(wi(t.displayName),"-").concat(t.componentId):t.componentId||c,h=r&&o.attrs?o.attrs.concat(l).filter(Boolean):l,m=t.shouldForwardProp;if(r&&o.shouldForwardProp){var g=o.shouldForwardProp;if(t.shouldForwardProp){var y=t.shouldForwardProp;m=function(e,t){return g(e,t)&&y(e,t)}}else m=g}var v=new zs(n,p,r?o.componentStyle:void 0);function b(e,t){return function(e,t,n){var r=e.attrs,o=e.componentStyle,i=e.defaultProps,s=e.foldedComponentIds,l=e.styledComponentId,u=e.target,c=a.useContext(Fs),d=_s(),f=e.shouldForwardProp||d.shouldForwardProp,p=gi(t,c,i)||mi,h=function(e,t,n){for(var r,o=oa(oa({},t),{className:void 0,theme:n}),a=0;a<e.length;a+=1){var i=Wi(r=e[a])?r(o):r;for(var s in i)o[s]="className"===s?Ki(o[s],i[s]):"style"===s?oa(oa({},o[s]),i[s]):i[s]}return t.className&&(o.className=Ki(o.className,t.className)),o}(r,t,p),m=h.as||u,g={};for(var y in h)void 0===h[y]||"$"===y[0]||"as"===y||"theme"===y&&h.theme===p||("forwardedAs"===y?g.as=h.forwardedAs:f&&!f(y,m)||(g[y]=h[y]));var v=function(e,t){var n=_s();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(o,h),b=Ki(s,l);return v&&(b+=" "+v),h.className&&(b+=" "+h.className),g[Oi(m)&&!yi.has(m)?"class":"className"]=b,n&&(g.ref=n),(0,a.createElement)(m,g)}(w,e,t)}b.displayName=f;var w=a.forwardRef(b);return w.attrs=h,w.componentStyle=v,w.displayName=f,w.shouldForwardProp=m,w.foldedComponentIds=r?Ki(o.foldedComponentIds,o.styledComponentId):"",w.styledComponentId=p,w.target=r?o.target:e,Object.defineProperty(w,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=r?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,o=t;r<o.length;r++)Ji(e,o[r],!0);return e}({},o.defaultProps,e):e}}),Gi(w,function(){return".".concat(w.styledComponentId)}),i&&Vi(w,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),w}function Ms(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n}var Us=function(e){return Object.assign(e,{isCss:!0})};function Bs(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Wi(e)||Yi(e))return Us(js(Ms(hi,aa([e],t,!0))));var r=e;return 0===t.length&&1===r.length&&"string"==typeof r[0]?js(r):Us(js(Ms(r,t)))}function Hs(e,t,n){if(void 0===n&&(n=mi),!t)throw Xi(1,t);var r=function(r){for(var o=[],a=1;a<arguments.length;a++)o[a-1]=arguments[a];return e(t,n,Bs.apply(void 0,aa([r],o,!1)))};return r.attrs=function(r){return Hs(e,t,oa(oa({},n),{attrs:Array.prototype.concat(n.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return Hs(e,t,oa(oa({},n),r))},r}var Vs=function(e){return Hs(Is,e)},Ws=Vs;yi.forEach(function(e){Ws[e]=Vs(e)});!function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=As(e),ys.registerId(this.componentId+1)}e.prototype.createStyles=function(e,t,n,r){var o=r(Qi(js(this.rules,t,n,r)),""),a=this.componentId+e;n.insertRules(a,a,o)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,n,r){e>2&&ys.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}();function qs(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Qi(Bs.apply(void 0,aa([e],t,!1))),o=Ti(r);return new Rs(o,r)}(function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=cs(),r=Qi([n&&'nonce="'.concat(n,'"'),"".concat(si,'="true"'),"".concat(ui,'="').concat(ci,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(t,"</style>")},this.getStyleTags=function(){if(e.sealed)throw Xi(2);return e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)throw Xi(2);var n=e.instance.toString();if(!n)return[];var r=((t={})[si]="",t[ui]=ci,t.dangerouslySetInnerHTML={__html:n},t),o=cs();return o&&(r.nonce=o),[a.createElement("style",oa({},r,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new ys({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw Xi(2);return a.createElement(Ts,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw Xi(3)}})(),"__sc-".concat(si,"__");const Ks=Ws.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,Qs=Ws.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
  margin: 20px;
`,Ys=Ws.div`
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin: 0 auto 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  font-weight: bold;
`,Js=Ws.h1`
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
`,Gs=Ws.p`
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 40px;
`,Xs=Ws.div`
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 30px;
`,Zs=Ws.button`
  padding: 10px 20px;
  border: 2px solid ${e=>e.active?"#667eea":"#ddd"};
  background: ${e=>e.active?"#667eea":"white"};
  color: ${e=>e.active?"white":"#333"};
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    background: ${e=>e.active?"#5a6fd8":"#f8f9ff"};
  }
`,el=Ws.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  font-size: 1.3rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`,tl=Ws.button`
  background: none;
  border: none;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  margin-top: 20px;
  text-decoration: underline;

  &:hover {
    color: #333;
  }
`,nl=()=>{const{t:e,i18n:t}=ra(),n=re(),[o,a]=(0,r.useState)(t.language),i="ar"===o,s=e=>{a(e),t.changeLanguage(e),localStorage.setItem("language",e)};return(0,Yr.jsx)(Ks,{isRTL:i,children:(0,Yr.jsxs)(Qs,{children:[(0,Yr.jsx)(Ys,{children:"\ud83d\udcfa"}),(0,Yr.jsx)(Js,{children:e("companyName")}),(0,Yr.jsx)(Gs,{children:e("welcome")}),(0,Yr.jsxs)("div",{children:[(0,Yr.jsx)("p",{style:{marginBottom:"15px",color:"#666",fontSize:"1rem"},children:e("selectLanguage")}),(0,Yr.jsxs)(Xs,{children:[(0,Yr.jsx)(Zs,{active:"ar"===o,onClick:()=>s("ar"),children:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"}),(0,Yr.jsx)(Zs,{active:"en"===o,onClick:()=>s("en"),children:"English"})]})]}),(0,Yr.jsx)(el,{onClick:()=>{n("/select-display")},children:e("startTransaction")}),(0,Yr.jsx)(tl,{onClick:()=>{n("/owner-login")},children:"ar"===o?"\u062f\u062e\u0648\u0644 \u0627\u0644\u0645\u0627\u0644\u0643":"Owner Login"})]})})},rl=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,ol=Ws.div`
  text-align: center;
  color: white;
  margin-bottom: 30px;
`,al=Ws.h1`
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
`,il=Ws.p`
  font-size: 1.2rem;
  opacity: 0.9;
`,sl=Ws.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
`,ll=Ws.div`
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  cursor: ${e=>e.available?"pointer":"not-allowed"};
  opacity: ${e=>e.available?1:.6};
  transition: all 0.3s ease;
  border: 3px solid ${e=>{switch(e.status){case"available":return"#28a745";case"occupied":return"#dc3545";case"reserved":return"#ffc107";case"maintenance":return"#6c757d";default:return"#ddd"}}};

  &:hover {
    transform: ${e=>e.available?"translateY(-5px)":"none"};
    box-shadow: ${e=>e.available?"0 15px 40px rgba(0, 0, 0, 0.15)":"0 10px 30px rgba(0, 0, 0, 0.1)"};
  }
`,ul=Ws.div`
  font-size: 3rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 15px;
`,cl=Ws.h3`
  font-size: 1.3rem;
  color: #333;
  text-align: center;
  margin-bottom: 15px;
`,dl=Ws.div`
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 15px;
  width: 100%;
  color: white;
  background: ${e=>{switch(e.status){case"available":return"#28a745";case"occupied":return"#dc3545";case"reserved":return"#ffc107";case"maintenance":return"#6c757d";default:return"#ddd"}}};
`,fl=Ws.div`
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  margin-top: 15px;
`,pl=Ws.div`
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
`,hl=Ws.div`
  color: #dc3545;
  font-weight: 600;
  font-size: 1.1rem;
`,ml=Ws.button`
  position: fixed;
  top: 20px;
  ${e=>e.isRTL?"right: 20px":"left: 20px"};
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: white;
    color: #667eea;
  }
`,gl=Ws.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: white;
  font-size: 1.2rem;
`,yl=Ws.div`
  background: #dc3545;
  color: white;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  margin: 20px auto;
  max-width: 500px;
`,vl=()=>{const{t:e,i18n:t}=ra(),n=re(),[o,a]=(0,r.useState)([]),[i,s]=(0,r.useState)(!0),[l,u]=(0,r.useState)(null),c="ar"===t.language;(0,r.useEffect)(()=>{d();const e=setInterval(d,3e4);return()=>clearInterval(e)},[]);const d=async()=>{try{const e=await Ar();e.success?(a(e.data),u(null)):u(e.message||"\u0641\u0634\u0644 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0634\u0627\u0634\u0627\u062a")}catch(l){u("\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629"),console.error("Error fetching displays:",l)}finally{s(!1)}},f=t=>{switch(t){case"available":return e("available");case"occupied":return e("occupied");case"reserved":return e("reserved");case"maintenance":return e("maintenance");default:return t}};return i?(0,Yr.jsx)(rl,{isRTL:c,children:(0,Yr.jsx)(gl,{children:c?"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0645\u064a\u0644...":"Loading..."})}):(0,Yr.jsxs)(rl,{isRTL:c,children:[(0,Yr.jsx)(ml,{isRTL:c,onClick:()=>{n("/")},children:e("back")}),(0,Yr.jsxs)(ol,{children:[(0,Yr.jsx)(al,{children:e("selectDisplay")}),(0,Yr.jsx)(il,{children:c?"\u0627\u062e\u062a\u0631 \u0627\u0644\u0634\u0627\u0634\u0629 \u0627\u0644\u062a\u064a \u062a\u0631\u064a\u062f \u0639\u0631\u0636 \u0627\u0633\u0645\u0643 \u0639\u0644\u064a\u0647\u0627":"Choose the display to show your name"})]}),l&&(0,Yr.jsx)(yl,{children:l}),(0,Yr.jsx)(sl,{children:o.map(t=>(0,Yr.jsxs)(ll,{available:"available"===t.status,status:t.status,onClick:()=>(e=>{"available"===e.status&&n("/login",{state:{selectedDisplay:e}})})(t),children:[(0,Yr.jsx)(ul,{children:t.displayNumber}),(0,Yr.jsx)(cl,{children:t.name}),(0,Yr.jsx)(dl,{status:t.status,children:f(t.status)}),"occupied"===t.status&&t.customerName&&(0,Yr.jsxs)(fl,{children:[(0,Yr.jsxs)(pl,{children:[c?"\u0627\u0644\u0639\u0645\u064a\u0644: ":"Customer: ",t.customerName]}),t.timeRemaining&&!t.timeRemaining.expired&&(0,Yr.jsx)(hl,{children:e("timeRemaining",{time:Br(t.timeRemaining.remaining)})})]}),"reserved"===t.status&&t.customerName&&(0,Yr.jsx)(fl,{children:(0,Yr.jsxs)(pl,{children:[c?"\u0645\u062d\u062c\u0648\u0632 \u0644\u0640: ":"Reserved for: ",t.customerName]})})]},t.id))})]})},bl=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,wl=Ws.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
`,xl=Ws.div`
  text-align: center;
  margin-bottom: 30px;
`,Sl=Ws.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
`,kl=Ws.div`
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  text-align: center;
`,El=Ws.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`,Cl=Ws.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,_l=Ws.label`
  font-weight: 600;
  color: #333;
  font-size: 1rem;
`,Tl=Ws.input`
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  direction: ${e=>"tel"===e.type?"ltr":"inherit"};
  text-align: ${e=>"tel"===e.type?"left":"inherit"};

  &:focus {
    outline: none;
    border-color: #667eea;
  }

  &:invalid {
    border-color: #dc3545;
  }
`,Rl=Ws.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${e=>e.disabled?.6:1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`,Ol=Ws.button`
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #667eea;
    color: white;
  }
`,Nl=Ws.div`
  background: #dc3545;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`,Pl=Ws.div`
  background: #28a745;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`,Ll=Ws.div`
  background: #e3f2fd;
  color: #1976d2;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
  font-size: 0.9rem;
`,jl=()=>{var e;const{t:t,i18n:n}=ra(),o=re(),a=ee(),{login:i}=Xr(),{loading:s,error:l,otpSent:u,sendOTP:c,verifyOTP:d,resetOTP:f}=(()=>{const[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(null),[a,i]=(0,r.useState)(!1);return{loading:e,error:n,otpSent:a,sendOTP:async function(e,r){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ar";t(!0),o(null);try{const t=await Nr(e,r,a);return t.success?(i(!0),t):(o(t.message||"\u0641\u0634\u0644 \u0641\u064a \u0625\u0631\u0633\u0627\u0644 \u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642"),null)}catch(n){return o(n.message||"\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629"),null}finally{t(!1)}},verifyOTP:async(e,r)=>{t(!0),o(null);try{const t=await Pr(e,r);return t.success?t:(o(t.message||"\u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642 \u063a\u064a\u0631 \u0635\u062d\u064a\u062d"),null)}catch(n){return o(n.message||"\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u062a\u062d\u0642\u0642 \u0645\u0646 \u0627\u0644\u0631\u0645\u0632"),null}finally{t(!1)}},resetOTP:()=>{i(!1),o(null)}}})(),[p,h]=(0,r.useState)("phone"),[m,g]=(0,r.useState)({phoneNumber:"",email:"",otpCode:""}),[y,v]=(0,r.useState)({}),b="ar"===n.language,w=null===(e=a.state)||void 0===e?void 0:e.selectedDisplay,x=e=>{const{name:t,value:n}=e.target;g(e=>({...e,[t]:n})),y[t]&&v(e=>({...e,[t]:""}))},S=()=>{const e={};var n;return"phone"===p?(m.phoneNumber?(e=>{const t=e.replace(/[\s\-\(\)]/g,"");return/^(\+966|966|0)?[5][0-9]{8}$/.test(t)})(m.phoneNumber)||(e.phoneNumber=t("invalidPhoneNumber")):e.phoneNumber=t("enterPhoneNumber"),m.email?(n=m.email,/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n)||(e.email=t("invalidEmail"))):e.email=t("enterEmail")):"otp"===p&&(m.otpCode?6!==m.otpCode.length&&(e.otpCode=t("invalidOTP")):e.otpCode=t("enterOTP")),v(e),0===Object.keys(e).length};return(0,Yr.jsx)(bl,{isRTL:b,children:(0,Yr.jsxs)(wl,{children:[(0,Yr.jsxs)(xl,{children:[(0,Yr.jsx)(Sl,{children:"phone"===p?b?"\u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u062f\u062e\u0648\u0644":"Login":b?"\u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642":"Verification Code"}),w&&(0,Yr.jsxs)(kl,{children:[(0,Yr.jsx)("strong",{children:t("displayNumber",{number:w.displayNumber})}),(0,Yr.jsx)("br",{}),w.name]})]}),l&&(0,Yr.jsx)(Nl,{children:l}),u&&"otp"===p&&(0,Yr.jsx)(Pl,{children:t("otpSent")}),"phone"===p?(0,Yr.jsxs)(El,{onSubmit:async e=>{if(e.preventDefault(),!S())return;const t=Ur(m.phoneNumber);await c(t,m.email,n.language)&&h("otp")},children:[(0,Yr.jsxs)(Cl,{children:[(0,Yr.jsx)(_l,{children:t("phoneNumber")}),(0,Yr.jsx)(Tl,{type:"tel",name:"phoneNumber",value:m.phoneNumber,onChange:x,placeholder:t("enterPhoneNumber"),required:!0}),y.phoneNumber&&(0,Yr.jsx)(Nl,{children:y.phoneNumber})]}),(0,Yr.jsxs)(Cl,{children:[(0,Yr.jsx)(_l,{children:t("email")}),(0,Yr.jsx)(Tl,{type:"email",name:"email",value:m.email,onChange:x,placeholder:t("enterEmail"),required:!0}),y.email&&(0,Yr.jsx)(Nl,{children:y.email})]}),(0,Yr.jsx)(Rl,{type:"submit",disabled:s,children:t(s?"processing":"sendOTP")})]}):(0,Yr.jsxs)(El,{onSubmit:async e=>{if(e.preventDefault(),!S())return;const t=Ur(m.phoneNumber),n=await d(t,m.otpCode);n&&(i(n.data.customer,n.data.token),o("/enter-name",{state:{selectedDisplay:w,customer:n.data.customer}}))},children:[(0,Yr.jsx)(Ll,{children:b?`\u062a\u0645 \u0625\u0631\u0633\u0627\u0644 \u0631\u0645\u0632 \u0627\u0644\u062a\u062d\u0642\u0642 \u0625\u0644\u0649: ${m.email}`:`Verification code sent to: ${m.email}`}),(0,Yr.jsxs)(Cl,{children:[(0,Yr.jsx)(_l,{children:t("otpCode")}),(0,Yr.jsx)(Tl,{type:"text",name:"otpCode",value:m.otpCode,onChange:x,placeholder:t("enterOTP"),maxLength:"6",required:!0}),y.otpCode&&(0,Yr.jsx)(Nl,{children:y.otpCode})]}),(0,Yr.jsx)(Rl,{type:"submit",disabled:s,children:t(s?"processing":"verifyOTP")}),(0,Yr.jsx)(Rl,{type:"button",onClick:async()=>{const e=Ur(m.phoneNumber);await c(e,m.email,n.language)},disabled:s,style:{background:"#6c757d"},children:b?"\u0625\u0639\u0627\u062f\u0629 \u0625\u0631\u0633\u0627\u0644 \u0627\u0644\u0631\u0645\u0632":"Resend Code"})]}),(0,Yr.jsx)("div",{style:{marginTop:"20px",textAlign:"center"},children:(0,Yr.jsx)(Ol,{onClick:()=>{"otp"===p?(h("phone"),f()):o("/select-display")},children:t("back")})})]})})},Al=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,Dl=Ws.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
`,zl=Ws.div`
  text-align: center;
  margin-bottom: 30px;
`,Fl=Ws.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
`,$l=Ws.div`
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 30px;
  text-align: center;
`,Il=Ws.div`
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10px;
`,Ml=Ws.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 30px;
`,Ul=Ws.div`
  background: #e3f2fd;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
`,Bl=Ws.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
`,Hl=Ws.div`
  font-size: 1.3rem;
  font-weight: bold;
  color: #1976d2;
`,Vl=Ws.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`,Wl=Ws.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,ql=Ws.label`
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
`,Kl=Ws.input`
  padding: 20px;
  border: 3px solid #ddd;
  border-radius: 15px;
  font-size: 1.3rem;
  text-align: center;
  transition: border-color 0.3s ease;
  font-weight: 600;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`,Ql=Ws.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  gap: 8px;
  margin-top: 20px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
`,Yl=Ws.button`
  padding: 12px 8px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f8f9fa;
    border-color: #667eea;
  }

  &:active {
    background: #667eea;
    color: white;
  }
`,Jl=Ws.button`
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 10px;
  align-self: center;

  &:hover {
    background: #5a6268;
  }
`,Gl=Ws.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${e=>e.disabled?.6:1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`,Xl=Ws.button`
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #667eea;
    color: white;
  }
`,Zl=Ws.div`
  background: #dc3545;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`,eu=()=>{var e,t;const{t:n,i18n:o}=ra(),a=re(),i=ee(),[s,l]=(0,r.useState)(""),[u,c]=(0,r.useState)("ar"),[d,f]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),[m,g]=(0,r.useState)({displayPrice:50,displayDuration:300}),y="ar"===o.language,v=null===(e=i.state)||void 0===e?void 0:e.selectedDisplay,b=null===(t=i.state)||void 0===t?void 0:t.customer;(0,r.useEffect)(()=>{const e=Kr("appSettings");e&&g({displayPrice:e.display_price||50,displayDuration:e.display_duration||300}),v&&b||a("/select-display")},[v,b,a]);return v&&b?(0,Yr.jsx)(Al,{isRTL:y,children:(0,Yr.jsxs)(Dl,{children:[(0,Yr.jsx)(zl,{children:(0,Yr.jsx)(Fl,{children:n("enterName")})}),(0,Yr.jsxs)($l,{children:[(0,Yr.jsx)(Il,{children:n("displayNumber",{number:v.displayNumber})}),(0,Yr.jsx)("div",{children:v.name})]}),(0,Yr.jsxs)(Ml,{children:[(0,Yr.jsxs)(Ul,{children:[(0,Yr.jsx)(Bl,{children:n("displayPrice",{price:""})}),(0,Yr.jsxs)(Hl,{children:[m.displayPrice," ",y?"\u0631\u064a\u0627\u0644":"SAR"]})]}),(0,Yr.jsxs)(Ul,{children:[(0,Yr.jsx)(Bl,{children:n("displayDuration",{duration:""})}),(0,Yr.jsxs)(Hl,{children:[Math.floor(m.displayDuration/60)," ",n("minutes")]})]})]}),p&&(0,Yr.jsx)(Zl,{children:p}),(0,Yr.jsxs)(Vl,{onSubmit:async e=>{e.preventDefault();const t=s.trim();if(t)if(t.length<2)h(y?"\u0627\u0644\u0627\u0633\u0645 \u064a\u062c\u0628 \u0623\u0646 \u064a\u0643\u0648\u0646 \u062d\u0631\u0641\u064a\u0646 \u0639\u0644\u0649 \u0627\u0644\u0623\u0642\u0644":"Name must be at least 2 characters");else{f(!0),h(null);try{const e=await zr(v.id,Wr(t));e.success?a("/payment",{state:{selectedDisplay:v,customer:b,customerName:Wr(t),amount:m.displayPrice,duration:m.displayDuration}}):h(e.message||"\u0641\u0634\u0644 \u0641\u064a \u062d\u062c\u0632 \u0627\u0644\u0634\u0627\u0634\u0629")}catch(p){h("\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629"),console.error("Error reserving display:",p)}finally{f(!1)}}else h(n("enterName"))},children:[(0,Yr.jsxs)(Wl,{children:[(0,Yr.jsx)(ql,{children:n("customerName")}),(0,Yr.jsx)(Kl,{type:"text",value:s,onChange:e=>l(e.target.value),placeholder:n("enterName"),maxLength:"50"})]}),(0,Yr.jsx)(Jl,{type:"button",onClick:()=>c(e=>"ar"===e?"en":"ar"),children:"ar"===u?"English":"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"}),(0,Yr.jsx)(Ql,{children:("ar"===u?[["\u0636","\u0635","\u062b","\u0642","\u0641","\u063a","\u0639","\u0647","\u062e","\u062d","\u062c","\u062f"],["\u0634","\u0633","\u064a","\u0628","\u0644","\u0627","\u062a","\u0646","\u0645","\u0643","\u0637"],["\u0626","\u0621","\u0624","\u0631","\u0644\u0627","\u0649","\u0629","\u0648","\u0632","\u0638"],["\u0645\u0633\u0627\u0641\u0629","\u062d\u0630\u0641"]]:[["Q","W","E","R","T","Y","U","I","O","P"],["A","S","D","F","G","H","J","K","L"],["Z","X","C","V","B","N","M"],["Space","Delete"]]).flat().map((e,t)=>(0,Yr.jsx)(Yl,{type:"button",onClick:()=>(e=>{l("\u0645\u0633\u0627\u0641\u0629"===e||"Space"===e?e=>e+" ":"\u062d\u0630\u0641"===e||"Delete"===e?e=>e.slice(0,-1):t=>t+e)})(e),style:{gridColumn:"\u0645\u0633\u0627\u0641\u0629"===e||"Space"===e?"span 6":"\u062d\u0630\u0641"===e||"Delete"===e?"span 3":"span 1"},children:e},t))}),(0,Yr.jsx)(Gl,{type:"submit",disabled:d||!s.trim(),children:n(d?"processing":"proceedToPayment")})]}),(0,Yr.jsx)("div",{style:{marginTop:"20px",textAlign:"center"},children:(0,Yr.jsx)(Xl,{onClick:()=>{a("/select-display")},children:n("back")})})]})}):null},tu=qs`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`,nu=qs`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`,ru=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,ou=Ws.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  text-align: center;
`,au=Ws.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 30px;
`,iu=Ws.div`
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
`,su=Ws.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 1.1rem;

  &:last-child {
    margin-bottom: 0;
    padding-top: 15px;
    border-top: 2px solid #ddd;
    font-weight: bold;
    font-size: 1.3rem;
    color: #667eea;
  }
`,lu=Ws.div`
  display: grid;
  gap: 15px;
  margin-bottom: 30px;
`,uu=Ws.button`
  background: ${e=>e.selected?"#667eea":"white"};
  color: ${e=>e.selected?"white":"#333"};
  border: 3px solid ${e=>e.selected?"#667eea":"#ddd"};
  padding: 20px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;

  &:hover {
    border-color: #667eea;
    background: ${e=>e.selected?"#5a6fd8":"#f8f9ff"};
  }
`,cu=Ws.div`
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 40px;
  border-radius: 20px;
  margin: 20px 0;
  animation: ${e=>e.active?tu:"none"} 2s infinite;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: ${nu} 3s linear infinite;
  }
`,du=Ws.div`
  font-size: 4rem;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
`,fu=Ws.div`
  font-size: 1.3rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
`,pu=Ws.div`
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #667eea;
  border-radius: 50%;
  animation: ${nu} 1s linear infinite;
  margin: 20px auto;
`,hu=Ws.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px 40px;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${e=>e.disabled?.6:1};
  margin: 0 10px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`,mu=Ws(hu)`
  background: #dc3545;
  
  &:hover:not(:disabled) {
    background: #c82333;
    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
  }
`,gu=Ws.div`
  background: #28a745;
  color: white;
  padding: 20px;
  border-radius: 15px;
  margin: 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
`,yu=Ws.div`
  background: #dc3545;
  color: white;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
`,vu=()=>{const{t:e,i18n:t}=ra(),n=re(),o=ee(),[a,i]=(0,r.useState)("nfc"),[s,l]=(0,r.useState)("idle"),[u,c]=(0,r.useState)(null),[d,f]=(0,r.useState)(null),p="ar"===t.language,{selectedDisplay:h,customer:m,customerName:g,amount:y,duration:v}=o.state||{};(0,r.useEffect)(()=>{h&&m&&g?b():n("/select-display")},[h,m,g,y,v,n]);const b=async()=>{try{const e=await Fr(h.id,g,y,v);e.success?c(e.data):f(e.message||"\u0641\u0634\u0644 \u0641\u064a \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0629")}catch(d){f("\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629"),console.error("Error creating transaction:",d)}},w=()=>{n("/enter-name",{state:{selectedDisplay:h,customer:m}})};return h&&m&&g?(0,Yr.jsx)(ru,{isRTL:p,children:(0,Yr.jsxs)(ou,{children:[(0,Yr.jsx)(au,{children:e("payment")}),(0,Yr.jsxs)(iu,{children:[(0,Yr.jsxs)(su,{children:[(0,Yr.jsxs)("span",{children:[e("customerName"),":"]}),(0,Yr.jsx)("span",{children:g})]}),(0,Yr.jsxs)(su,{children:[(0,Yr.jsxs)("span",{children:[e("displayNumber",{number:h.displayNumber}),":"]}),(0,Yr.jsx)("span",{children:h.name})]}),(0,Yr.jsxs)(su,{children:[(0,Yr.jsxs)("span",{children:[e("duration"),":"]}),(0,Yr.jsxs)("span",{children:[Math.floor(v/60)," ",e("minutes")]})]}),(0,Yr.jsxs)(su,{children:[(0,Yr.jsxs)("span",{children:[e("amount"),":"]}),(0,Yr.jsx)("span",{children:Hr(y,"SAR",p?"ar-SA":"en-US")})]})]}),d&&(0,Yr.jsx)(yu,{children:d}),"success"===s&&(0,Yr.jsxs)(gu,{children:[e("paymentSuccessful"),(0,Yr.jsx)("br",{}),p?"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0648\u064a\u0644...":"Redirecting..."]}),"idle"===s&&(0,Yr.jsxs)(Yr.Fragment,{children:[(0,Yr.jsx)("div",{style:{marginBottom:"20px"},children:(0,Yr.jsx)("h3",{children:e("paymentMethod")})}),(0,Yr.jsx)(lu,{children:(0,Yr.jsxs)(uu,{selected:"nfc"===a,onClick:()=>i("nfc"),children:["\ud83d\udcf1 ",e("nfcPayment")]})}),"nfc"===a&&(0,Yr.jsxs)(cu,{active:"processing"===s,children:[(0,Yr.jsx)(du,{children:"\ud83d\udce1"}),(0,Yr.jsx)(fu,{children:e("processing"===s?"processing":"placeCardOnReader")}),"processing"===s&&(0,Yr.jsx)(pu,{})]}),(0,Yr.jsxs)("div",{style:{marginTop:"30px"},children:[(0,Yr.jsx)(hu,{onClick:async()=>{if(u){l("processing"),f(null);try{const e=await Ir(u.transactionId);if(e.success){const t=await $r(u.transactionId,e.data.paymentIntentId);t.success?(l("success"),setTimeout(()=>{n("/success",{state:{transaction:t.data,customerName:g,selectedDisplay:h}})},3e3)):(l("failed"),f(t.message||"\u0641\u0634\u0644 \u0641\u064a \u062a\u0623\u0643\u064a\u062f \u0627\u0644\u0645\u0639\u0627\u0645\u0644\u0629"))}else l("failed"),f(e.message||"\u0641\u0634\u0644 \u0641\u064a \u0627\u0644\u062f\u0641\u0639")}catch(d){l("failed"),f("\u062e\u0637\u0623 \u0641\u064a \u0645\u0639\u0627\u0644\u062c\u0629 \u0627\u0644\u062f\u0641\u0639"),console.error("Payment error:",d)}}},disabled:"processing"===s||!u,children:e("processing"===s?"processing":"confirm")}),(0,Yr.jsx)(mu,{onClick:w,disabled:"processing"===s,children:e("cancel")})]})]}),"failed"===s&&(0,Yr.jsxs)("div",{style:{marginTop:"20px"},children:[(0,Yr.jsx)(hu,{onClick:()=>l("idle"),children:e("retry")}),(0,Yr.jsx)(mu,{onClick:w,children:e("cancel")})]})]})}):null},bu=qs`
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
`,wu=qs`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`,xu=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,Su=Ws.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  text-align: center;
  animation: ${wu} 0.6s ease-out;
`,ku=Ws.div`
  width: 120px;
  height: 120px;
  margin: 0 auto 30px;
  position: relative;
`,Eu=Ws.svg`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #28a745;
  stroke-miterlimit: 10;
  box-shadow: inset 0px 0px 0px #28a745;
  animation: ${bu} 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
`,Cu=Ws.path`
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
`,_u=Ws.h1`
  font-size: 2.5rem;
  color: #28a745;
  margin-bottom: 20px;
  font-weight: 700;
`,Tu=Ws.p`
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 30px;
`,Ru=Ws.div`
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
  text-align: ${e=>e.isRTL?"right":"left"};
`,Ou=Ws.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;

  &:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
`,Nu=Ws.span`
  font-weight: 600;
  color: #333;
`,Pu=Ws.span`
  color: #666;
  font-weight: 500;
`,Lu=Ws.div`
  background: #e3f2fd;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background: #bbdefb;
  }
`,ju=Ws.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
`,Au=Ws.div`
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
`,Du=Ws.div`
  font-size: 1rem;
  opacity: 0.9;
`,zu=Ws.div`
  background: #fff3cd;
  color: #856404;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
`,Fu=Ws.div`
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
`,$u=Ws.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }
`,Iu=Ws($u)`
  background: #6c757d;
  
  &:hover {
    background: #5a6268;
    box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
  }
`,Mu=()=>{const{t:e,i18n:t}=ra(),n=re(),o=ee(),[a,i]=(0,r.useState)(30),[s,l]=(0,r.useState)(!1),u="ar"===t.language,{transaction:c,customerName:d,selectedDisplay:f}=o.state||{};(0,r.useEffect)(()=>{if(!c||!d||!f)return void n("/");const e=setInterval(()=>{i(e=>e<=1?(n("/"),0):e-1)},1e3);return()=>clearInterval(e)},[c,d,f,n]);return c&&d&&f?(0,Yr.jsx)(xu,{isRTL:u,children:(0,Yr.jsxs)(Su,{children:[(0,Yr.jsx)(ku,{children:(0,Yr.jsxs)(Eu,{viewBox:"0 0 100 100",children:[(0,Yr.jsx)("circle",{cx:"50",cy:"50",r:"45",fill:"none",stroke:"#28a745",strokeWidth:"3"}),(0,Yr.jsx)(Cu,{fill:"none",d:"M25,50 L40,65 L75,30"})]})}),(0,Yr.jsx)(_u,{children:e("paymentSuccessful")}),(0,Yr.jsx)(Tu,{children:u?"\u062a\u0645 \u062a\u0623\u0643\u064a\u062f \u0645\u0639\u0627\u0645\u0644\u062a\u0643 \u0628\u0646\u062c\u0627\u062d":"Your transaction has been confirmed successfully"}),(0,Yr.jsxs)(Lu,{onClick:async()=>{if(null!==c&&void 0!==c&&c.transactionNumber){await(async e=>{try{return await navigator.clipboard.writeText(e),!0}catch(t){const r=document.createElement("textarea");r.value=e,document.body.appendChild(r),r.focus(),r.select();try{return document.execCommand("copy"),document.body.removeChild(r),!0}catch(n){return document.body.removeChild(r),console.error("Failed to copy text:",n),!1}}})(c.transactionNumber)&&(l(!0),setTimeout(()=>l(!1),2e3))}},children:[e("transactionNumber"),": ",c.transactionNumber,s&&(0,Yr.jsx)("div",{style:{fontSize:"0.9rem",marginTop:"5px",color:"#28a745"},children:u?"\u062a\u0645 \u0627\u0644\u0646\u0633\u062e!":"Copied!"})]}),(0,Yr.jsxs)(Ru,{isRTL:u,children:[(0,Yr.jsxs)(Ou,{children:[(0,Yr.jsxs)(Nu,{children:[e("customerName"),":"]}),(0,Yr.jsx)(Pu,{children:d})]}),(0,Yr.jsxs)(Ou,{children:[(0,Yr.jsxs)(Nu,{children:[e("displayNumber",{number:f.displayNumber}),":"]}),(0,Yr.jsx)(Pu,{children:f.name})]}),(0,Yr.jsxs)(Ou,{children:[(0,Yr.jsxs)(Nu,{children:[e("amount"),":"]}),(0,Yr.jsx)(Pu,{children:Hr(c.amount||50,"SAR",u?"ar-SA":"en-US")})]}),(0,Yr.jsxs)(Ou,{children:[(0,Yr.jsxs)(Nu,{children:[e("duration"),":"]}),(0,Yr.jsxs)(Pu,{children:[Math.floor((c.duration||300)/60)," ",e("minutes")]})]}),(0,Yr.jsxs)(Ou,{children:[(0,Yr.jsxs)(Nu,{children:[e("startTime"),":"]}),(0,Yr.jsx)(Pu,{children:Vr(c.startTime||new Date,u?"ar-SA":"en-US")})]})]}),(0,Yr.jsxs)(ju,{children:[(0,Yr.jsx)(Au,{children:d}),(0,Yr.jsxs)(Du,{children:[u?"\u064a\u062a\u0645 \u0639\u0631\u0636 \u0627\u0633\u0645\u0643 \u0627\u0644\u0622\u0646 \u0639\u0644\u0649":"Your name is now displayed on"," ",f.name]})]}),(0,Yr.jsx)(zu,{children:u?`\u0633\u064a\u062a\u0645 \u062a\u0648\u062c\u064a\u0647\u0643 \u0644\u0644\u0635\u0641\u062d\u0629 \u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629 \u062e\u0644\u0627\u0644 ${a} \u062b\u0627\u0646\u064a\u0629`:`Redirecting to home page in ${a} seconds`}),(0,Yr.jsxs)(Fu,{children:[(0,Yr.jsx)($u,{onClick:()=>{n("/select-display")},children:u?"\u0645\u0639\u0627\u0645\u0644\u0629 \u062c\u062f\u064a\u062f\u0629":"New Transaction"}),(0,Yr.jsx)(Iu,{onClick:()=>{n("/")},children:u?"\u0627\u0644\u0635\u0641\u062d\u0629 \u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629":"Home Page"})]})]})}):null},Uu=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,Bu=Ws.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 450px;
  width: 100%;
`,Hu=Ws.div`
  text-align: center;
  margin-bottom: 30px;
`,Vu=Ws.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
`,Wu=Ws.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
`,qu=Ws.p`
  font-size: 1rem;
  color: #666;
`,Ku=Ws.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`,Qu=Ws.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,Yu=Ws.label`
  font-weight: 600;
  color: #333;
  font-size: 1rem;
`,Ju=Ws.input`
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  &:invalid {
    border-color: #dc3545;
  }
`,Gu=Ws.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${e=>e.disabled?.6:1};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    cursor: not-allowed;
  }
`,Xu=Ws.button`
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 10px;

  &:hover {
    background: #667eea;
    color: white;
  }
`,Zu=Ws.div`
  background: #dc3545;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
`,ec=Ws.div`
  background: #e3f2fd;
  color: #1976d2;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  text-align: center;
`,tc=()=>{const{t:e,i18n:t}=ra(),n=re(),{loading:o,error:a,ownerLogin:i}=(()=>{const[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(null),{login:a}=Xr();return{loading:e,error:n,ownerLogin:async e=>{t(!0),o(null);try{const t=await Lr(e);return t.success?(a(t.data.owner,t.data.token),t):(o(t.message||"\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u063a\u064a\u0631 \u0635\u062d\u064a\u062d\u0629"),null)}catch(n){return o(n.message||"\u062e\u0637\u0623 \u0641\u064a \u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u062f\u062e\u0648\u0644"),null}finally{t(!1)}}}})(),[s,l]=(0,r.useState)(""),u="ar"===t.language;return(0,Yr.jsx)(Uu,{isRTL:u,children:(0,Yr.jsxs)(Bu,{children:[(0,Yr.jsxs)(Hu,{children:[(0,Yr.jsx)(Vu,{children:"\ud83d\udc64"}),(0,Yr.jsx)(Wu,{children:u?"\u062f\u062e\u0648\u0644 \u0627\u0644\u0645\u0627\u0644\u0643":"Owner Login"}),(0,Yr.jsx)(qu,{children:u?"\u0644\u0648\u062d\u0629 \u062a\u062d\u0643\u0645 \u0627\u0644\u0646\u0638\u0627\u0645":"System Control Panel"})]}),(0,Yr.jsx)(ec,{children:u?"\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u0627\u0644\u0627\u0641\u062a\u0631\u0627\u0636\u064a\u0629: admin123":"Default password: admin123"}),a&&(0,Yr.jsx)(Zu,{children:a}),(0,Yr.jsxs)(Ku,{onSubmit:async e=>{if(e.preventDefault(),!s.trim())return;await i(s)&&n("/owner-dashboard")},children:[(0,Yr.jsxs)(Qu,{children:[(0,Yr.jsx)(Yu,{children:u?"\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631":"Password"}),(0,Yr.jsx)(Ju,{type:"password",value:s,onChange:e=>l(e.target.value),placeholder:u?"\u0623\u062f\u062e\u0644 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631":"Enter password",required:!0})]}),(0,Yr.jsx)(Gu,{type:"submit",disabled:o||!s.trim(),children:o?u?"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0642\u0642...":"Verifying...":u?"\u062f\u062e\u0648\u0644":"Login"})]}),(0,Yr.jsx)(Xu,{onClick:()=>{n("/")},children:e("back")})]})})},nc=Ws.div`
  min-height: 100vh;
  background: #f8f9fa;
  direction: ${e=>e.isRTL?"rtl":"ltr"};
`,rc=Ws.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
`,oc=Ws.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
`,ac=Ws.h1`
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`,ic=Ws.button`
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2px solid white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: white;
    color: #667eea;
  }
`,sc=Ws.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
`,lc=Ws.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`,uc=Ws.div`
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  text-align: center;
`,cc=Ws.div`
  font-size: 2.5rem;
  font-weight: bold;
  color: ${e=>e.color||"#667eea"};
  margin-bottom: 10px;
`,dc=Ws.div`
  font-size: 1rem;
  color: #666;
  font-weight: 600;
`,fc=Ws.div`
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
`,pc=Ws.h2`
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 20px;
  font-weight: 700;
`,hc=Ws.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`,mc=Ws.div`
  border: 3px solid ${e=>{switch(e.status){case"available":return"#28a745";case"occupied":return"#dc3545";case"reserved":return"#ffc107";case"maintenance":return"#6c757d";default:return"#ddd"}}};
  border-radius: 10px;
  padding: 20px;
  background: #f8f9fa;
`,gc=Ws.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
`,yc=Ws.div`
  display: inline-block;
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
  background: ${e=>{switch(e.status){case"available":return"#28a745";case"occupied":return"#dc3545";case"reserved":return"#ffc107";case"maintenance":return"#6c757d";default:return"#ddd"}}};
`,vc=Ws.div`
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-top: 10px;
`,bc=Ws.div`
  overflow-x: auto;
`,wc=Ws.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
`,xc=Ws.th`
  background: #f8f9fa;
  padding: 12px;
  text-align: ${e=>e.isRTL?"right":"left"};
  border-bottom: 2px solid #ddd;
  font-weight: 600;
  color: #333;
`,Sc=Ws.td`
  padding: 12px;
  border-bottom: 1px solid #eee;
  text-align: ${e=>e.isRTL?"right":"left"};
`,kc=Ws.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #666;
`,Ec=Ws.div`
  background: #dc3545;
  color: white;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  margin: 20px 0;
`,Cc=()=>{const{t:e,i18n:t}=ra(),n=re(),{user:o,logout:a}=Xr(),[i,s]=(0,r.useState)(null),[l,u]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[f,p]=(0,r.useState)(null),h="ar"===t.language;(0,r.useEffect)(()=>{if(!o||"owner"!==o.type)return void n("/owner-login");m(),g();const e=setInterval(()=>{m(),g()},3e4);return()=>clearInterval(e)},[o,n]);const m=async()=>{try{const e=await Mr();e.success?(s(e.data),p(null)):p(e.message||"\u0641\u0634\u0644 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a")}catch(f){p("\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629"),console.error("Error fetching dashboard data:",f)}},g=async()=>{try{const e=await Ar();e.success&&u(e.data)}catch(f){console.error("Error fetching displays:",f)}finally{d(!1)}},y=t=>{switch(t){case"available":return e("available");case"occupied":return e("occupied");case"reserved":return e("reserved");case"maintenance":return e("maintenance");default:return t}};return c?(0,Yr.jsxs)(nc,{isRTL:h,children:[(0,Yr.jsx)(rc,{children:(0,Yr.jsx)(oc,{children:(0,Yr.jsx)(ac,{children:e("ownerDashboard")})})}),(0,Yr.jsx)(sc,{children:(0,Yr.jsx)(kc,{children:h?"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0645\u064a\u0644...":"Loading..."})})]}):(0,Yr.jsxs)(nc,{isRTL:h,children:[(0,Yr.jsx)(rc,{children:(0,Yr.jsxs)(oc,{children:[(0,Yr.jsx)(ac,{children:e("ownerDashboard")}),(0,Yr.jsx)(ic,{onClick:()=>{a(),n("/")},children:h?"\u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u062e\u0631\u0648\u062c":"Logout"})]})}),(0,Yr.jsxs)(sc,{children:[f&&(0,Yr.jsx)(Ec,{children:f}),i&&(0,Yr.jsxs)(lc,{children:[(0,Yr.jsxs)(uc,{children:[(0,Yr.jsx)(cc,{color:"#28a745",children:i.today.transactions}),(0,Yr.jsx)(dc,{children:e("todayTransactions")})]}),(0,Yr.jsxs)(uc,{children:[(0,Yr.jsx)(cc,{color:"#667eea",children:Hr(i.today.revenue,"SAR",h?"ar-SA":"en-US")}),(0,Yr.jsx)(dc,{children:e("todayRevenue")})]}),(0,Yr.jsxs)(uc,{children:[(0,Yr.jsx)(cc,{color:"#dc3545",children:i.total.transactions}),(0,Yr.jsx)(dc,{children:e("totalTransactions")})]}),(0,Yr.jsxs)(uc,{children:[(0,Yr.jsx)(cc,{color:"#ffc107",children:Hr(i.total.revenue,"SAR",h?"ar-SA":"en-US")}),(0,Yr.jsx)(dc,{children:e("totalRevenue")})]})]}),(0,Yr.jsxs)(fc,{children:[(0,Yr.jsx)(pc,{children:e("displayStatus")}),(0,Yr.jsx)(hc,{children:l.map(t=>(0,Yr.jsxs)(mc,{status:t.status,children:[(0,Yr.jsx)(gc,{children:e("displayNumber",{number:t.displayNumber})}),(0,Yr.jsx)("div",{style:{fontWeight:"600",marginBottom:"10px"},children:t.name}),(0,Yr.jsx)(yc,{status:t.status,children:y(t.status)}),"occupied"===t.status&&t.customerName&&(0,Yr.jsxs)(vc,{children:[(0,Yr.jsxs)("div",{children:[(0,Yr.jsx)("strong",{children:h?"\u0627\u0644\u0639\u0645\u064a\u0644:":"Customer:"})," ",t.customerName]}),t.timeRemaining&&!t.timeRemaining.expired&&(0,Yr.jsxs)("div",{children:[(0,Yr.jsx)("strong",{children:h?"\u0627\u0644\u0648\u0642\u062a \u0627\u0644\u0645\u062a\u0628\u0642\u064a:":"Time remaining:"})," ",Br(t.timeRemaining.remaining)]})]})]},t.id))})]}),i&&i.activeTransactions.length>0&&(0,Yr.jsxs)(fc,{children:[(0,Yr.jsx)(pc,{children:e("activeTransactions")}),(0,Yr.jsx)(bc,{children:(0,Yr.jsxs)(wc,{children:[(0,Yr.jsx)("thead",{children:(0,Yr.jsxs)("tr",{children:[(0,Yr.jsx)(xc,{isRTL:h,children:e("transactionNumber")}),(0,Yr.jsx)(xc,{isRTL:h,children:e("customerName")}),(0,Yr.jsx)(xc,{isRTL:h,children:h?"\u0627\u0644\u0634\u0627\u0634\u0629":"Display"}),(0,Yr.jsx)(xc,{isRTL:h,children:e("amount")}),(0,Yr.jsx)(xc,{isRTL:h,children:e("endTime")})]})}),(0,Yr.jsx)("tbody",{children:i.activeTransactions.map(e=>(0,Yr.jsxs)("tr",{children:[(0,Yr.jsx)(Sc,{isRTL:h,children:e.transactionNumber}),(0,Yr.jsx)(Sc,{isRTL:h,children:e.customerName}),(0,Yr.jsx)(Sc,{isRTL:h,children:e.displayName}),(0,Yr.jsx)(Sc,{isRTL:h,children:Hr(e.amount,"SAR",h?"ar-SA":"en-US")}),(0,Yr.jsx)(Sc,{isRTL:h,children:new Date(e.endTime).toLocaleTimeString(h?"ar-SA":"en-US")})]},e.id))})]})})]})]})]})},_c=Object.create(null);_c.open="0",_c.close="1",_c.ping="2",_c.pong="3",_c.message="4",_c.upgrade="5",_c.noop="6";const Tc=Object.create(null);Object.keys(_c).forEach(e=>{Tc[_c[e]]=e});const Rc={type:"error",data:"parser error"},Oc="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),Nc="function"===typeof ArrayBuffer,Pc=e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,Lc=(e,t,n)=>{let{type:r,data:o}=e;return Oc&&o instanceof Blob?t?n(o):jc(o,n):Nc&&(o instanceof ArrayBuffer||Pc(o))?t?n(o):jc(new Blob([o]),n):n(_c[r]+(o||""))},jc=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function Ac(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Dc;const zc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Fc="undefined"===typeof Uint8Array?[]:new Uint8Array(256);for(let n=0;n<64;n++)Fc[zc.charCodeAt(n)]=n;const $c="function"===typeof ArrayBuffer,Ic=(e,t)=>{if("string"!==typeof e)return{type:"message",data:Uc(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:Mc(e.substring(1),t)};return Tc[n]?e.length>1?{type:Tc[n],data:e.substring(1)}:{type:Tc[n]}:Rc},Mc=(e,t)=>{if($c){const n=(e=>{let t,n,r,o,a,i=.75*e.length,s=e.length,l=0;"="===e[e.length-1]&&(i--,"="===e[e.length-2]&&i--);const u=new ArrayBuffer(i),c=new Uint8Array(u);for(t=0;t<s;t+=4)n=Fc[e.charCodeAt(t)],r=Fc[e.charCodeAt(t+1)],o=Fc[e.charCodeAt(t+2)],a=Fc[e.charCodeAt(t+3)],c[l++]=n<<2|r>>4,c[l++]=(15&r)<<4|o>>2,c[l++]=(3&o)<<6|63&a;return u})(e);return Uc(n,t)}return{base64:!0,data:e}},Uc=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,Bc=String.fromCharCode(30);function Hc(){return new TransformStream({transform(e,t){!function(e,t){Oc&&e.data instanceof Blob?e.data.arrayBuffer().then(Ac).then(t):Nc&&(e.data instanceof ArrayBuffer||Pc(e.data))?t(Ac(e.data)):Lc(e,!1,e=>{Dc||(Dc=new TextEncoder),t(Dc.encode(e))})}(e,n=>{const r=n.length;let o;if(r<126)o=new Uint8Array(1),new DataView(o.buffer).setUint8(0,r);else if(r<65536){o=new Uint8Array(3);const e=new DataView(o.buffer);e.setUint8(0,126),e.setUint16(1,r)}else{o=new Uint8Array(9);const e=new DataView(o.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(r))}e.data&&"string"!==typeof e.data&&(o[0]|=128),t.enqueue(o),t.enqueue(n)})}})}let Vc;function Wc(e){return e.reduce((e,t)=>e+t.length,0)}function qc(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let o=0;o<t;o++)n[o]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function Kc(e){if(e)return function(e){for(var t in Kc.prototype)e[t]=Kc.prototype[t];return e}(e)}Kc.prototype.on=Kc.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},Kc.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},Kc.prototype.off=Kc.prototype.removeListener=Kc.prototype.removeAllListeners=Kc.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var o=0;o<r.length;o++)if((n=r[o])===t||n.fn===t){r.splice(o,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},Kc.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,t)}return this},Kc.prototype.emitReserved=Kc.prototype.emit,Kc.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},Kc.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Qc="function"===typeof Promise&&"function"===typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),Yc="undefined"!==typeof self?self:"undefined"!==typeof window?window:Function("return this")();function Jc(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t),{})}const Gc=Yc.setTimeout,Xc=Yc.clearTimeout;function Zc(e,t){t.useNativeTimers?(e.setTimeoutFn=Gc.bind(Yc),e.clearTimeoutFn=Xc.bind(Yc)):(e.setTimeoutFn=Yc.setTimeout.bind(Yc),e.clearTimeoutFn=Yc.clearTimeout.bind(Yc))}function ed(e){return"string"===typeof e?function(e){let t=0,n=0;for(let r=0,o=e.length;r<o;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}(e):Math.ceil(1.33*(e.byteLength||e.size))}function td(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class nd extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class rd extends Kc{constructor(e){super(),this.writable=!1,Zc(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new nd(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=Ic(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class od extends rd{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(Bc),r=[];for(let o=0;o<n.length;o++){const e=Ic(n[o],t);if(r.push(e),"error"===e.type)break}return r})(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,r=new Array(n);let o=0;e.forEach((e,a)=>{Lc(e,!1,e=>{r[a]=e,++o===n&&t(r.join(Bc))})})})(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=td()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let ad=!1;try{ad="undefined"!==typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(pf){}const id=ad;function sd(){}class ld extends od{constructor(e){if(super(e),"undefined"!==typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!==typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class ud extends Kc{constructor(e,t,n){super(),this.createRequest=e,Zc(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var e;const t=Jc(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&n.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(ff){}if("POST"===this._method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(ff){}try{n.setRequestHeader("Accept","*/*")}catch(ff){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var e;3===n.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(n.getResponseHeader("set-cookie"))),4===n.readyState&&(200===n.status||1223===n.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"===typeof n.status?n.status:0)},0))},n.send(this._data)}catch(ff){return void this.setTimeoutFn(()=>{this._onError(ff)},0)}"undefined"!==typeof document&&(this._index=ud.requestsCount++,ud.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if("undefined"!==typeof this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=sd,e)try{this._xhr.abort()}catch(ff){}"undefined"!==typeof document&&delete ud.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(ud.requestsCount=0,ud.requests={},"undefined"!==typeof document)if("function"===typeof attachEvent)attachEvent("onunload",cd);else if("function"===typeof addEventListener){addEventListener("onpagehide"in Yc?"pagehide":"unload",cd,!1)}function cd(){for(let e in ud.requests)ud.requests.hasOwnProperty(e)&&ud.requests[e].abort()}const dd=function(){const e=fd({xdomain:!1});return e&&null!==e.responseType}();function fd(e){const t=e.xdomain;try{if("undefined"!==typeof XMLHttpRequest&&(!t||id))return new XMLHttpRequest}catch(ff){}if(!t)try{return new(Yc[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(ff){}}const pd="undefined"!==typeof navigator&&"string"===typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class hd extends rd{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=pd?{}:Jc(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(pf){return this.emitReserved("error",pf)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;Lc(n,this.supportsBinary,e=>{try{this.doWrite(n,e)}catch(ff){}r&&Qc(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){"undefined"!==typeof this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=td()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const md=Yc.WebSocket||Yc.MozWebSocket;const gd={websocket:class extends hd{createSocket(e,t,n){return pd?new md(e,t,n):t?new md(e,t):new md(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends rd{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(pf){return this.emitReserved("error",pf)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=function(e,t){Vc||(Vc=new TextDecoder);const n=[];let r=0,o=-1,a=!1;return new TransformStream({transform(i,s){for(n.push(i);;){if(0===r){if(Wc(n)<1)break;const e=qc(n,1);a=128===(128&e[0]),o=127&e[0],r=o<126?3:126===o?1:2}else if(1===r){if(Wc(n)<2)break;const e=qc(n,2);o=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(Wc(n)<8)break;const e=qc(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),a=t.getUint32(0);if(a>Math.pow(2,21)-1){s.enqueue(Rc);break}o=a*Math.pow(2,32)+t.getUint32(4),r=3}else{if(Wc(n)<o)break;const e=qc(n,o);s.enqueue(Ic(a?e:Vc.decode(e),t)),r=0}if(0===o||o>e){s.enqueue(Rc);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),r=Hc();r.readable.pipeTo(e.writable),this._writer=r.writable.getWriter();const o=()=>{n.read().then(e=>{let{done:t,value:n}=e;t||(this.onPacket(n),o())}).catch(e=>{})};o();const a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;this._writer.write(n).then(()=>{r&&Qc(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends ld{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=dd&&!t}request(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(e,{xd:this.xd},this.opts),new ud(fd,this.uri(),e)}}},yd=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,vd=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function bd(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");-1!=n&&-1!=r&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let o=yd.exec(e||""),a={},i=14;for(;i--;)a[vd[i]]=o[i]||"";return-1!=n&&-1!=r&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=function(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||r.splice(0,1);"/"==t.slice(-1)&&r.splice(r.length-1,1);return r}(0,a.path),a.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(n[t]=r)}),n}(0,a.query),a}const wd="function"===typeof addEventListener&&"function"===typeof removeEventListener,xd=[];wd&&addEventListener("offline",()=>{xd.forEach(e=>e())},!1);class Sd extends Kc{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"===typeof e&&(t=e,e=null),e){const n=bd(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=bd(t.host).host);Zc(this,t),this.secure=null!=t.secure?t.secure:"undefined"!==typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!==typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!==typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"===typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let r=0,o=n.length;r<o;r++){let e=n[r].split("=");t[decodeURIComponent(e[0])]=decodeURIComponent(e[1])}return t}(this.opts.query)),wd&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},xd.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);const e=this.opts.rememberUpgrade&&Sd.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",Sd.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){const n=this.writeBuffer[t].data;if(n&&(e+=ed(n)),t>0&&e>this._maxPayload)return this.writeBuffer.slice(0,t);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,Qc(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,r){if("function"===typeof t&&(r=t,t=void 0),"function"===typeof n&&(r=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const o={type:e,data:t,options:n};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),r&&this.once("flush",r),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():e()}):this.upgrading?n():e()),this}_onError(e){if(Sd.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),wd&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=xd.indexOf(this._offlineEventListener);-1!==e&&xd.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}Sd.protocol=4;class kd extends Sd{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;Sd.priorWebsocketSuccess=!1;const r=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;Sd.priorWebsocketSuccess="websocket"===t.name,this.transport.pause(()=>{n||"closed"!==this.readyState&&(u(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}}))};function o(){n||(n=!0,u(),t.close(),t=null)}const a=e=>{const n=new Error("probe error: "+e);n.transport=t.name,o(),this.emitReserved("upgradeError",n)};function i(){a("transport closed")}function s(){a("socket closed")}function l(e){t&&e.name!==t.name&&o()}const u=()=>{t.removeListener("open",r),t.removeListener("error",a),t.removeListener("close",i),this.off("close",s),this.off("upgrading",l)};t.once("open",r),t.once("error",a),t.once("close",i),this.once("close",s),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{n||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}class Ed extends kd{constructor(e){const t="object"===typeof e?e:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(!t.transports||t.transports&&"string"===typeof t.transports[0])&&(t.transports=(t.transports||["polling","websocket","webtransport"]).map(e=>gd[e]).filter(e=>!!e)),super(e,t)}}const Cd="function"===typeof ArrayBuffer,_d=Object.prototype.toString,Td="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===_d.call(Blob),Rd="function"===typeof File||"undefined"!==typeof File&&"[object FileConstructor]"===_d.call(File);function Od(e){return Cd&&(e instanceof ArrayBuffer||(e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||Td&&e instanceof Blob||Rd&&e instanceof File}function Nd(e,t){if(!e||"object"!==typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(Nd(e[t]))return!0;return!1}if(Od(e))return!0;if(e.toJSON&&"function"===typeof e.toJSON&&1===arguments.length)return Nd(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&Nd(e[n]))return!0;return!1}function Pd(e){const t=[],n=e.data,r=e;return r.data=Ld(n,t),r.attachments=t.length,{packet:r,buffers:t}}function Ld(e,t){if(!e)return e;if(Od(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=Ld(e[r],t);return n}if("object"===typeof e&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=Ld(e[r],t));return n}return e}function jd(e,t){return e.data=Ad(e.data,t),delete e.attachments,e}function Ad(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"===typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=Ad(e[n],t);else if("object"===typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=Ad(e[n],t));return e}const Dd=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],zd=5;var Fd;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(Fd||(Fd={}));class $d{constructor(e){this.replacer=e}encode(e){return e.type!==Fd.EVENT&&e.type!==Fd.ACK||!Nd(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===Fd.EVENT?Fd.BINARY_EVENT:Fd.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==Fd.BINARY_EVENT&&e.type!==Fd.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=Pd(e),n=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(n),r}}function Id(e){return"[object Object]"===Object.prototype.toString.call(e)}class Md extends Kc{constructor(e){super(),this.reviver=e}add(e){let t;if("string"===typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===Fd.BINARY_EVENT;n||t.type===Fd.BINARY_ACK?(t.type=n?Fd.EVENT:Fd.ACK,this.reconstructor=new Ud(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!Od(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===Fd[n.type])throw new Error("unknown packet type "+n.type);if(n.type===Fd.BINARY_EVENT||n.type===Fd.BINARY_ACK){const r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const o=e.substring(r,t);if(o!=Number(o)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(o)}if("/"===e.charAt(t+1)){const r=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(r,t)}else n.nsp="/";const r=e.charAt(t+1);if(""!==r&&Number(r)==r){const r=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(!Md.isPayloadValid(n.type,r))throw new Error("invalid payload");n.data=r}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(ff){return!1}}static isPayloadValid(e,t){switch(e){case Fd.CONNECT:return Id(t);case Fd.DISCONNECT:return void 0===t;case Fd.CONNECT_ERROR:return"string"===typeof t||Id(t);case Fd.EVENT:case Fd.BINARY_EVENT:return Array.isArray(t)&&("number"===typeof t[0]||"string"===typeof t[0]&&-1===Dd.indexOf(t[0]));case Fd.ACK:case Fd.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Ud{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=jd(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function Bd(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const Hd=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Vd extends Kc{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[Bd(e,"open",this.onopen.bind(this)),Bd(e,"packet",this.onpacket.bind(this)),Bd(e,"error",this.onerror.bind(this)),Bd(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.unshift("message"),this.emit.apply(this,t),this}emit(e){var t,n,r;if(Hd.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];if(a.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(a),this;const s={type:Fd.EVENT,data:a,options:{}};if(s.options.compress=!1!==this.flags.compress,"function"===typeof a[a.length-1]){const e=this.ids++,t=a.pop();this._registerAckCallback(e,t),s.id=e}const l=null===(n=null===(t=this.io.engine)||void 0===t?void 0:t.transport)||void 0===n?void 0:n.writable,u=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r._hasPingExpired());return this.flags.volatile&&!l||(u?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(e,t){var n,r=this;const o=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===o)return void(this.acks[e]=t);const a=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))},o),i=function(){r.io.clearTimeoutFn(a);for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];t.apply(r,n)};i.withError=!0,this.acks[e]=i}emitWithAck(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return new Promise((t,r)=>{const o=(e,n)=>e?r(e):t(n);o.withError=!0,n.push(o),this.emit(e,...n)})}_addToQueue(e){var t=this;let n;"function"===typeof e[e.length-1]&&(n=e.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push(function(e){if(r!==t._queue[0])return;if(null!==e)r.tryCount>t._opts.retries&&(t._queue.shift(),n&&n(e));else if(t._queue.shift(),n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];n(null,...a)}return r.pending=!1,t._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:Fd.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case Fd.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case Fd.EVENT:case Fd.BINARY_EVENT:this.onevent(e);break;case Fd.ACK:case Fd.BINARY_ACK:this.onack(e);break;case Fd.DISCONNECT:this.ondisconnect();break;case Fd.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"===typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(){if(!n){n=!0;for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];t.packet({type:Fd.ACK,id:e,data:o})}}}onack(e){const t=this.acks[e.id];"function"===typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:Fd.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function Wd(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Wd.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this.max)},Wd.prototype.reset=function(){this.attempts=0},Wd.prototype.setMin=function(e){this.ms=e},Wd.prototype.setMax=function(e){this.max=e},Wd.prototype.setJitter=function(e){this.jitter=e};class qd extends Kc{constructor(e,n){var r;super(),this.nsps={},this.subs=[],e&&"object"===typeof e&&(n=e,e=void 0),(n=n||{}).path=n.path||"/socket.io",this.opts=n,Zc(this,n),this.reconnection(!1!==n.reconnection),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=n.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new Wd({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==n.timeout?2e4:n.timeout),this._readyState="closed",this.uri=e;const o=n.parser||t;this.encoder=new o.Encoder,this.decoder=new o.Decoder,this._autoConnect=!1!==n.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Ed(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const r=Bd(t,"open",function(){n.onopen(),e&&e()}),o=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},a=Bd(t,"error",o);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn(()=>{r(),o(new Error("timeout")),t.close()},e);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}return this.subs.push(r),this.subs.push(a),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(Bd(e,"ping",this.onping.bind(this)),Bd(e,"data",this.ondata.bind(this)),Bd(e,"error",this.onerror.bind(this)),Bd(e,"close",this.onclose.bind(this)),Bd(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(ff){this.onclose("parse error",ff)}}ondecoded(e){Qc(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new Vd(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t){if(this.nsps[n].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()}))},t);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const Kd={};function Qd(e,t){"object"===typeof e&&(t=e,e=void 0);const n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=e;n=n||"undefined"!==typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"===typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e="undefined"!==typeof n?n.protocol+"//"+e:"https://"+e),r=bd(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const o=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+o+":"+r.port+t,r.href=r.protocol+"://"+o+(n&&n.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),r=n.source,o=n.id,a=n.path,i=Kd[o]&&a in Kd[o].nsps;let s;return t.forceNew||t["force new connection"]||!1===t.multiplex||i?s=new qd(r,t):(Kd[o]||(Kd[o]=new qd(r,t)),s=Kd[o]),n.query&&!t.query&&(t.query=n.queryKey),s.socket(n.path,t)}Object.assign(Qd,{Manager:qd,Socket:Vd,io:Qd,connect:Qd});const Yd=qs`
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`,Jd=qs`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`,Gd=Ws.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: ${Jd} 4s ease-in-out infinite;
  }
`,Xd=Ws.div`
  background: white;
  border-radius: 30px;
  padding: 60px;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 800px;
  width: 100%;
  position: relative;
  z-index: 1;
  animation: ${Yd} 1s ease-out;
`,Zd=Ws.div`
  position: absolute;
  top: 20px;
  right: 20px;
  background: #667eea;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 1.2rem;
`,ef=Ws.h1`
  font-size: 4rem;
  font-weight: bold;
  color: #333;
  margin: 40px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  word-break: break-word;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`,tf=Ws.div`
  font-size: 1.8rem;
  color: #666;
  margin-bottom: 30px;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 1.3rem;
  }
`,nf=Ws.div`
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin: 30px 0;
  border-left: 5px solid #667eea;
`,rf=Ws.div`
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 10px;
`,of=Ws.div`
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  font-family: 'Courier New', monospace;
`,af=Ws.div`
  background: ${e=>{switch(e.type){case"waiting":return"#ffc107";case"active":return"#28a745";case"ending":return"#dc3545";default:return"#6c757d"}}};
  color: white;
  padding: 20px;
  border-radius: 15px;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 20px 0;
`,sf=Ws.div`
  width: 60px;
  height: 60px;
  border: 6px solid #f3f3f3;
  border-top: 6px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 40px auto;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`,lf=Ws.div`
  background: #dc3545;
  color: white;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  font-size: 1.2rem;
`,uf=()=>{const{displayId:e}=oe(),[t,n]=(0,r.useState)(null),[o,a]=(0,r.useState)(!0),[i,s]=(0,r.useState)(null),[l,u]=(0,r.useState)(null),[c,d]=(0,r.useState)(null);(0,r.useEffect)(()=>{var t;const n=Qd((null===(t={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL)||void 0===t?void 0:t.replace("/api",""))||"http://localhost:3001");return d(n),n.emit("join-display",e),n.on("display-activated",e=>{f()}),n.on("transaction-ended",()=>{f()}),()=>{n.disconnect()}},[e]),(0,r.useEffect)(()=>{f();const e=setInterval(f,1e4);return()=>clearInterval(e)},[e]),(0,r.useEffect)(()=>{if(t&&"occupied"===t.status&&t.endTime){const e=setInterval(()=>{const e=(e=>{const t=new Date,n=new Date(e)-t;return n<=0?{expired:!0,remaining:0}:{expired:!1,remaining:Math.floor(n/1e3),minutes:Math.floor(n/6e4),seconds:Math.floor(n%6e4/1e3)}})(t.endTime);u(e),e.expired&&f()},1e3);return()=>clearInterval(e)}},[t]);const f=async()=>{try{const t=await Dr(e);t.success?(n(t.data),s(null)):s(t.message||"\u0641\u0634\u0644 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u0634\u0627\u0634\u0629")}catch(i){s("\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629"),console.error("Error fetching display data:",i)}finally{a(!1)}};return o?(0,Yr.jsx)(Gd,{children:(0,Yr.jsxs)(Xd,{children:[(0,Yr.jsx)(sf,{}),(0,Yr.jsx)("div",{style:{fontSize:"1.2rem",color:"#666",marginTop:"20px"},children:"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0645\u064a\u0644..."})]})}):i?(0,Yr.jsx)(Gd,{children:(0,Yr.jsx)(Xd,{children:(0,Yr.jsx)(lf,{children:i})})}):t?(0,Yr.jsx)(Gd,{children:(0,Yr.jsxs)(Xd,{children:[(0,Yr.jsxs)(Zd,{children:["\u0634\u0627\u0634\u0629 ",t.displayNumber]}),"available"===t.status&&(0,Yr.jsxs)(Yr.Fragment,{children:[(0,Yr.jsx)(tf,{children:"\u0623\u0647\u0644\u0627\u064b \u0648\u0633\u0647\u0644\u0627\u064b \u0628\u0643\u0645"}),(0,Yr.jsx)(af,{type:"waiting",children:"\u0627\u0644\u0634\u0627\u0634\u0629 \u0645\u062a\u0627\u062d\u0629 \u0644\u0644\u062d\u062c\u0632"})]}),"occupied"===t.status&&t.customerName&&(0,Yr.jsxs)(Yr.Fragment,{children:[(0,Yr.jsx)(tf,{children:"\u0623\u0647\u0644\u0627\u064b \u0648\u0633\u0647\u0644\u0627\u064b"}),(0,Yr.jsx)(ef,{children:t.customerName}),l&&!l.expired&&(0,Yr.jsxs)(nf,{children:[(0,Yr.jsx)(rf,{children:"\u0627\u0644\u0648\u0642\u062a \u0627\u0644\u0645\u062a\u0628\u0642\u064a"}),(0,Yr.jsx)(of,{children:Br(l.remaining)})]}),(0,Yr.jsx)(af,{type:l&&l.remaining<60?"ending":"active",children:l&&l.remaining<60?"\u0627\u0644\u0639\u0631\u0636 \u0639\u0644\u0649 \u0648\u0634\u0643 \u0627\u0644\u0627\u0646\u062a\u0647\u0627\u0621":"\u0627\u0644\u0639\u0631\u0636 \u0646\u0634\u0637 \u0627\u0644\u0622\u0646"})]}),"reserved"===t.status&&(0,Yr.jsxs)(Yr.Fragment,{children:[(0,Yr.jsx)(tf,{children:"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0636\u064a\u0631..."}),(0,Yr.jsx)(af,{type:"waiting",children:"\u0627\u0644\u0634\u0627\u0634\u0629 \u0645\u062d\u062c\u0648\u0632\u0629 - \u0641\u064a \u0627\u0646\u062a\u0638\u0627\u0631 \u0627\u0644\u062f\u0641\u0639"})]}),"maintenance"===t.status&&(0,Yr.jsxs)(Yr.Fragment,{children:[(0,Yr.jsx)(tf,{children:"\u0646\u0639\u062a\u0630\u0631 \u0644\u0644\u0625\u0632\u0639\u0627\u062c"}),(0,Yr.jsx)(af,{type:"maintenance",children:"\u0627\u0644\u0634\u0627\u0634\u0629 \u062a\u062d\u062a \u0627\u0644\u0635\u064a\u0627\u0646\u0629"})]})]})}):(0,Yr.jsx)(Gd,{children:(0,Yr.jsx)(Xd,{children:(0,Yr.jsx)(lf,{children:"\u0627\u0644\u0634\u0627\u0634\u0629 \u063a\u064a\u0631 \u0645\u0648\u062c\u0648\u062f\u0629"})})})};const cf=function(){return(0,Yr.jsx)(Gr,{children:(0,Yr.jsx)(nt,{children:(0,Yr.jsx)("div",{className:"App",children:(0,Yr.jsxs)(xe,{children:[(0,Yr.jsx)(be,{path:"/",element:(0,Yr.jsx)(nl,{})}),(0,Yr.jsx)(be,{path:"/select-display",element:(0,Yr.jsx)(vl,{})}),(0,Yr.jsx)(be,{path:"/login",element:(0,Yr.jsx)(jl,{})}),(0,Yr.jsx)(be,{path:"/enter-name",element:(0,Yr.jsx)(eu,{})}),(0,Yr.jsx)(be,{path:"/payment",element:(0,Yr.jsx)(vu,{})}),(0,Yr.jsx)(be,{path:"/success",element:(0,Yr.jsx)(Mu,{})}),(0,Yr.jsx)(be,{path:"/owner-login",element:(0,Yr.jsx)(tc,{})}),(0,Yr.jsx)(be,{path:"/owner-dashboard",element:(0,Yr.jsx)(Cc,{})}),(0,Yr.jsx)(be,{path:"/display/:displayId",element:(0,Yr.jsx)(uf,{})})]})})})})},df=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:o,getLCP:a,getTTFB:i}=t;n(e),r(e),o(e),a(e),i(e)})};o.createRoot(document.getElementById("root")).render((0,Yr.jsx)(r.StrictMode,{children:(0,Yr.jsx)(cf,{})})),df()})()})();
//# sourceMappingURL=main.9f73c19c.js.map