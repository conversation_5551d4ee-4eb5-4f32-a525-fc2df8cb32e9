{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\SuccessPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { formatCurrency, formatDate, copyToClipboard } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst checkmark = keyframes`\n  0% {\n    stroke-dashoffset: 100;\n  }\n  100% {\n    stroke-dashoffset: 0;\n  }\n`;\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n_c = Container;\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n  animation: ${fadeIn} 0.6s ease-out;\n`;\n_c2 = Card;\nconst SuccessIcon = styled.div`\n  width: 120px;\n  height: 120px;\n  margin: 0 auto 30px;\n  position: relative;\n`;\n_c3 = SuccessIcon;\nconst CheckmarkSVG = styled.svg`\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  display: block;\n  stroke-width: 3;\n  stroke: #28a745;\n  stroke-miterlimit: 10;\n  box-shadow: inset 0px 0px 0px #28a745;\n  animation: ${checkmark} 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;\n`;\n_c4 = CheckmarkSVG;\nconst CheckmarkPath = styled.path`\n  stroke-dasharray: 100;\n  stroke-dashoffset: 100;\n`;\n_c5 = CheckmarkPath;\nconst Title = styled.h1`\n  font-size: 2.5rem;\n  color: #28a745;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n_c6 = Title;\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 30px;\n`;\n_c7 = Subtitle;\nconst TransactionDetails = styled.div`\n  background: #f8f9fa;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n`;\n_c8 = TransactionDetails;\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n\n  &:last-child {\n    margin-bottom: 0;\n    border-bottom: none;\n  }\n`;\n_c9 = DetailRow;\nconst DetailLabel = styled.span`\n  font-weight: 600;\n  color: #333;\n`;\n_c0 = DetailLabel;\nconst DetailValue = styled.span`\n  color: #666;\n  font-weight: 500;\n`;\n_c1 = DetailValue;\nconst TransactionNumber = styled.div`\n  background: #e3f2fd;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1976d2;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background: #bbdefb;\n  }\n`;\n_c10 = TransactionNumber;\nconst DisplayPreview = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 15px;\n  margin: 20px 0;\n  position: relative;\n  overflow: hidden;\n`;\n_c11 = DisplayPreview;\nconst DisplayName = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n`;\n_c12 = DisplayName;\nconst DisplayInfo = styled.div`\n  font-size: 1rem;\n  opacity: 0.9;\n`;\n_c13 = DisplayInfo;\nconst CountdownTimer = styled.div`\n  background: #fff3cd;\n  color: #856404;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n  font-size: 1.1rem;\n  font-weight: 600;\n`;\n_c14 = CountdownTimer;\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n_c15 = ButtonGroup;\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 30px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n`;\n_c16 = Button;\nconst SecondaryButton = styled(Button)`\n  background: #6c757d;\n  \n  &:hover {\n    background: #5a6268;\n    box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);\n  }\n`;\n_c17 = SecondaryButton;\nconst SuccessPage = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [countdown, setCountdown] = useState(30);\n  const [copied, setCopied] = useState(false);\n  const isRTL = i18n.language === 'ar';\n  const {\n    transaction,\n    customerName,\n    selectedDisplay\n  } = location.state || {};\n  useEffect(() => {\n    // التحقق من وجود البيانات المطلوبة\n    if (!transaction || !customerName || !selectedDisplay) {\n      navigate('/');\n      return;\n    }\n\n    // العد التنازلي للعودة للصفحة الرئيسية\n    const timer = setInterval(() => {\n      setCountdown(prev => {\n        if (prev <= 1) {\n          navigate('/');\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [transaction, customerName, selectedDisplay, navigate]);\n  const handleCopyTransactionNumber = async () => {\n    if (transaction !== null && transaction !== void 0 && transaction.transactionNumber) {\n      const success = await copyToClipboard(transaction.transactionNumber);\n      if (success) {\n        setCopied(true);\n        setTimeout(() => setCopied(false), 2000);\n      }\n    }\n  };\n  const handleNewTransaction = () => {\n    navigate('/select-display');\n  };\n  const handleGoHome = () => {\n    navigate('/');\n  };\n  if (!transaction || !customerName || !selectedDisplay) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    isRTL: isRTL,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(SuccessIcon, {\n        children: /*#__PURE__*/_jsxDEV(CheckmarkSVG, {\n          viewBox: \"0 0 100 100\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"50\",\n            cy: \"50\",\n            r: \"45\",\n            fill: \"none\",\n            stroke: \"#28a745\",\n            strokeWidth: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CheckmarkPath, {\n            fill: \"none\",\n            d: \"M25,50 L40,65 L75,30\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: t('paymentSuccessful')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n        children: isRTL ? 'تم تأكيد معاملتك بنجاح' : 'Your transaction has been confirmed successfully'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TransactionNumber, {\n        onClick: handleCopyTransactionNumber,\n        children: [t('transactionNumber'), \": \", transaction.transactionNumber, copied && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.9rem',\n            marginTop: '5px',\n            color: '#28a745'\n          },\n          children: isRTL ? 'تم النسخ!' : 'Copied!'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TransactionDetails, {\n        isRTL: isRTL,\n        children: [/*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: [t('customerName'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: customerName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: [t('displayNumber', {\n              number: selectedDisplay.displayNumber\n            }), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: selectedDisplay.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: [t('amount'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: formatCurrency(transaction.amount || 50, 'SAR', isRTL ? 'ar-SA' : 'en-US')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: [t('duration'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: [Math.floor((transaction.duration || 300) / 60), \" \", t('minutes')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: [t('startTime'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: formatDate(transaction.startTime || new Date(), isRTL ? 'ar-SA' : 'en-US')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DisplayPreview, {\n        children: [/*#__PURE__*/_jsxDEV(DisplayName, {\n          children: customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DisplayInfo, {\n          children: [isRTL ? 'يتم عرض اسمك الآن على' : 'Your name is now displayed on', \" \", selectedDisplay.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CountdownTimer, {\n        children: isRTL ? `سيتم توجيهك للصفحة الرئيسية خلال ${countdown} ثانية` : `Redirecting to home page in ${countdown} seconds`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNewTransaction,\n          children: isRTL ? 'معاملة جديدة' : 'New Transaction'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n          onClick: handleGoHome,\n          children: isRTL ? 'الصفحة الرئيسية' : 'Home Page'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(SuccessPage, \"89yaKBIpCvaLdLQ2PjglF+f5zls=\", false, function () {\n  return [useTranslation, useNavigate, useLocation];\n});\n_c18 = SuccessPage;\nexport default SuccessPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"SuccessIcon\");\n$RefreshReg$(_c4, \"CheckmarkSVG\");\n$RefreshReg$(_c5, \"CheckmarkPath\");\n$RefreshReg$(_c6, \"Title\");\n$RefreshReg$(_c7, \"Subtitle\");\n$RefreshReg$(_c8, \"TransactionDetails\");\n$RefreshReg$(_c9, \"DetailRow\");\n$RefreshReg$(_c0, \"DetailLabel\");\n$RefreshReg$(_c1, \"DetailValue\");\n$RefreshReg$(_c10, \"TransactionNumber\");\n$RefreshReg$(_c11, \"DisplayPreview\");\n$RefreshReg$(_c12, \"DisplayName\");\n$RefreshReg$(_c13, \"DisplayInfo\");\n$RefreshReg$(_c14, \"CountdownTimer\");\n$RefreshReg$(_c15, \"ButtonGroup\");\n$RefreshReg$(_c16, \"Button\");\n$RefreshReg$(_c17, \"SecondaryButton\");\n$RefreshReg$(_c18, \"SuccessPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useTranslation", "useNavigate", "useLocation", "styled", "keyframes", "formatCurrency", "formatDate", "copyToClipboard", "jsxDEV", "_jsxDEV", "checkmark", "fadeIn", "Container", "div", "props", "isRTL", "_c", "Card", "_c2", "SuccessIcon", "_c3", "CheckmarkSVG", "svg", "_c4", "CheckmarkPath", "path", "_c5", "Title", "h1", "_c6", "Subtitle", "p", "_c7", "TransactionDetails", "_c8", "DetailRow", "_c9", "DetailLabel", "span", "_c0", "DetailValue", "_c1", "TransactionNumber", "_c10", "DisplayPreview", "_c11", "DisplayName", "_c12", "DisplayInfo", "_c13", "CountdownTimer", "_c14", "ButtonGroup", "_c15", "<PERSON><PERSON>", "button", "_c16", "SecondaryButton", "_c17", "SuccessPage", "_s", "t", "i18n", "navigate", "location", "countdown", "setCountdown", "copied", "setCopied", "language", "transaction", "customerName", "selectedDisplay", "state", "timer", "setInterval", "prev", "clearInterval", "handleCopyTransactionNumber", "transactionNumber", "success", "setTimeout", "handleNewTransaction", "handleGoHome", "children", "viewBox", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "onClick", "style", "fontSize", "marginTop", "color", "number", "displayNumber", "name", "amount", "Math", "floor", "duration", "startTime", "Date", "_c18", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/SuccessPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { formatCurrency, formatDate, copyToClipboard } from '../utils/helpers';\n\nconst checkmark = keyframes`\n  0% {\n    stroke-dashoffset: 100;\n  }\n  100% {\n    stroke-dashoffset: 0;\n  }\n`;\n\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n  animation: ${fadeIn} 0.6s ease-out;\n`;\n\nconst SuccessIcon = styled.div`\n  width: 120px;\n  height: 120px;\n  margin: 0 auto 30px;\n  position: relative;\n`;\n\nconst CheckmarkSVG = styled.svg`\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  display: block;\n  stroke-width: 3;\n  stroke: #28a745;\n  stroke-miterlimit: 10;\n  box-shadow: inset 0px 0px 0px #28a745;\n  animation: ${checkmark} 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;\n`;\n\nconst CheckmarkPath = styled.path`\n  stroke-dasharray: 100;\n  stroke-dashoffset: 100;\n`;\n\nconst Title = styled.h1`\n  font-size: 2.5rem;\n  color: #28a745;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  color: #666;\n  margin-bottom: 30px;\n`;\n\nconst TransactionDetails = styled.div`\n  background: #f8f9fa;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n`;\n\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n\n  &:last-child {\n    margin-bottom: 0;\n    border-bottom: none;\n  }\n`;\n\nconst DetailLabel = styled.span`\n  font-weight: 600;\n  color: #333;\n`;\n\nconst DetailValue = styled.span`\n  color: #666;\n  font-weight: 500;\n`;\n\nconst TransactionNumber = styled.div`\n  background: #e3f2fd;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1976d2;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background: #bbdefb;\n  }\n`;\n\nconst DisplayPreview = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 15px;\n  margin: 20px 0;\n  position: relative;\n  overflow: hidden;\n`;\n\nconst DisplayName = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n`;\n\nconst DisplayInfo = styled.div`\n  font-size: 1rem;\n  opacity: 0.9;\n`;\n\nconst CountdownTimer = styled.div`\n  background: #fff3cd;\n  color: #856404;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n  font-size: 1.1rem;\n  font-weight: 600;\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 30px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n`;\n\nconst SecondaryButton = styled(Button)`\n  background: #6c757d;\n  \n  &:hover {\n    background: #5a6268;\n    box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);\n  }\n`;\n\nconst SuccessPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [countdown, setCountdown] = useState(30);\n  const [copied, setCopied] = useState(false);\n\n  const isRTL = i18n.language === 'ar';\n  const { transaction, customerName, selectedDisplay } = location.state || {};\n\n  useEffect(() => {\n    // التحقق من وجود البيانات المطلوبة\n    if (!transaction || !customerName || !selectedDisplay) {\n      navigate('/');\n      return;\n    }\n\n    // العد التنازلي للعودة للصفحة الرئيسية\n    const timer = setInterval(() => {\n      setCountdown(prev => {\n        if (prev <= 1) {\n          navigate('/');\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [transaction, customerName, selectedDisplay, navigate]);\n\n  const handleCopyTransactionNumber = async () => {\n    if (transaction?.transactionNumber) {\n      const success = await copyToClipboard(transaction.transactionNumber);\n      if (success) {\n        setCopied(true);\n        setTimeout(() => setCopied(false), 2000);\n      }\n    }\n  };\n\n  const handleNewTransaction = () => {\n    navigate('/select-display');\n  };\n\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  if (!transaction || !customerName || !selectedDisplay) {\n    return null;\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <SuccessIcon>\n          <CheckmarkSVG viewBox=\"0 0 100 100\">\n            <circle\n              cx=\"50\"\n              cy=\"50\"\n              r=\"45\"\n              fill=\"none\"\n              stroke=\"#28a745\"\n              strokeWidth=\"3\"\n            />\n            <CheckmarkPath\n              fill=\"none\"\n              d=\"M25,50 L40,65 L75,30\"\n            />\n          </CheckmarkSVG>\n        </SuccessIcon>\n\n        <Title>{t('paymentSuccessful')}</Title>\n        <Subtitle>\n          {isRTL ? 'تم تأكيد معاملتك بنجاح' : 'Your transaction has been confirmed successfully'}\n        </Subtitle>\n\n        <TransactionNumber onClick={handleCopyTransactionNumber}>\n          {t('transactionNumber')}: {transaction.transactionNumber}\n          {copied && (\n            <div style={{ fontSize: '0.9rem', marginTop: '5px', color: '#28a745' }}>\n              {isRTL ? 'تم النسخ!' : 'Copied!'}\n            </div>\n          )}\n        </TransactionNumber>\n\n        <TransactionDetails isRTL={isRTL}>\n          <DetailRow>\n            <DetailLabel>{t('customerName')}:</DetailLabel>\n            <DetailValue>{customerName}</DetailValue>\n          </DetailRow>\n          <DetailRow>\n            <DetailLabel>{t('displayNumber', { number: selectedDisplay.displayNumber })}:</DetailLabel>\n            <DetailValue>{selectedDisplay.name}</DetailValue>\n          </DetailRow>\n          <DetailRow>\n            <DetailLabel>{t('amount')}:</DetailLabel>\n            <DetailValue>{formatCurrency(transaction.amount || 50, 'SAR', isRTL ? 'ar-SA' : 'en-US')}</DetailValue>\n          </DetailRow>\n          <DetailRow>\n            <DetailLabel>{t('duration')}:</DetailLabel>\n            <DetailValue>{Math.floor((transaction.duration || 300) / 60)} {t('minutes')}</DetailValue>\n          </DetailRow>\n          <DetailRow>\n            <DetailLabel>{t('startTime')}:</DetailLabel>\n            <DetailValue>{formatDate(transaction.startTime || new Date(), isRTL ? 'ar-SA' : 'en-US')}</DetailValue>\n          </DetailRow>\n        </TransactionDetails>\n\n        <DisplayPreview>\n          <DisplayName>{customerName}</DisplayName>\n          <DisplayInfo>\n            {isRTL ? 'يتم عرض اسمك الآن على' : 'Your name is now displayed on'} {selectedDisplay.name}\n          </DisplayInfo>\n        </DisplayPreview>\n\n        <CountdownTimer>\n          {isRTL ? \n            `سيتم توجيهك للصفحة الرئيسية خلال ${countdown} ثانية` :\n            `Redirecting to home page in ${countdown} seconds`\n          }\n        </CountdownTimer>\n\n        <ButtonGroup>\n          <Button onClick={handleNewTransaction}>\n            {isRTL ? 'معاملة جديدة' : 'New Transaction'}\n          </Button>\n          <SecondaryButton onClick={handleGoHome}>\n            {isRTL ? 'الصفحة الرئيسية' : 'Home Page'}\n          </SecondaryButton>\n        </ButtonGroup>\n      </Card>\n    </Container>\n  );\n};\n\nexport default SuccessPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,cAAc,EAAEC,UAAU,EAAEC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,SAAS,GAAGN,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,MAAM,GAAGP,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMQ,SAAS,GAAGT,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;AACnD,CAAC;AAACC,EAAA,GARIJ,SAAS;AAUf,MAAMK,IAAI,GAAGd,MAAM,CAACU,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,MAAM;AACrB,CAAC;AAACO,GAAA,GATID,IAAI;AAWV,MAAME,WAAW,GAAGhB,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GALID,WAAW;AAOjB,MAAME,YAAY,GAAGlB,MAAM,CAACmB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeZ,SAAS;AACxB,CAAC;AAACa,GAAA,GAVIF,YAAY;AAYlB,MAAMG,aAAa,GAAGrB,MAAM,CAACsB,IAAI;AACjC;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,aAAa;AAKnB,MAAMG,KAAK,GAAGxB,MAAM,CAACyB,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,QAAQ,GAAG3B,MAAM,CAAC4B,CAAC;AACzB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,QAAQ;AAMd,MAAMG,kBAAkB,GAAG9B,MAAM,CAACU,GAAG;AACrC;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,OAAO,GAAG,MAAM;AACvD,CAAC;AAACmB,GAAA,GANID,kBAAkB;AAQxB,MAAME,SAAS,GAAGhC,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAZID,SAAS;AAcf,MAAME,WAAW,GAAGlC,MAAM,CAACmC,IAAI;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,WAAW;AAKjB,MAAMG,WAAW,GAAGrC,MAAM,CAACmC,IAAI;AAC/B;AACA;AACA,CAAC;AAACG,GAAA,GAHID,WAAW;AAKjB,MAAME,iBAAiB,GAAGvC,MAAM,CAACU,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GAdID,iBAAiB;AAgBvB,MAAME,cAAc,GAAGzC,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GARID,cAAc;AAUpB,MAAME,WAAW,GAAG3C,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAG7C,MAAM,CAACU,GAAG;AAC9B;AACA;AACA,CAAC;AAACoC,IAAA,GAHID,WAAW;AAKjB,MAAME,cAAc,GAAG/C,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GARID,cAAc;AAUpB,MAAME,WAAW,GAAGjD,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GALID,WAAW;AAOjB,MAAME,MAAM,GAAGnD,MAAM,CAACoD,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAfIF,MAAM;AAiBZ,MAAMG,eAAe,GAAGtD,MAAM,CAACmD,MAAM,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,IAAA,GAPID,eAAe;AASrB,MAAME,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAG9D,cAAc,CAAC,CAAC;EACpC,MAAM+D,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAC9B,MAAM+D,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMgB,KAAK,GAAG+C,IAAI,CAACO,QAAQ,KAAK,IAAI;EACpC,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGR,QAAQ,CAACS,KAAK,IAAI,CAAC,CAAC;EAE3E3E,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACwE,WAAW,IAAI,CAACC,YAAY,IAAI,CAACC,eAAe,EAAE;MACrDT,QAAQ,CAAC,GAAG,CAAC;MACb;IACF;;IAEA;IACA,MAAMW,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,YAAY,CAACU,IAAI,IAAI;QACnB,IAAIA,IAAI,IAAI,CAAC,EAAE;UACbb,QAAQ,CAAC,GAAG,CAAC;UACb,OAAO,CAAC;QACV;QACA,OAAOa,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACJ,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAET,QAAQ,CAAC,CAAC;EAE1D,MAAMe,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAIR,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAES,iBAAiB,EAAE;MAClC,MAAMC,OAAO,GAAG,MAAMzE,eAAe,CAAC+D,WAAW,CAACS,iBAAiB,CAAC;MACpE,IAAIC,OAAO,EAAE;QACXZ,SAAS,CAAC,IAAI,CAAC;QACfa,UAAU,CAAC,MAAMb,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC1C;IACF;EACF,CAAC;EAED,MAAMc,oBAAoB,GAAGA,CAAA,KAAM;IACjCnB,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzBpB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,IAAI,CAACO,WAAW,IAAI,CAACC,YAAY,IAAI,CAACC,eAAe,EAAE;IACrD,OAAO,IAAI;EACb;EAEA,oBACE/D,OAAA,CAACG,SAAS;IAACG,KAAK,EAAEA,KAAM;IAAAqE,QAAA,eACtB3E,OAAA,CAACQ,IAAI;MAAAmE,QAAA,gBACH3E,OAAA,CAACU,WAAW;QAAAiE,QAAA,eACV3E,OAAA,CAACY,YAAY;UAACgE,OAAO,EAAC,aAAa;UAAAD,QAAA,gBACjC3E,OAAA;YACE6E,EAAE,EAAC,IAAI;YACPC,EAAE,EAAC,IAAI;YACPC,CAAC,EAAC,IAAI;YACNC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFtF,OAAA,CAACe,aAAa;YACZiE,IAAI,EAAC,MAAM;YACXO,CAAC,EAAC;UAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEdtF,OAAA,CAACkB,KAAK;QAAAyD,QAAA,EAAEvB,CAAC,CAAC,mBAAmB;MAAC;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvCtF,OAAA,CAACqB,QAAQ;QAAAsD,QAAA,EACNrE,KAAK,GAAG,wBAAwB,GAAG;MAAkD;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eAEXtF,OAAA,CAACiC,iBAAiB;QAACuD,OAAO,EAAEnB,2BAA4B;QAAAM,QAAA,GACrDvB,CAAC,CAAC,mBAAmB,CAAC,EAAC,IAAE,EAACS,WAAW,CAACS,iBAAiB,EACvDZ,MAAM,iBACL1D,OAAA;UAAKyF,KAAK,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,SAAS,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAjB,QAAA,EACpErE,KAAK,GAAG,WAAW,GAAG;QAAS;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACgB,CAAC,eAEpBtF,OAAA,CAACwB,kBAAkB;QAAClB,KAAK,EAAEA,KAAM;QAAAqE,QAAA,gBAC/B3E,OAAA,CAAC0B,SAAS;UAAAiD,QAAA,gBACR3E,OAAA,CAAC4B,WAAW;YAAA+C,QAAA,GAAEvB,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/CtF,OAAA,CAAC+B,WAAW;YAAA4C,QAAA,EAAEb;UAAY;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACZtF,OAAA,CAAC0B,SAAS;UAAAiD,QAAA,gBACR3E,OAAA,CAAC4B,WAAW;YAAA+C,QAAA,GAAEvB,CAAC,CAAC,eAAe,EAAE;cAAEyC,MAAM,EAAE9B,eAAe,CAAC+B;YAAc,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3FtF,OAAA,CAAC+B,WAAW;YAAA4C,QAAA,EAAEZ,eAAe,CAACgC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACZtF,OAAA,CAAC0B,SAAS;UAAAiD,QAAA,gBACR3E,OAAA,CAAC4B,WAAW;YAAA+C,QAAA,GAAEvB,CAAC,CAAC,QAAQ,CAAC,EAAC,GAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzCtF,OAAA,CAAC+B,WAAW;YAAA4C,QAAA,EAAE/E,cAAc,CAACiE,WAAW,CAACmC,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE1F,KAAK,GAAG,OAAO,GAAG,OAAO;UAAC;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,eACZtF,OAAA,CAAC0B,SAAS;UAAAiD,QAAA,gBACR3E,OAAA,CAAC4B,WAAW;YAAA+C,QAAA,GAAEvB,CAAC,CAAC,UAAU,CAAC,EAAC,GAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC3CtF,OAAA,CAAC+B,WAAW;YAAA4C,QAAA,GAAEsB,IAAI,CAACC,KAAK,CAAC,CAACrC,WAAW,CAACsC,QAAQ,IAAI,GAAG,IAAI,EAAE,CAAC,EAAC,GAAC,EAAC/C,CAAC,CAAC,SAAS,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACZtF,OAAA,CAAC0B,SAAS;UAAAiD,QAAA,gBACR3E,OAAA,CAAC4B,WAAW;YAAA+C,QAAA,GAAEvB,CAAC,CAAC,WAAW,CAAC,EAAC,GAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC5CtF,OAAA,CAAC+B,WAAW;YAAA4C,QAAA,EAAE9E,UAAU,CAACgE,WAAW,CAACuC,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,EAAE/F,KAAK,GAAG,OAAO,GAAG,OAAO;UAAC;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAErBtF,OAAA,CAACmC,cAAc;QAAAwC,QAAA,gBACb3E,OAAA,CAACqC,WAAW;UAAAsC,QAAA,EAAEb;QAAY;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACzCtF,OAAA,CAACuC,WAAW;UAAAoC,QAAA,GACTrE,KAAK,GAAG,uBAAuB,GAAG,+BAA+B,EAAC,GAAC,EAACyD,eAAe,CAACgC,IAAI;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEjBtF,OAAA,CAACyC,cAAc;QAAAkC,QAAA,EACZrE,KAAK,GACJ,oCAAoCkD,SAAS,QAAQ,GACrD,+BAA+BA,SAAS;MAAU;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtC,CAAC,eAEjBtF,OAAA,CAAC2C,WAAW;QAAAgC,QAAA,gBACV3E,OAAA,CAAC6C,MAAM;UAAC2C,OAAO,EAAEf,oBAAqB;UAAAE,QAAA,EACnCrE,KAAK,GAAG,cAAc,GAAG;QAAiB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACTtF,OAAA,CAACgD,eAAe;UAACwC,OAAO,EAAEd,YAAa;UAAAC,QAAA,EACpCrE,KAAK,GAAG,iBAAiB,GAAG;QAAW;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACnC,EAAA,CAvIID,WAAW;EAAA,QACK3D,cAAc,EACjBC,WAAW,EACXC,WAAW;AAAA;AAAA6G,IAAA,GAHxBpD,WAAW;AAyIjB,eAAeA,WAAW;AAAC,IAAA3C,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAqD,IAAA;AAAAC,YAAA,CAAAhG,EAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}