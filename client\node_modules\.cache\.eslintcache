[{"D:\\برمجة\\tste 1\\client\\src\\index.js": "1", "D:\\برمجة\\tste 1\\client\\src\\App.js": "2", "D:\\برمجة\\tste 1\\client\\src\\reportWebVitals.js": "3", "D:\\برمجة\\tste 1\\client\\src\\hooks\\useAuth.js": "4", "D:\\برمجة\\tste 1\\client\\src\\pages\\DisplaySelectionPage.js": "5", "D:\\برمجة\\tste 1\\client\\src\\pages\\LoginPage.js": "6", "D:\\برمجة\\tste 1\\client\\src\\pages\\HomePage.js": "7", "D:\\برمجة\\tste 1\\client\\src\\utils\\i18n.js": "8", "D:\\برمجة\\tste 1\\client\\src\\utils\\helpers.js": "9", "D:\\برمجة\\tste 1\\client\\src\\services\\api.js": "10", "D:\\برمجة\\tste 1\\client\\src\\pages\\SuccessPage.js": "11", "D:\\برمجة\\tste 1\\client\\src\\pages\\OwnerLoginPage.js": "12", "D:\\برمجة\\tste 1\\client\\src\\pages\\PaymentPage.js": "13", "D:\\برمجة\\tste 1\\client\\src\\pages\\NameInputPage.js": "14", "D:\\برمجة\\tste 1\\client\\src\\pages\\OwnerDashboard.js": "15", "D:\\برمجة\\tste 1\\client\\src\\pages\\DisplayScreen.js": "16"}, {"size": 688, "mtime": 1752298107477, "results": "17", "hashOfConfig": "18"}, {"size": 1503, "mtime": 1752298719294, "results": "19", "hashOfConfig": "18"}, {"size": 362, "mtime": 1752297056276, "results": "20", "hashOfConfig": "18"}, {"size": 4700, "mtime": 1752297983359, "results": "21", "hashOfConfig": "18"}, {"size": 7297, "mtime": 1752298040070, "results": "22", "hashOfConfig": "18"}, {"size": 9397, "mtime": 1752298080443, "results": "23", "hashOfConfig": "18"}, {"size": 4158, "mtime": 1752298007467, "results": "24", "hashOfConfig": "18"}, {"size": 6313, "mtime": 1752297927273, "results": "25", "hashOfConfig": "18"}, {"size": 7465, "mtime": 1752297961277, "results": "26", "hashOfConfig": "18"}, {"size": 5105, "mtime": 1752297894911, "results": "27", "hashOfConfig": "18"}, {"size": 8320, "mtime": 1752298528656, "results": "28", "hashOfConfig": "18"}, {"size": 4955, "mtime": 1752298555376, "results": "29", "hashOfConfig": "18"}, {"size": 10100, "mtime": 1752298489483, "results": "30", "hashOfConfig": "18"}, {"size": 9801, "mtime": 1752298446531, "results": "31", "hashOfConfig": "18"}, {"size": 10846, "mtime": 1752298635734, "results": "32", "hashOfConfig": "18"}, {"size": 7962, "mtime": 1752298698702, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "y5f92p", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\برمجة\\tste 1\\client\\src\\index.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\App.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\reportWebVitals.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\hooks\\useAuth.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\DisplaySelectionPage.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\LoginPage.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\HomePage.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\utils\\i18n.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\utils\\helpers.js", ["82", "83", "84", "85", "86"], [], "D:\\برمجة\\tste 1\\client\\src\\services\\api.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\SuccessPage.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\OwnerLoginPage.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\PaymentPage.js", ["87"], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\NameInputPage.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\OwnerDashboard.js", [], [], "D:\\برمجة\\tste 1\\client\\src\\pages\\DisplayScreen.js", ["88", "89", "90", "91"], [], {"ruleId": "92", "severity": 1, "message": "93", "line": 4, "column": 49, "nodeType": "94", "messageId": "95", "endLine": 4, "endColumn": 50, "suggestions": "96"}, {"ruleId": "92", "severity": 1, "message": "97", "line": 4, "column": 51, "nodeType": "94", "messageId": "95", "endLine": 4, "endColumn": 52, "suggestions": "98"}, {"ruleId": "92", "severity": 1, "message": "93", "line": 22, "column": 49, "nodeType": "94", "messageId": "95", "endLine": 22, "endColumn": 50, "suggestions": "99"}, {"ruleId": "92", "severity": 1, "message": "97", "line": 22, "column": 51, "nodeType": "94", "messageId": "95", "endLine": 22, "endColumn": 52, "suggestions": "100"}, {"ruleId": "92", "severity": 1, "message": "101", "line": 101, "column": 27, "nodeType": "94", "messageId": "95", "endLine": 101, "endColumn": 28, "suggestions": "102"}, {"ruleId": "103", "severity": 1, "message": "104", "line": 214, "column": 6, "nodeType": "105", "endLine": 214, "endColumn": 75, "suggestions": "106"}, {"ruleId": "107", "severity": 1, "message": "108", "line": 168, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 168, "endColumn": 16}, {"ruleId": "103", "severity": 1, "message": "111", "line": 190, "column": 6, "nodeType": "105", "endLine": 190, "endColumn": 17, "suggestions": "112"}, {"ruleId": "103", "severity": 1, "message": "111", "line": 198, "column": 6, "nodeType": "105", "endLine": 198, "endColumn": 17, "suggestions": "113"}, {"ruleId": "103", "severity": 1, "message": "111", "line": 214, "column": 6, "nodeType": "105", "endLine": 214, "endColumn": 19, "suggestions": "114"}, "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["115", "116"], "Unnecessary escape character: \\).", ["117", "118"], ["119", "120"], ["121", "122"], "Unnecessary escape character: \\\".", ["123", "124"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'createTransaction'. Either include it or remove the dependency array.", "ArrayExpression", ["125"], "no-unused-vars", "'socket' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchDisplayData'. Either include it or remove the dependency array.", ["126"], ["127"], ["128"], {"messageId": "129", "fix": "130", "desc": "131"}, {"messageId": "132", "fix": "133", "desc": "134"}, {"messageId": "129", "fix": "135", "desc": "131"}, {"messageId": "132", "fix": "136", "desc": "134"}, {"messageId": "129", "fix": "137", "desc": "131"}, {"messageId": "132", "fix": "138", "desc": "134"}, {"messageId": "129", "fix": "139", "desc": "131"}, {"messageId": "132", "fix": "140", "desc": "134"}, {"messageId": "129", "fix": "141", "desc": "131"}, {"messageId": "132", "fix": "142", "desc": "134"}, {"desc": "143", "fix": "144"}, {"desc": "145", "fix": "146"}, {"desc": "145", "fix": "147"}, {"desc": "148", "fix": "149"}, "removeEscape", {"range": "150", "text": "151"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "152", "text": "153"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "154", "text": "151"}, {"range": "155", "text": "153"}, {"range": "156", "text": "151"}, {"range": "157", "text": "153"}, {"range": "158", "text": "151"}, {"range": "159", "text": "153"}, {"range": "160", "text": "151"}, {"range": "161", "text": "153"}, "Update the dependencies array to be: [selectedDisplay, customer, customerName, amount, duration, navigate, createTransaction]", {"range": "162", "text": "163"}, "Update the dependencies array to be: [displayId, fetchDisplayData]", {"range": "164", "text": "165"}, {"range": "166", "text": "165"}, "Update the dependencies array to be: [displayData, fetchDisplayData]", {"range": "167", "text": "168"}, [161, 162], "", [161, 161], "\\", [163, 164], [163, 163], [766, 767], [766, 766], [768, 769], [768, 768], [2867, 2868], [2867, 2867], [4907, 4976], "[selectedDisplay, customer, customerName, amount, duration, navigate, createTransaction]", [4061, 4072], "[displayId, fetchDisplayData]", [4264, 4275], [4717, 4730], "[displayData, fetchDisplayData]"]