{"name": "web-vitals", "version": "2.1.4", "description": "Easily measure performance metrics in JavaScript", "main": "dist/web-vitals.umd.js", "module": "dist/web-vitals.js", "typings": "dist/modules/index.d.ts", "files": ["base.js", "base.d.ts", "dist", "src"], "scripts": {"build": "run-s clean build:ts build:js", "build:ts": "tsc -b", "build:js": "rollup -c", "clean": "rm -rf dist tsconfig.tsbuildinfo", "dev": "run-p watch test:server", "lint": "eslint \"*.js\" \"src/**/*.ts\" \"test/**/*.js\"", "lint:fix": "eslint --fix \"*.js\" \"src/**/*.ts\" \"test/**/*.js\"", "postversion": "git push --follow-tags", "release:major": "npm version major -m 'Release v%s' && npm publish", "release:minor": "npm version minor -m 'Release v%s' && npm publish", "release:patch": "npm version patch -m 'Release v%s' && npm publish", "test": "npm-run-all build -p -r test:*", "test:e2e": "wdio wdio.conf.js", "test:server": "node test/server.js", "start": "run-s build:ts test:server watch", "watch": "run-p watch:*", "watch:ts": "tsc -b -w", "watch:js": "rollup -c -w", "version": "run-s build", "prepare": "husky install"}, "keywords": ["crux", "performance", "metrics", "CLS", "FCP", "FID", "LCP", "TTFB"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://philipwalton.com"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/GoogleChrome/web-vitals.git"}, "bugs": {"url": "https://github.com/GoogleChrome/web-vitals/issues"}, "husky": {"hooks": {"pre-commit": "npm run lint"}}, "devDependencies": {"@babel/core": "^7.16.10", "@babel/preset-env": "^7.16.11", "@rollup/plugin-replace": "^3.0.1", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "@wdio/cli": "^7.16.13", "@wdio/local-runner": "^7.16.13", "@wdio/mocha-framework": "^7.16.13", "@wdio/selenium-standalone-service": "^7.16.13", "@wdio/spec-reporter": "^7.16.13", "body-parser": "^1.19.1", "chromedriver": "^97.0.0", "eslint": "^8.7.0", "eslint-config-google": "^0.14.0", "express": "^4.17.2", "fs-extra": "^10.0.0", "husky": "^7.0.4", "npm-run-all": "^4.1.5", "nunjucks": "^3.2.3", "rollup": "^2.64.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.5.5", "wdio-chromedriver-service": "^7.2.6"}}