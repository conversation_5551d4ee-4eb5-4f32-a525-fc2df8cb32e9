{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate,useLocation}from'react-router-dom';import styled,{keyframes}from'styled-components';import{transactionService,paymentService}from'../services/api';import{formatCurrency}from'../utils/helpers';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const pulse=keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n`;const spin=keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;const Container=styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Card=styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n`;const Title=styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 30px;\n`;const TransactionSummary=styled.div`\n  background: #f8f9fa;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n`;const SummaryRow=styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  font-size: 1.1rem;\n\n  &:last-child {\n    margin-bottom: 0;\n    padding-top: 15px;\n    border-top: 2px solid #ddd;\n    font-weight: bold;\n    font-size: 1.3rem;\n    color: #667eea;\n  }\n`;const PaymentMethods=styled.div`\n  display: grid;\n  gap: 15px;\n  margin-bottom: 30px;\n`;const PaymentMethod=styled.button`\n  background: ${props=>props.selected?'#667eea':'white'};\n  color: ${props=>props.selected?'white':'#333'};\n  border: 3px solid ${props=>props.selected?'#667eea':'#ddd'};\n  padding: 20px;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1.1rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props=>props.selected?'#5a6fd8':'#f8f9ff'};\n  }\n`;const NFCReader=styled.div`\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  padding: 40px;\n  border-radius: 20px;\n  margin: 20px 0;\n  animation: ${props=>props.active?pulse:'none'} 2s infinite;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${spin} 3s linear infinite;\n  }\n`;const NFCIcon=styled.div`\n  font-size: 4rem;\n  margin-bottom: 15px;\n  position: relative;\n  z-index: 1;\n`;const NFCText=styled.div`\n  font-size: 1.3rem;\n  font-weight: 600;\n  position: relative;\n  z-index: 1;\n`;const ProcessingSpinner=styled.div`\n  width: 50px;\n  height: 50px;\n  border: 5px solid #f3f3f3;\n  border-top: 5px solid #667eea;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin: 20px auto;\n`;const Button=styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px 40px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props=>props.disabled?0.6:1};\n  margin: 0 10px;\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;const CancelButton=styled(Button)`\n  background: #dc3545;\n  \n  &:hover:not(:disabled) {\n    background: #c82333;\n    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);\n  }\n`;const SuccessMessage=styled.div`\n  background: #28a745;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 20px 0;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n`;const PaymentPage=()=>{const{t,i18n}=useTranslation();const navigate=useNavigate();const location=useLocation();const[paymentMethod,setPaymentMethod]=useState('nfc');const[paymentStatus,setPaymentStatus]=useState('idle');// idle, processing, success, failed\nconst[transaction,setTransaction]=useState(null);const[error,setError]=useState(null);const isRTL=i18n.language==='ar';const{selectedDisplay,customer,customerName,amount,duration}=location.state||{};useEffect(()=>{// التحقق من وجود البيانات المطلوبة\nif(!selectedDisplay||!customer||!customerName){navigate('/select-display');return;}// إنشاء المعاملة\ncreateTransaction();},[selectedDisplay,customer,customerName,amount,duration,navigate]);const createTransaction=async()=>{try{const response=await transactionService.createTransaction(selectedDisplay.id,customerName,amount,duration);if(response.success){setTransaction(response.data);}else{setError(response.message||'فشل في إنشاء المعاملة');}}catch(error){setError('خطأ في الشبكة');console.error('Error creating transaction:',error);}};const handleNFCPayment=async()=>{if(!transaction)return;setPaymentStatus('processing');setError(null);try{// محاكاة دفع NFC\nconst response=await paymentService.simulateNFCPayment(transaction.transactionId);if(response.success){// تأكيد المعاملة\nconst confirmResponse=await transactionService.confirmTransaction(transaction.transactionId,response.data.paymentIntentId);if(confirmResponse.success){setPaymentStatus('success');// الانتقال لصفحة النجاح بعد 3 ثوان\nsetTimeout(()=>{navigate('/success',{state:{transaction:confirmResponse.data,customerName,selectedDisplay}});},3000);}else{setPaymentStatus('failed');setError(confirmResponse.message||'فشل في تأكيد المعاملة');}}else{setPaymentStatus('failed');setError(response.message||'فشل في الدفع');}}catch(error){setPaymentStatus('failed');setError('خطأ في معالجة الدفع');console.error('Payment error:',error);}};const handleCancel=()=>{navigate('/enter-name',{state:{selectedDisplay,customer}});};if(!selectedDisplay||!customer||!customerName){return null;}return/*#__PURE__*/_jsx(Container,{isRTL:isRTL,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Title,{children:t('payment')}),/*#__PURE__*/_jsxs(TransactionSummary,{children:[/*#__PURE__*/_jsxs(SummaryRow,{children:[/*#__PURE__*/_jsxs(\"span\",{children:[t('customerName'),\":\"]}),/*#__PURE__*/_jsx(\"span\",{children:customerName})]}),/*#__PURE__*/_jsxs(SummaryRow,{children:[/*#__PURE__*/_jsxs(\"span\",{children:[t('displayNumber',{number:selectedDisplay.displayNumber}),\":\"]}),/*#__PURE__*/_jsx(\"span\",{children:selectedDisplay.name})]}),/*#__PURE__*/_jsxs(SummaryRow,{children:[/*#__PURE__*/_jsxs(\"span\",{children:[t('duration'),\":\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[Math.floor(duration/60),\" \",t('minutes')]})]}),/*#__PURE__*/_jsxs(SummaryRow,{children:[/*#__PURE__*/_jsxs(\"span\",{children:[t('amount'),\":\"]}),/*#__PURE__*/_jsx(\"span\",{children:formatCurrency(amount,'SAR',isRTL?'ar-SA':'en-US')})]})]}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),paymentStatus==='success'&&/*#__PURE__*/_jsxs(SuccessMessage,{children:[t('paymentSuccessful'),/*#__PURE__*/_jsx(\"br\",{}),isRTL?'جاري التحويل...':'Redirecting...']}),paymentStatus==='idle'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'20px'},children:/*#__PURE__*/_jsx(\"h3\",{children:t('paymentMethod')})}),/*#__PURE__*/_jsx(PaymentMethods,{children:/*#__PURE__*/_jsxs(PaymentMethod,{selected:paymentMethod==='nfc',onClick:()=>setPaymentMethod('nfc'),children:[\"\\uD83D\\uDCF1 \",t('nfcPayment')]})}),paymentMethod==='nfc'&&/*#__PURE__*/_jsxs(NFCReader,{active:paymentStatus==='processing',children:[/*#__PURE__*/_jsx(NFCIcon,{children:\"\\uD83D\\uDCE1\"}),/*#__PURE__*/_jsx(NFCText,{children:paymentStatus==='processing'?t('processing'):t('placeCardOnReader')}),paymentStatus==='processing'&&/*#__PURE__*/_jsx(ProcessingSpinner,{})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'30px'},children:[/*#__PURE__*/_jsx(Button,{onClick:handleNFCPayment,disabled:paymentStatus==='processing'||!transaction,children:paymentStatus==='processing'?t('processing'):t('confirm')}),/*#__PURE__*/_jsx(CancelButton,{onClick:handleCancel,disabled:paymentStatus==='processing',children:t('cancel')})]})]}),paymentStatus==='failed'&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'20px'},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setPaymentStatus('idle'),children:t('retry')}),/*#__PURE__*/_jsx(CancelButton,{onClick:handleCancel,children:t('cancel')})]})]})});};export default PaymentPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useLocation", "styled", "keyframes", "transactionService", "paymentService", "formatCurrency", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "pulse", "spin", "Container", "div", "props", "isRTL", "Card", "Title", "h1", "TransactionSummary", "SummaryRow", "PaymentMethods", "PaymentMethod", "button", "selected", "<PERSON><PERSON><PERSON><PERSON>", "active", "NFCIcon", "NFCText", "ProcessingSpinner", "<PERSON><PERSON>", "disabled", "CancelButton", "SuccessMessage", "ErrorMessage", "PaymentPage", "t", "i18n", "navigate", "location", "paymentMethod", "setPaymentMethod", "paymentStatus", "setPaymentStatus", "transaction", "setTransaction", "error", "setError", "language", "selectedDisplay", "customer", "customerName", "amount", "duration", "state", "createTransaction", "response", "id", "success", "data", "message", "console", "handleNFCPayment", "simulateNFCPayment", "transactionId", "confirmResponse", "confirmTransaction", "paymentIntentId", "setTimeout", "handleCancel", "children", "number", "displayNumber", "name", "Math", "floor", "style", "marginBottom", "onClick", "marginTop"], "sources": ["D:/برمجة/tste 1/client/src/pages/PaymentPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled, { keyframes } from 'styled-components';\nimport { transactionService, paymentService } from '../services/api';\nimport { formatCurrency } from '../utils/helpers';\n\nconst pulse = keyframes`\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n`;\n\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 30px;\n`;\n\nconst TransactionSummary = styled.div`\n  background: #f8f9fa;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n`;\n\nconst SummaryRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  font-size: 1.1rem;\n\n  &:last-child {\n    margin-bottom: 0;\n    padding-top: 15px;\n    border-top: 2px solid #ddd;\n    font-weight: bold;\n    font-size: 1.3rem;\n    color: #667eea;\n  }\n`;\n\nconst PaymentMethods = styled.div`\n  display: grid;\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n\nconst PaymentMethod = styled.button`\n  background: ${props => props.selected ? '#667eea' : 'white'};\n  color: ${props => props.selected ? 'white' : '#333'};\n  border: 3px solid ${props => props.selected ? '#667eea' : '#ddd'};\n  padding: 20px;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1.1rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n\n  &:hover {\n    border-color: #667eea;\n    background: ${props => props.selected ? '#5a6fd8' : '#f8f9ff'};\n  }\n`;\n\nconst NFCReader = styled.div`\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  padding: 40px;\n  border-radius: 20px;\n  margin: 20px 0;\n  animation: ${props => props.active ? pulse : 'none'} 2s infinite;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n    animation: ${spin} 3s linear infinite;\n  }\n`;\n\nconst NFCIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 15px;\n  position: relative;\n  z-index: 1;\n`;\n\nconst NFCText = styled.div`\n  font-size: 1.3rem;\n  font-weight: 600;\n  position: relative;\n  z-index: 1;\n`;\n\nconst ProcessingSpinner = styled.div`\n  width: 50px;\n  height: 50px;\n  border: 5px solid #f3f3f3;\n  border-top: 5px solid #667eea;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin: 20px auto;\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px 40px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n  margin: 0 10px;\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst CancelButton = styled(Button)`\n  background: #dc3545;\n  \n  &:hover:not(:disabled) {\n    background: #c82333;\n    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);\n  }\n`;\n\nconst SuccessMessage = styled.div`\n  background: #28a745;\n  color: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin: 20px 0;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  margin: 20px 0;\n`;\n\nconst PaymentPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const [paymentMethod, setPaymentMethod] = useState('nfc');\n  const [paymentStatus, setPaymentStatus] = useState('idle'); // idle, processing, success, failed\n  const [transaction, setTransaction] = useState(null);\n  const [error, setError] = useState(null);\n\n  const isRTL = i18n.language === 'ar';\n  const { selectedDisplay, customer, customerName, amount, duration } = location.state || {};\n\n  useEffect(() => {\n    // التحقق من وجود البيانات المطلوبة\n    if (!selectedDisplay || !customer || !customerName) {\n      navigate('/select-display');\n      return;\n    }\n\n    // إنشاء المعاملة\n    createTransaction();\n  }, [selectedDisplay, customer, customerName, amount, duration, navigate]);\n\n  const createTransaction = async () => {\n    try {\n      const response = await transactionService.createTransaction(\n        selectedDisplay.id,\n        customerName,\n        amount,\n        duration\n      );\n\n      if (response.success) {\n        setTransaction(response.data);\n      } else {\n        setError(response.message || 'فشل في إنشاء المعاملة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error creating transaction:', error);\n    }\n  };\n\n  const handleNFCPayment = async () => {\n    if (!transaction) return;\n\n    setPaymentStatus('processing');\n    setError(null);\n\n    try {\n      // محاكاة دفع NFC\n      const response = await paymentService.simulateNFCPayment(transaction.transactionId);\n      \n      if (response.success) {\n        // تأكيد المعاملة\n        const confirmResponse = await transactionService.confirmTransaction(\n          transaction.transactionId,\n          response.data.paymentIntentId\n        );\n\n        if (confirmResponse.success) {\n          setPaymentStatus('success');\n          \n          // الانتقال لصفحة النجاح بعد 3 ثوان\n          setTimeout(() => {\n            navigate('/success', {\n              state: {\n                transaction: confirmResponse.data,\n                customerName,\n                selectedDisplay\n              }\n            });\n          }, 3000);\n        } else {\n          setPaymentStatus('failed');\n          setError(confirmResponse.message || 'فشل في تأكيد المعاملة');\n        }\n      } else {\n        setPaymentStatus('failed');\n        setError(response.message || 'فشل في الدفع');\n      }\n    } catch (error) {\n      setPaymentStatus('failed');\n      setError('خطأ في معالجة الدفع');\n      console.error('Payment error:', error);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/enter-name', {\n      state: { selectedDisplay, customer }\n    });\n  };\n\n  if (!selectedDisplay || !customer || !customerName) {\n    return null;\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Title>{t('payment')}</Title>\n\n        <TransactionSummary>\n          <SummaryRow>\n            <span>{t('customerName')}:</span>\n            <span>{customerName}</span>\n          </SummaryRow>\n          <SummaryRow>\n            <span>{t('displayNumber', { number: selectedDisplay.displayNumber })}:</span>\n            <span>{selectedDisplay.name}</span>\n          </SummaryRow>\n          <SummaryRow>\n            <span>{t('duration')}:</span>\n            <span>{Math.floor(duration / 60)} {t('minutes')}</span>\n          </SummaryRow>\n          <SummaryRow>\n            <span>{t('amount')}:</span>\n            <span>{formatCurrency(amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')}</span>\n          </SummaryRow>\n        </TransactionSummary>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        {paymentStatus === 'success' && (\n          <SuccessMessage>\n            {t('paymentSuccessful')}\n            <br />\n            {isRTL ? 'جاري التحويل...' : 'Redirecting...'}\n          </SuccessMessage>\n        )}\n\n        {paymentStatus === 'idle' && (\n          <>\n            <div style={{ marginBottom: '20px' }}>\n              <h3>{t('paymentMethod')}</h3>\n            </div>\n\n            <PaymentMethods>\n              <PaymentMethod\n                selected={paymentMethod === 'nfc'}\n                onClick={() => setPaymentMethod('nfc')}\n              >\n                📱 {t('nfcPayment')}\n              </PaymentMethod>\n            </PaymentMethods>\n\n            {paymentMethod === 'nfc' && (\n              <NFCReader active={paymentStatus === 'processing'}>\n                <NFCIcon>📡</NFCIcon>\n                <NFCText>\n                  {paymentStatus === 'processing' \n                    ? t('processing') \n                    : t('placeCardOnReader')\n                  }\n                </NFCText>\n                {paymentStatus === 'processing' && <ProcessingSpinner />}\n              </NFCReader>\n            )}\n\n            <div style={{ marginTop: '30px' }}>\n              <Button\n                onClick={handleNFCPayment}\n                disabled={paymentStatus === 'processing' || !transaction}\n              >\n                {paymentStatus === 'processing' ? t('processing') : t('confirm')}\n              </Button>\n              \n              <CancelButton\n                onClick={handleCancel}\n                disabled={paymentStatus === 'processing'}\n              >\n                {t('cancel')}\n              </CancelButton>\n            </div>\n          </>\n        )}\n\n        {paymentStatus === 'failed' && (\n          <div style={{ marginTop: '20px' }}>\n            <Button onClick={() => setPaymentStatus('idle')}>\n              {t('retry')}\n            </Button>\n            \n            <CancelButton onClick={handleCancel}>\n              {t('cancel')}\n            </CancelButton>\n          </div>\n        )}\n      </Card>\n    </Container>\n  );\n};\n\nexport default PaymentPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,MAAM,EAAIC,SAAS,KAAQ,mBAAmB,CACrD,OAASC,kBAAkB,CAAEC,cAAc,KAAQ,iBAAiB,CACpE,OAASC,cAAc,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAElD,KAAM,CAAAC,KAAK,CAAGV,SAAS;AACvB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,IAAI,CAAGX,SAAS;AACtB;AACA;AACA,CAAC,CAED,KAAM,CAAAY,SAAS,CAAGb,MAAM,CAACc,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGjB,MAAM,CAACc,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,KAAK,CAAGlB,MAAM,CAACmB,EAAE;AACvB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGpB,MAAM,CAACc,GAAG;AACrC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAO,UAAU,CAAGrB,MAAM,CAACc,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,cAAc,CAAGtB,MAAM,CAACc,GAAG;AACjC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,aAAa,CAAGvB,MAAM,CAACwB,MAAM;AACnC,gBAAgBT,KAAK,EAAIA,KAAK,CAACU,QAAQ,CAAG,SAAS,CAAG,OAAO;AAC7D,WAAWV,KAAK,EAAIA,KAAK,CAACU,QAAQ,CAAG,OAAO,CAAG,MAAM;AACrD,sBAAsBV,KAAK,EAAIA,KAAK,CAACU,QAAQ,CAAG,SAAS,CAAG,MAAM;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBV,KAAK,EAAIA,KAAK,CAACU,QAAQ,CAAG,SAAS,CAAG,SAAS;AACjE;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG1B,MAAM,CAACc,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACY,MAAM,CAAGhB,KAAK,CAAG,MAAM;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBC,IAAI;AACrB;AACA,CAAC,CAED,KAAM,CAAAgB,OAAO,CAAG5B,MAAM,CAACc,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAe,OAAO,CAAG7B,MAAM,CAACc,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAG9B,MAAM,CAACc,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,eAAeF,IAAI;AACnB;AACA,CAAC,CAED,KAAM,CAAAmB,MAAM,CAAG/B,MAAM,CAACwB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaT,KAAK,EAAIA,KAAK,CAACiB,QAAQ,CAAG,GAAG,CAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGjC,MAAM,CAAC+B,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,cAAc,CAAGlC,MAAM,CAACc,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAqB,YAAY,CAAGnC,MAAM,CAACc,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAsB,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGzC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA0C,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA0C,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAC0C,aAAa,CAAEC,gBAAgB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACgD,aAAa,CAAEC,gBAAgB,CAAC,CAAGjD,QAAQ,CAAC,MAAM,CAAC,CAAE;AAC5D,KAAM,CAACkD,WAAW,CAAEC,cAAc,CAAC,CAAGnD,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACoD,KAAK,CAAEC,QAAQ,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CAExC,KAAM,CAAAqB,KAAK,CAAGsB,IAAI,CAACW,QAAQ,GAAK,IAAI,CACpC,KAAM,CAAEC,eAAe,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,MAAM,CAAEC,QAAS,CAAC,CAAGd,QAAQ,CAACe,KAAK,EAAI,CAAC,CAAC,CAE1F3D,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACsD,eAAe,EAAI,CAACC,QAAQ,EAAI,CAACC,YAAY,CAAE,CAClDb,QAAQ,CAAC,iBAAiB,CAAC,CAC3B,OACF,CAEA;AACAiB,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAACN,eAAe,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,MAAM,CAAEC,QAAQ,CAAEf,QAAQ,CAAC,CAAC,CAEzE,KAAM,CAAAiB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAvD,kBAAkB,CAACsD,iBAAiB,CACzDN,eAAe,CAACQ,EAAE,CAClBN,YAAY,CACZC,MAAM,CACNC,QACF,CAAC,CAED,GAAIG,QAAQ,CAACE,OAAO,CAAE,CACpBb,cAAc,CAACW,QAAQ,CAACG,IAAI,CAAC,CAC/B,CAAC,IAAM,CACLZ,QAAQ,CAACS,QAAQ,CAACI,OAAO,EAAI,uBAAuB,CAAC,CACvD,CACF,CAAE,MAAOd,KAAK,CAAE,CACdC,QAAQ,CAAC,eAAe,CAAC,CACzBc,OAAO,CAACf,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAgB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAAClB,WAAW,CAAE,OAElBD,gBAAgB,CAAC,YAAY,CAAC,CAC9BI,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF;AACA,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAtD,cAAc,CAAC6D,kBAAkB,CAACnB,WAAW,CAACoB,aAAa,CAAC,CAEnF,GAAIR,QAAQ,CAACE,OAAO,CAAE,CACpB;AACA,KAAM,CAAAO,eAAe,CAAG,KAAM,CAAAhE,kBAAkB,CAACiE,kBAAkB,CACjEtB,WAAW,CAACoB,aAAa,CACzBR,QAAQ,CAACG,IAAI,CAACQ,eAChB,CAAC,CAED,GAAIF,eAAe,CAACP,OAAO,CAAE,CAC3Bf,gBAAgB,CAAC,SAAS,CAAC,CAE3B;AACAyB,UAAU,CAAC,IAAM,CACf9B,QAAQ,CAAC,UAAU,CAAE,CACnBgB,KAAK,CAAE,CACLV,WAAW,CAAEqB,eAAe,CAACN,IAAI,CACjCR,YAAY,CACZF,eACF,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACLN,gBAAgB,CAAC,QAAQ,CAAC,CAC1BI,QAAQ,CAACkB,eAAe,CAACL,OAAO,EAAI,uBAAuB,CAAC,CAC9D,CACF,CAAC,IAAM,CACLjB,gBAAgB,CAAC,QAAQ,CAAC,CAC1BI,QAAQ,CAACS,QAAQ,CAACI,OAAO,EAAI,cAAc,CAAC,CAC9C,CACF,CAAE,MAAOd,KAAK,CAAE,CACdH,gBAAgB,CAAC,QAAQ,CAAC,CAC1BI,QAAQ,CAAC,qBAAqB,CAAC,CAC/Bc,OAAO,CAACf,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAAuB,YAAY,CAAGA,CAAA,GAAM,CACzB/B,QAAQ,CAAC,aAAa,CAAE,CACtBgB,KAAK,CAAE,CAAEL,eAAe,CAAEC,QAAS,CACrC,CAAC,CAAC,CACJ,CAAC,CAED,GAAI,CAACD,eAAe,EAAI,CAACC,QAAQ,EAAI,CAACC,YAAY,CAAE,CAClD,MAAO,KAAI,CACb,CAEA,mBACE9C,IAAA,CAACO,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAAuD,QAAA,cACtB/D,KAAA,CAACS,IAAI,EAAAsD,QAAA,eACHjE,IAAA,CAACY,KAAK,EAAAqD,QAAA,CAAElC,CAAC,CAAC,SAAS,CAAC,CAAQ,CAAC,cAE7B7B,KAAA,CAACY,kBAAkB,EAAAmD,QAAA,eACjB/D,KAAA,CAACa,UAAU,EAAAkD,QAAA,eACT/D,KAAA,SAAA+D,QAAA,EAAOlC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAC,EAAM,CAAC,cACjC/B,IAAA,SAAAiE,QAAA,CAAOnB,YAAY,CAAO,CAAC,EACjB,CAAC,cACb5C,KAAA,CAACa,UAAU,EAAAkD,QAAA,eACT/D,KAAA,SAAA+D,QAAA,EAAOlC,CAAC,CAAC,eAAe,CAAE,CAAEmC,MAAM,CAAEtB,eAAe,CAACuB,aAAc,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,cAC7EnE,IAAA,SAAAiE,QAAA,CAAOrB,eAAe,CAACwB,IAAI,CAAO,CAAC,EACzB,CAAC,cACblE,KAAA,CAACa,UAAU,EAAAkD,QAAA,eACT/D,KAAA,SAAA+D,QAAA,EAAOlC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAC,EAAM,CAAC,cAC7B7B,KAAA,SAAA+D,QAAA,EAAOI,IAAI,CAACC,KAAK,CAACtB,QAAQ,CAAG,EAAE,CAAC,CAAC,GAAC,CAACjB,CAAC,CAAC,SAAS,CAAC,EAAO,CAAC,EAC7C,CAAC,cACb7B,KAAA,CAACa,UAAU,EAAAkD,QAAA,eACT/D,KAAA,SAAA+D,QAAA,EAAOlC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAC,EAAM,CAAC,cAC3B/B,IAAA,SAAAiE,QAAA,CAAOnE,cAAc,CAACiD,MAAM,CAAE,KAAK,CAAErC,KAAK,CAAG,OAAO,CAAG,OAAO,CAAC,CAAO,CAAC,EAC7D,CAAC,EACK,CAAC,CAEpB+B,KAAK,eAAIzC,IAAA,CAAC6B,YAAY,EAAAoC,QAAA,CAAExB,KAAK,CAAe,CAAC,CAE7CJ,aAAa,GAAK,SAAS,eAC1BnC,KAAA,CAAC0B,cAAc,EAAAqC,QAAA,EACZlC,CAAC,CAAC,mBAAmB,CAAC,cACvB/B,IAAA,QAAK,CAAC,CACLU,KAAK,CAAG,iBAAiB,CAAG,gBAAgB,EAC/B,CACjB,CAEA2B,aAAa,GAAK,MAAM,eACvBnC,KAAA,CAAAE,SAAA,EAAA6D,QAAA,eACEjE,IAAA,QAAKuE,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAP,QAAA,cACnCjE,IAAA,OAAAiE,QAAA,CAAKlC,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,CAC1B,CAAC,cAEN/B,IAAA,CAACgB,cAAc,EAAAiD,QAAA,cACb/D,KAAA,CAACe,aAAa,EACZE,QAAQ,CAAEgB,aAAa,GAAK,KAAM,CAClCsC,OAAO,CAAEA,CAAA,GAAMrC,gBAAgB,CAAC,KAAK,CAAE,CAAA6B,QAAA,EACxC,eACI,CAAClC,CAAC,CAAC,YAAY,CAAC,EACN,CAAC,CACF,CAAC,CAEhBI,aAAa,GAAK,KAAK,eACtBjC,KAAA,CAACkB,SAAS,EAACC,MAAM,CAAEgB,aAAa,GAAK,YAAa,CAAA4B,QAAA,eAChDjE,IAAA,CAACsB,OAAO,EAAA2C,QAAA,CAAC,cAAE,CAAS,CAAC,cACrBjE,IAAA,CAACuB,OAAO,EAAA0C,QAAA,CACL5B,aAAa,GAAK,YAAY,CAC3BN,CAAC,CAAC,YAAY,CAAC,CACfA,CAAC,CAAC,mBAAmB,CAAC,CAEnB,CAAC,CACTM,aAAa,GAAK,YAAY,eAAIrC,IAAA,CAACwB,iBAAiB,GAAE,CAAC,EAC/C,CACZ,cAEDtB,KAAA,QAAKqE,KAAK,CAAE,CAAEG,SAAS,CAAE,MAAO,CAAE,CAAAT,QAAA,eAChCjE,IAAA,CAACyB,MAAM,EACLgD,OAAO,CAAEhB,gBAAiB,CAC1B/B,QAAQ,CAAEW,aAAa,GAAK,YAAY,EAAI,CAACE,WAAY,CAAA0B,QAAA,CAExD5B,aAAa,GAAK,YAAY,CAAGN,CAAC,CAAC,YAAY,CAAC,CAAGA,CAAC,CAAC,SAAS,CAAC,CAC1D,CAAC,cAET/B,IAAA,CAAC2B,YAAY,EACX8C,OAAO,CAAET,YAAa,CACtBtC,QAAQ,CAAEW,aAAa,GAAK,YAAa,CAAA4B,QAAA,CAExClC,CAAC,CAAC,QAAQ,CAAC,CACA,CAAC,EACZ,CAAC,EACN,CACH,CAEAM,aAAa,GAAK,QAAQ,eACzBnC,KAAA,QAAKqE,KAAK,CAAE,CAAEG,SAAS,CAAE,MAAO,CAAE,CAAAT,QAAA,eAChCjE,IAAA,CAACyB,MAAM,EAACgD,OAAO,CAAEA,CAAA,GAAMnC,gBAAgB,CAAC,MAAM,CAAE,CAAA2B,QAAA,CAC7ClC,CAAC,CAAC,OAAO,CAAC,CACL,CAAC,cAET/B,IAAA,CAAC2B,YAAY,EAAC8C,OAAO,CAAET,YAAa,CAAAC,QAAA,CACjClC,CAAC,CAAC,QAAQ,CAAC,CACA,CAAC,EACZ,CACN,EACG,CAAC,CACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}