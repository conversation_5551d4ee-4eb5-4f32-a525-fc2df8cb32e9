{"ast": null, "code": "import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}", "map": {"version": 3, "names": ["useContext", "nodesToString", "Trans", "TransWithoutContext", "getI18n", "I18nContext", "_ref", "children", "count", "parent", "i18nKey", "context", "tOptions", "values", "defaults", "components", "ns", "i18n", "i18nFromProps", "t", "tFromProps", "shouldUnescape", "additionalProps", "i18nFromContext", "defaultNS", "defaultNSFromContext", "bind", "options"], "sources": ["D:/برمجة/tste 1/node_modules/react-i18next/dist/es/Trans.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,EAAEC,KAAK,IAAIC,mBAAmB,QAAQ,0BAA0B;AACtF,SAASC,OAAO,EAAEC,WAAW,QAAQ,cAAc;AACnD,SAASJ,aAAa;AACtB,OAAO,SAASC,KAAKA,CAAAI,IAAA,EAelB;EAAA,IAfmB;IACpBC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ,GAAG,CAAC,CAAC;IACbC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,EAAE;IACFC,IAAI,EAAEC,aAAa;IACnBC,CAAC,EAAEC,UAAU;IACbC,cAAc;IACd,GAAGC;EACL,CAAC,GAAAhB,IAAA;EACC,MAAM;IACJW,IAAI,EAAEM,eAAe;IACrBC,SAAS,EAAEC;EACb,CAAC,GAAGzB,UAAU,CAACK,WAAW,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMY,IAAI,GAAGC,aAAa,IAAIK,eAAe,IAAInB,OAAO,CAAC,CAAC;EAC1D,MAAMe,CAAC,GAAGC,UAAU,IAAIH,IAAI,EAAEE,CAAC,CAACO,IAAI,CAACT,IAAI,CAAC;EAC1C,OAAOd,mBAAmB,CAAC;IACzBI,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,EAAE,EAAEA,EAAE,IAAIG,CAAC,EAAEH,EAAE,IAAIS,oBAAoB,IAAIR,IAAI,EAAEU,OAAO,EAAEH,SAAS;IACnEP,IAAI;IACJE,CAAC,EAAEC,UAAU;IACbC,cAAc;IACd,GAAGC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}