{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useOTP, useAuth } from '../hooks/useAuth';\nimport { validateSaudiPhoneNumber, validateEmail, formatSaudiPhoneNumber } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n_c = Container;\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n`;\n_c2 = Card;\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n_c3 = Header;\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;\n_c4 = Title;\nconst DisplayInfo = styled.div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  text-align: center;\n`;\n_c5 = DisplayInfo;\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c6 = Form;\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n_c7 = InputGroup;\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1rem;\n`;\n_c8 = Label;\nconst Input = styled.input`\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  direction: ${props => props.type === 'tel' ? 'ltr' : 'inherit'};\n  text-align: ${props => props.type === 'tel' ? 'left' : 'inherit'};\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n\n  &:invalid {\n    border-color: #dc3545;\n  }\n`;\n_c9 = Input;\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n_c0 = Button;\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n_c1 = BackButton;\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n_c10 = ErrorMessage;\nconst SuccessMessage = styled.div`\n  background: #28a745;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n_c11 = SuccessMessage;\nconst OTPInfo = styled.div`\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n`;\n_c12 = OTPInfo;\nconst LoginPage = () => {\n  _s();\n  var _location$state;\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login\n  } = useAuth();\n  const {\n    loading,\n    error,\n    otpSent,\n    sendOTP,\n    verifyOTP,\n    resetOTP\n  } = useOTP();\n  const [step, setStep] = useState('phone'); // 'phone' or 'otp'\n  const [formData, setFormData] = useState({\n    phoneNumber: '',\n    email: '',\n    otpCode: ''\n  });\n  const [validationErrors, setValidationErrors] = useState({});\n  const isRTL = i18n.language === 'ar';\n  const selectedDisplay = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.selectedDisplay;\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // إزالة رسالة الخطأ عند التعديل\n    if (validationErrors[name]) {\n      setValidationErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (step === 'phone') {\n      if (!formData.phoneNumber) {\n        errors.phoneNumber = t('enterPhoneNumber');\n      } else if (!validateSaudiPhoneNumber(formData.phoneNumber)) {\n        errors.phoneNumber = t('invalidPhoneNumber');\n      }\n      if (!formData.email) {\n        errors.email = t('enterEmail');\n      } else if (!validateEmail(formData.email)) {\n        errors.email = t('invalidEmail');\n      }\n    } else if (step === 'otp') {\n      if (!formData.otpCode) {\n        errors.otpCode = t('enterOTP');\n      } else if (formData.otpCode.length !== 6) {\n        errors.otpCode = t('invalidOTP');\n      }\n    }\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleSendOTP = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    const response = await sendOTP(formattedPhone, formData.email, i18n.language);\n    if (response) {\n      setStep('otp');\n    }\n  };\n  const handleVerifyOTP = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    const response = await verifyOTP(formattedPhone, formData.otpCode);\n    if (response) {\n      // تسجيل الدخول\n      login(response.data.customer, response.data.token);\n\n      // الانتقال لصفحة إدخال الاسم\n      navigate('/enter-name', {\n        state: {\n          selectedDisplay,\n          customer: response.data.customer\n        }\n      });\n    }\n  };\n  const handleBack = () => {\n    if (step === 'otp') {\n      setStep('phone');\n      resetOTP();\n    } else {\n      navigate('/select-display');\n    }\n  };\n  const handleResendOTP = async () => {\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    await sendOTP(formattedPhone, formData.email, i18n.language);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    isRTL: isRTL,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: step === 'phone' ? isRTL ? 'تسجيل الدخول' : 'Login' : isRTL ? 'رمز التحقق' : 'Verification Code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), selectedDisplay && /*#__PURE__*/_jsxDEV(DisplayInfo, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: t('displayNumber', {\n              number: selectedDisplay.displayNumber\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), selectedDisplay.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 19\n      }, this), otpSent && step === 'otp' && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n        children: t('otpSent')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), step === 'phone' ? /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSendOTP,\n        children: [/*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: t('phoneNumber')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"tel\",\n            name: \"phoneNumber\",\n            value: formData.phoneNumber,\n            onChange: handleInputChange,\n            placeholder: t('enterPhoneNumber'),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), validationErrors.phoneNumber && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n            children: validationErrors.phoneNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: t('email')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            placeholder: t('enterEmail'),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), validationErrors.email && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n            children: validationErrors.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? t('processing') : t('sendOTP')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleVerifyOTP,\n        children: [/*#__PURE__*/_jsxDEV(OTPInfo, {\n          children: isRTL ? `تم إرسال رمز التحقق إلى: ${formData.email}` : `Verification code sent to: ${formData.email}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: t('otpCode')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            name: \"otpCode\",\n            value: formData.otpCode,\n            onChange: handleInputChange,\n            placeholder: t('enterOTP'),\n            maxLength: \"6\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), validationErrors.otpCode && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n            children: validationErrors.otpCode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? t('processing') : t('verifyOTP')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          onClick: handleResendOTP,\n          disabled: loading,\n          style: {\n            background: '#6c757d'\n          },\n          children: isRTL ? 'إعادة إرسال الرمز' : 'Resend Code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: handleBack,\n          children: t('back')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 261,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"TQ9141rUvQOaQq8lZ8HWnAzIU6I=\", false, function () {\n  return [useTranslation, useNavigate, useLocation, useAuth, useOTP];\n});\n_c13 = LoginPage;\nexport default LoginPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"Header\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"DisplayInfo\");\n$RefreshReg$(_c6, \"Form\");\n$RefreshReg$(_c7, \"InputGroup\");\n$RefreshReg$(_c8, \"Label\");\n$RefreshReg$(_c9, \"Input\");\n$RefreshReg$(_c0, \"Button\");\n$RefreshReg$(_c1, \"BackButton\");\n$RefreshReg$(_c10, \"ErrorMessage\");\n$RefreshReg$(_c11, \"SuccessMessage\");\n$RefreshReg$(_c12, \"OTPInfo\");\n$RefreshReg$(_c13, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useNavigate", "useLocation", "styled", "useOTP", "useAuth", "validateSaudiPhoneNumber", "validateEmail", "formatSaudiPhoneNumber", "jsxDEV", "_jsxDEV", "Container", "div", "props", "isRTL", "_c", "Card", "_c2", "Header", "_c3", "Title", "h1", "_c4", "DisplayInfo", "_c5", "Form", "form", "_c6", "InputGroup", "_c7", "Label", "label", "_c8", "Input", "input", "type", "_c9", "<PERSON><PERSON>", "button", "disabled", "_c0", "BackButton", "_c1", "ErrorMessage", "_c10", "SuccessMessage", "_c11", "OTPInfo", "_c12", "LoginPage", "_s", "_location$state", "t", "i18n", "navigate", "location", "login", "loading", "error", "otpSent", "sendOTP", "verifyOTP", "resetOTP", "step", "setStep", "formData", "setFormData", "phoneNumber", "email", "otpCode", "validationErrors", "setValidationErrors", "language", "selectedDisplay", "state", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "errors", "length", "Object", "keys", "handleSendOTP", "preventDefault", "formattedPhone", "response", "handleVerifyOTP", "data", "customer", "token", "handleBack", "handleResendOTP", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "number", "displayNumber", "onSubmit", "onChange", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "onClick", "style", "background", "marginTop", "textAlign", "_c13", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useOTP, useAuth } from '../hooks/useAuth';\nimport { validateSaudiPhoneNumber, validateEmail, formatSaudiPhoneNumber } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst DisplayInfo = styled.div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  text-align: center;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1rem;\n`;\n\nconst Input = styled.input`\n  padding: 15px;\n  border: 2px solid #ddd;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  direction: ${props => props.type === 'tel' ? 'ltr' : 'inherit'};\n  text-align: ${props => props.type === 'tel' ? 'left' : 'inherit'};\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n\n  &:invalid {\n    border-color: #dc3545;\n  }\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst SuccessMessage = styled.div`\n  background: #28a745;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst OTPInfo = styled.div`\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n`;\n\nconst LoginPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login } = useAuth();\n  const { loading, error, otpSent, sendOTP, verifyOTP, resetOTP } = useOTP();\n\n  const [step, setStep] = useState('phone'); // 'phone' or 'otp'\n  const [formData, setFormData] = useState({\n    phoneNumber: '',\n    email: '',\n    otpCode: ''\n  });\n  const [validationErrors, setValidationErrors] = useState({});\n\n  const isRTL = i18n.language === 'ar';\n  const selectedDisplay = location.state?.selectedDisplay;\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // إزالة رسالة الخطأ عند التعديل\n    if (validationErrors[name]) {\n      setValidationErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (step === 'phone') {\n      if (!formData.phoneNumber) {\n        errors.phoneNumber = t('enterPhoneNumber');\n      } else if (!validateSaudiPhoneNumber(formData.phoneNumber)) {\n        errors.phoneNumber = t('invalidPhoneNumber');\n      }\n\n      if (!formData.email) {\n        errors.email = t('enterEmail');\n      } else if (!validateEmail(formData.email)) {\n        errors.email = t('invalidEmail');\n      }\n    } else if (step === 'otp') {\n      if (!formData.otpCode) {\n        errors.otpCode = t('enterOTP');\n      } else if (formData.otpCode.length !== 6) {\n        errors.otpCode = t('invalidOTP');\n      }\n    }\n\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSendOTP = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    const response = await sendOTP(formattedPhone, formData.email, i18n.language);\n    \n    if (response) {\n      setStep('otp');\n    }\n  };\n\n  const handleVerifyOTP = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    const response = await verifyOTP(formattedPhone, formData.otpCode);\n    \n    if (response) {\n      // تسجيل الدخول\n      login(response.data.customer, response.data.token);\n      \n      // الانتقال لصفحة إدخال الاسم\n      navigate('/enter-name', {\n        state: {\n          selectedDisplay,\n          customer: response.data.customer\n        }\n      });\n    }\n  };\n\n  const handleBack = () => {\n    if (step === 'otp') {\n      setStep('phone');\n      resetOTP();\n    } else {\n      navigate('/select-display');\n    }\n  };\n\n  const handleResendOTP = async () => {\n    const formattedPhone = formatSaudiPhoneNumber(formData.phoneNumber);\n    await sendOTP(formattedPhone, formData.email, i18n.language);\n  };\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Header>\n          <Title>\n            {step === 'phone' ? \n              (isRTL ? 'تسجيل الدخول' : 'Login') : \n              (isRTL ? 'رمز التحقق' : 'Verification Code')\n            }\n          </Title>\n          \n          {selectedDisplay && (\n            <DisplayInfo>\n              <strong>{t('displayNumber', { number: selectedDisplay.displayNumber })}</strong>\n              <br />\n              {selectedDisplay.name}\n            </DisplayInfo>\n          )}\n        </Header>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        \n        {otpSent && step === 'otp' && (\n          <SuccessMessage>\n            {t('otpSent')}\n          </SuccessMessage>\n        )}\n\n        {step === 'phone' ? (\n          <Form onSubmit={handleSendOTP}>\n            <InputGroup>\n              <Label>{t('phoneNumber')}</Label>\n              <Input\n                type=\"tel\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                placeholder={t('enterPhoneNumber')}\n                required\n              />\n              {validationErrors.phoneNumber && (\n                <ErrorMessage>{validationErrors.phoneNumber}</ErrorMessage>\n              )}\n            </InputGroup>\n\n            <InputGroup>\n              <Label>{t('email')}</Label>\n              <Input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                placeholder={t('enterEmail')}\n                required\n              />\n              {validationErrors.email && (\n                <ErrorMessage>{validationErrors.email}</ErrorMessage>\n              )}\n            </InputGroup>\n\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? t('processing') : t('sendOTP')}\n            </Button>\n          </Form>\n        ) : (\n          <Form onSubmit={handleVerifyOTP}>\n            <OTPInfo>\n              {isRTL ? \n                `تم إرسال رمز التحقق إلى: ${formData.email}` :\n                `Verification code sent to: ${formData.email}`\n              }\n            </OTPInfo>\n\n            <InputGroup>\n              <Label>{t('otpCode')}</Label>\n              <Input\n                type=\"text\"\n                name=\"otpCode\"\n                value={formData.otpCode}\n                onChange={handleInputChange}\n                placeholder={t('enterOTP')}\n                maxLength=\"6\"\n                required\n              />\n              {validationErrors.otpCode && (\n                <ErrorMessage>{validationErrors.otpCode}</ErrorMessage>\n              )}\n            </InputGroup>\n\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? t('processing') : t('verifyOTP')}\n            </Button>\n\n            <Button \n              type=\"button\" \n              onClick={handleResendOTP}\n              disabled={loading}\n              style={{ background: '#6c757d' }}\n            >\n              {isRTL ? 'إعادة إرسال الرمز' : 'Resend Code'}\n            </Button>\n          </Form>\n        )}\n\n        <div style={{ marginTop: '20px', textAlign: 'center' }}>\n          <BackButton onClick={handleBack}>\n            {t('back')}\n          </BackButton>\n        </div>\n      </Card>\n    </Container>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,QAAQ,kBAAkB;AAClD,SAASC,wBAAwB,EAAEC,aAAa,EAAEC,sBAAsB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,SAAS,GAAGR,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;AACnD,CAAC;AAACC,EAAA,GARIJ,SAAS;AAUf,MAAMK,IAAI,GAAGb,MAAM,CAACS,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAPID,IAAI;AASV,MAAME,MAAM,GAAGf,MAAM,CAACS,GAAG;AACzB;AACA;AACA,CAAC;AAACO,GAAA,GAHID,MAAM;AAKZ,MAAME,KAAK,GAAGjB,MAAM,CAACkB,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,WAAW,GAAGpB,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,WAAW;AAQjB,MAAME,IAAI,GAAGtB,MAAM,CAACuB,IAAI;AACxB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,UAAU,GAAGzB,MAAM,CAACS,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAJID,UAAU;AAMhB,MAAME,KAAK,GAAG3B,MAAM,CAAC4B,KAAK;AAC1B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,KAAK,GAAG9B,MAAM,CAAC+B,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACsB,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,SAAS;AAChE,gBAAgBtB,KAAK,IAAIA,KAAK,CAACsB,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,SAAS;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIH,KAAK;AAmBX,MAAMI,MAAM,GAAGlC,MAAM,CAACmC,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAazB,KAAK,IAAIA,KAAK,CAAC0B,QAAQ,GAAG,GAAG,GAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApBIH,MAAM;AAsBZ,MAAMI,UAAU,GAAGtC,MAAM,CAACmC,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAfID,UAAU;AAiBhB,MAAME,YAAY,GAAGxC,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAPID,YAAY;AASlB,MAAME,cAAc,GAAG1C,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAPID,cAAc;AASpB,MAAME,OAAO,GAAG5C,MAAM,CAACS,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GARID,OAAO;AAUb,MAAME,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACtB,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGrD,cAAc,CAAC,CAAC;EACpC,MAAMsD,QAAQ,GAAGrD,WAAW,CAAC,CAAC;EAC9B,MAAMsD,QAAQ,GAAGrD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsD;EAAM,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEoD,OAAO;IAAEC,KAAK;IAAEC,OAAO;IAAEC,OAAO;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAG1D,MAAM,CAAC,CAAC;EAE1E,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAGjE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACvCoE,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE5D,MAAMe,KAAK,GAAGuC,IAAI,CAACmB,QAAQ,KAAK,IAAI;EACpC,MAAMC,eAAe,IAAAtB,eAAA,GAAGI,QAAQ,CAACmB,KAAK,cAAAvB,eAAA,uBAAdA,eAAA,CAAgBsB,eAAe;EAEvD,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCb,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIR,gBAAgB,CAACO,IAAI,CAAC,EAAE;MAC1BN,mBAAmB,CAACS,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAInB,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAACE,QAAQ,CAACE,WAAW,EAAE;QACzBe,MAAM,CAACf,WAAW,GAAGf,CAAC,CAAC,kBAAkB,CAAC;MAC5C,CAAC,MAAM,IAAI,CAAC9C,wBAAwB,CAAC2D,QAAQ,CAACE,WAAW,CAAC,EAAE;QAC1De,MAAM,CAACf,WAAW,GAAGf,CAAC,CAAC,oBAAoB,CAAC;MAC9C;MAEA,IAAI,CAACa,QAAQ,CAACG,KAAK,EAAE;QACnBc,MAAM,CAACd,KAAK,GAAGhB,CAAC,CAAC,YAAY,CAAC;MAChC,CAAC,MAAM,IAAI,CAAC7C,aAAa,CAAC0D,QAAQ,CAACG,KAAK,CAAC,EAAE;QACzCc,MAAM,CAACd,KAAK,GAAGhB,CAAC,CAAC,cAAc,CAAC;MAClC;IACF,CAAC,MAAM,IAAIW,IAAI,KAAK,KAAK,EAAE;MACzB,IAAI,CAACE,QAAQ,CAACI,OAAO,EAAE;QACrBa,MAAM,CAACb,OAAO,GAAGjB,CAAC,CAAC,UAAU,CAAC;MAChC,CAAC,MAAM,IAAIa,QAAQ,CAACI,OAAO,CAACc,MAAM,KAAK,CAAC,EAAE;QACxCD,MAAM,CAACb,OAAO,GAAGjB,CAAC,CAAC,YAAY,CAAC;MAClC;IACF;IAEAmB,mBAAmB,CAACW,MAAM,CAAC;IAC3B,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACC,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMG,aAAa,GAAG,MAAOV,CAAC,IAAK;IACjCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErB,MAAMO,cAAc,GAAGhF,sBAAsB,CAACyD,QAAQ,CAACE,WAAW,CAAC;IACnE,MAAMsB,QAAQ,GAAG,MAAM7B,OAAO,CAAC4B,cAAc,EAAEvB,QAAQ,CAACG,KAAK,EAAEf,IAAI,CAACmB,QAAQ,CAAC;IAE7E,IAAIiB,QAAQ,EAAE;MACZzB,OAAO,CAAC,KAAK,CAAC;IAChB;EACF,CAAC;EAED,MAAM0B,eAAe,GAAG,MAAOd,CAAC,IAAK;IACnCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErB,MAAMO,cAAc,GAAGhF,sBAAsB,CAACyD,QAAQ,CAACE,WAAW,CAAC;IACnE,MAAMsB,QAAQ,GAAG,MAAM5B,SAAS,CAAC2B,cAAc,EAAEvB,QAAQ,CAACI,OAAO,CAAC;IAElE,IAAIoB,QAAQ,EAAE;MACZ;MACAjC,KAAK,CAACiC,QAAQ,CAACE,IAAI,CAACC,QAAQ,EAAEH,QAAQ,CAACE,IAAI,CAACE,KAAK,CAAC;;MAElD;MACAvC,QAAQ,CAAC,aAAa,EAAE;QACtBoB,KAAK,EAAE;UACLD,eAAe;UACfmB,QAAQ,EAAEH,QAAQ,CAACE,IAAI,CAACC;QAC1B;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI/B,IAAI,KAAK,KAAK,EAAE;MAClBC,OAAO,CAAC,OAAO,CAAC;MAChBF,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLR,QAAQ,CAAC,iBAAiB,CAAC;IAC7B;EACF,CAAC;EAED,MAAMyC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,MAAMP,cAAc,GAAGhF,sBAAsB,CAACyD,QAAQ,CAACE,WAAW,CAAC;IACnE,MAAMP,OAAO,CAAC4B,cAAc,EAAEvB,QAAQ,CAACG,KAAK,EAAEf,IAAI,CAACmB,QAAQ,CAAC;EAC9D,CAAC;EAED,oBACE9D,OAAA,CAACC,SAAS;IAACG,KAAK,EAAEA,KAAM;IAAAkF,QAAA,eACtBtF,OAAA,CAACM,IAAI;MAAAgF,QAAA,gBACHtF,OAAA,CAACQ,MAAM;QAAA8E,QAAA,gBACLtF,OAAA,CAACU,KAAK;UAAA4E,QAAA,EACHjC,IAAI,KAAK,OAAO,GACdjD,KAAK,GAAG,cAAc,GAAG,OAAO,GAChCA,KAAK,GAAG,YAAY,GAAG;QAAoB;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzC,CAAC,EAEP3B,eAAe,iBACd/D,OAAA,CAACa,WAAW;UAAAyE,QAAA,gBACVtF,OAAA;YAAAsF,QAAA,EAAS5C,CAAC,CAAC,eAAe,EAAE;cAAEiD,MAAM,EAAE5B,eAAe,CAAC6B;YAAc,CAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAChF1F,OAAA;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL3B,eAAe,CAACI,IAAI;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACd;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAER1C,KAAK,iBAAIhD,OAAA,CAACiC,YAAY;QAAAqD,QAAA,EAAEtC;MAAK;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,EAE7CzC,OAAO,IAAII,IAAI,KAAK,KAAK,iBACxBrD,OAAA,CAACmC,cAAc;QAAAmD,QAAA,EACZ5C,CAAC,CAAC,SAAS;MAAC;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACjB,EAEArC,IAAI,KAAK,OAAO,gBACfrD,OAAA,CAACe,IAAI;QAAC8E,QAAQ,EAAEjB,aAAc;QAAAU,QAAA,gBAC5BtF,OAAA,CAACkB,UAAU;UAAAoE,QAAA,gBACTtF,OAAA,CAACoB,KAAK;YAAAkE,QAAA,EAAE5C,CAAC,CAAC,aAAa;UAAC;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjC1F,OAAA,CAACuB,KAAK;YACJE,IAAI,EAAC,KAAK;YACV0C,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAEb,QAAQ,CAACE,WAAY;YAC5BqC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,WAAW,EAAErD,CAAC,CAAC,kBAAkB,CAAE;YACnCsD,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACD9B,gBAAgB,CAACH,WAAW,iBAC3BzD,OAAA,CAACiC,YAAY;YAAAqD,QAAA,EAAE1B,gBAAgB,CAACH;UAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAEb1F,OAAA,CAACkB,UAAU;UAAAoE,QAAA,gBACTtF,OAAA,CAACoB,KAAK;YAAAkE,QAAA,EAAE5C,CAAC,CAAC,OAAO;UAAC;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3B1F,OAAA,CAACuB,KAAK;YACJE,IAAI,EAAC,OAAO;YACZ0C,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEb,QAAQ,CAACG,KAAM;YACtBoC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,WAAW,EAAErD,CAAC,CAAC,YAAY,CAAE;YAC7BsD,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACD9B,gBAAgB,CAACF,KAAK,iBACrB1D,OAAA,CAACiC,YAAY;YAAAqD,QAAA,EAAE1B,gBAAgB,CAACF;UAAK;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CACrD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAEb1F,OAAA,CAAC2B,MAAM;UAACF,IAAI,EAAC,QAAQ;UAACI,QAAQ,EAAEkB,OAAQ;UAAAuC,QAAA,EACrCvC,OAAO,GAAGL,CAAC,CAAC,YAAY,CAAC,GAAGA,CAAC,CAAC,SAAS;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAEP1F,OAAA,CAACe,IAAI;QAAC8E,QAAQ,EAAEb,eAAgB;QAAAM,QAAA,gBAC9BtF,OAAA,CAACqC,OAAO;UAAAiD,QAAA,EACLlF,KAAK,GACJ,4BAA4BmD,QAAQ,CAACG,KAAK,EAAE,GAC5C,8BAA8BH,QAAQ,CAACG,KAAK;QAAE;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzC,CAAC,eAEV1F,OAAA,CAACkB,UAAU;UAAAoE,QAAA,gBACTtF,OAAA,CAACoB,KAAK;YAAAkE,QAAA,EAAE5C,CAAC,CAAC,SAAS;UAAC;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7B1F,OAAA,CAACuB,KAAK;YACJE,IAAI,EAAC,MAAM;YACX0C,IAAI,EAAC,SAAS;YACdC,KAAK,EAAEb,QAAQ,CAACI,OAAQ;YACxBmC,QAAQ,EAAE7B,iBAAkB;YAC5B8B,WAAW,EAAErD,CAAC,CAAC,UAAU,CAAE;YAC3BuD,SAAS,EAAC,GAAG;YACbD,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACD9B,gBAAgB,CAACD,OAAO,iBACvB3D,OAAA,CAACiC,YAAY;YAAAqD,QAAA,EAAE1B,gBAAgB,CAACD;UAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAEb1F,OAAA,CAAC2B,MAAM;UAACF,IAAI,EAAC,QAAQ;UAACI,QAAQ,EAAEkB,OAAQ;UAAAuC,QAAA,EACrCvC,OAAO,GAAGL,CAAC,CAAC,YAAY,CAAC,GAAGA,CAAC,CAAC,WAAW;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAET1F,OAAA,CAAC2B,MAAM;UACLF,IAAI,EAAC,QAAQ;UACbyE,OAAO,EAAEb,eAAgB;UACzBxD,QAAQ,EAAEkB,OAAQ;UAClBoD,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAU,CAAE;UAAAd,QAAA,EAEhClF,KAAK,GAAG,mBAAmB,GAAG;QAAa;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACP,eAED1F,OAAA;QAAKmG,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAhB,QAAA,eACrDtF,OAAA,CAAC+B,UAAU;UAACmE,OAAO,EAAEd,UAAW;UAAAE,QAAA,EAC7B5C,CAAC,CAAC,MAAM;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAAClD,EAAA,CA9NID,SAAS;EAAA,QACOjD,cAAc,EACjBC,WAAW,EACXC,WAAW,EACVG,OAAO,EACyCD,MAAM;AAAA;AAAA6G,IAAA,GALpEhE,SAAS;AAgOf,eAAeA,SAAS;AAAC,IAAAlC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAiE,IAAA;AAAAC,YAAA,CAAAnG,EAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}