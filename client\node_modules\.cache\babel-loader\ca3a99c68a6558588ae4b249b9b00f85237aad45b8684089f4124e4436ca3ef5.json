{"ast": null, "code": "import axios from'axios';// إعداد الـ API base URL\nconst API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:3001/api';// إنشاء instance من axios\nconst api=axios.create({baseURL:API_BASE_URL,timeout:10000,headers:{'Content-Type':'application/json'}});// إضافة interceptor للطلبات لإضافة token\napi.interceptors.request.use(config=>{const token=localStorage.getItem('authToken');if(token){config.headers.Authorization=`Bearer ${token}`;}return config;},error=>{return Promise.reject(error);});// إضافة interceptor للاستجابات لمعالجة الأخطاء\napi.interceptors.response.use(response=>{return response.data;},error=>{var _error$response,_error$response2;if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401){// إزالة token منتهي الصلاحية\nlocalStorage.removeItem('authToken');localStorage.removeItem('userInfo');window.location.href='/';}return Promise.reject(((_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.data)||error.message);});// خدمات المصادقة\nexport const authService={// إرسال OTP\nsendOTP:function(phoneNumber,email){let language=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'ar';return api.post('/auth/send-otp',{phoneNumber,email,language});},// التحقق من OTP\nverifyOTP:(phoneNumber,otpCode)=>{return api.post('/auth/verify-otp',{phoneNumber,otpCode});},// تسجيل دخول المالك\nownerLogin:password=>{return api.post('/auth/owner-login',{password});},// التحقق من صحة الرمز المميز\nverifyToken:()=>{return api.get('/auth/verify-token');}};// خدمات الشاشات\nexport const displayService={// الحصول على جميع الشاشات\ngetAllDisplays:()=>{return api.get('/displays');},// الحصول على شاشة محددة\ngetDisplay:displayId=>{return api.get(`/displays/${displayId}`);},// حجز شاشة\nreserveDisplay:(displayId,customerName)=>{return api.post(`/displays/${displayId}/reserve`,{customerName});},// إلغاء حجز الشاشة\ncancelReservation:displayId=>{return api.post(`/displays/${displayId}/cancel-reservation`);},// تحديث إعدادات الشاشة (للمالك)\nupdateDisplay:(displayId,data)=>{return api.put(`/displays/${displayId}`,data);}};// خدمات المعاملات\nexport const transactionService={// إنشاء معاملة جديدة\ncreateTransaction:(displayId,customerName,amount,duration)=>{return api.post('/transactions',{displayId,customerName,amount,duration});},// تأكيد المعاملة بعد الدفع\nconfirmTransaction:(transactionId,paymentIntentId)=>{return api.post(`/transactions/${transactionId}/confirm`,{paymentIntentId});},// الحصول على معاملات العميل\ngetMyTransactions:()=>{return api.get('/transactions/my-transactions');},// الحصول على جميع المعاملات (للمالك)\ngetAllTransactions:function(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return api.get('/transactions/all',{params});}};// خدمات الدفع\nexport const paymentService={// الحصول على إعدادات الدفع\ngetPaymentConfig:()=>{return api.get('/payment/config');},// إنشاء Payment Intent\ncreatePaymentIntent:function(amount,transactionId){let currency=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'sar';return api.post('/payment/create-payment-intent',{amount,transactionId,currency});},// التحقق من حالة الدفع\ngetPaymentStatus:paymentIntentId=>{return api.get(`/payment/payment-status/${paymentIntentId}`);},// محاكاة دفع NFC\nsimulateNFCPayment:function(transactionId){let cardNumber=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'****************';return api.post('/payment/simulate-nfc-payment',{transactionId,cardNumber});}};// خدمات المالك\nexport const ownerService={// الحصول على إحصائيات لوحة التحكم\ngetDashboardStats:()=>{return api.get('/owner/dashboard-stats');},// الحصول على الإعدادات\ngetSettings:()=>{return api.get('/owner/settings');},// تحديث الإعدادات\nupdateSettings:settings=>{return api.put('/owner/settings',{settings});},// الحصول على تقرير الإيرادات\ngetRevenueReport:function(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return api.get('/owner/revenue-report',{params});},// الحصول على تقرير استخدام الشاشات\ngetDisplayUsageReport:function(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return api.get('/owner/display-usage-report',{params});},// إنهاء معاملة يدوياً\nendTransaction:(transactionId,reason)=>{return api.post(`/owner/end-transaction/${transactionId}`,{reason});}};// خدمات عامة\nexport const generalService={// اختبار الاتصال\nping:()=>{return api.get('/ping');}};export default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response", "_error$response2", "status", "removeItem", "window", "location", "href", "message", "authService", "sendOTP", "phoneNumber", "email", "language", "arguments", "length", "undefined", "post", "verifyOTP", "otpCode", "owner<PERSON><PERSON><PERSON>", "password", "verifyToken", "get", "displayService", "getAllDisplays", "getDisplay", "displayId", "reserveDisplay", "customerName", "cancelReservation", "updateDisplay", "put", "transactionService", "createTransaction", "amount", "duration", "confirmTransaction", "transactionId", "paymentIntentId", "getMyTransactions", "getAllTransactions", "params", "paymentService", "getPaymentConfig", "createPaymentIntent", "currency", "getPaymentStatus", "simulateNFCPayment", "cardNumber", "ownerService", "getDashboardStats", "getSettings", "updateSettings", "settings", "getRevenueReport", "getDisplayUsageReport", "endTransaction", "reason", "generalService", "ping"], "sources": ["D:/برمجة/tste 1/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// إعداد الـ API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// إضافة interceptor للطلبات لإضافة token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// إضافة interceptor للاستجابات لمعالجة الأخطاء\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // إزالة token منتهي الصلاحية\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userInfo');\n      window.location.href = '/';\n    }\n    return Promise.reject(error.response?.data || error.message);\n  }\n);\n\n// خدمات المصادقة\nexport const authService = {\n  // إرسال OTP\n  sendOTP: (phoneNumber, email, language = 'ar') => {\n    return api.post('/auth/send-otp', { phoneNumber, email, language });\n  },\n\n  // التحقق من OTP\n  verifyOTP: (phoneNumber, otpCode) => {\n    return api.post('/auth/verify-otp', { phoneNumber, otpCode });\n  },\n\n  // تسجيل دخول المالك\n  ownerLogin: (password) => {\n    return api.post('/auth/owner-login', { password });\n  },\n\n  // التحقق من صحة الرمز المميز\n  verifyToken: () => {\n    return api.get('/auth/verify-token');\n  },\n};\n\n// خدمات الشاشات\nexport const displayService = {\n  // الحصول على جميع الشاشات\n  getAllDisplays: () => {\n    return api.get('/displays');\n  },\n\n  // الحصول على شاشة محددة\n  getDisplay: (displayId) => {\n    return api.get(`/displays/${displayId}`);\n  },\n\n  // حجز شاشة\n  reserveDisplay: (displayId, customerName) => {\n    return api.post(`/displays/${displayId}/reserve`, { customerName });\n  },\n\n  // إلغاء حجز الشاشة\n  cancelReservation: (displayId) => {\n    return api.post(`/displays/${displayId}/cancel-reservation`);\n  },\n\n  // تحديث إعدادات الشاشة (للمالك)\n  updateDisplay: (displayId, data) => {\n    return api.put(`/displays/${displayId}`, data);\n  },\n};\n\n// خدمات المعاملات\nexport const transactionService = {\n  // إنشاء معاملة جديدة\n  createTransaction: (displayId, customerName, amount, duration) => {\n    return api.post('/transactions', { displayId, customerName, amount, duration });\n  },\n\n  // تأكيد المعاملة بعد الدفع\n  confirmTransaction: (transactionId, paymentIntentId) => {\n    return api.post(`/transactions/${transactionId}/confirm`, { paymentIntentId });\n  },\n\n  // الحصول على معاملات العميل\n  getMyTransactions: () => {\n    return api.get('/transactions/my-transactions');\n  },\n\n  // الحصول على جميع المعاملات (للمالك)\n  getAllTransactions: (params = {}) => {\n    return api.get('/transactions/all', { params });\n  },\n};\n\n// خدمات الدفع\nexport const paymentService = {\n  // الحصول على إعدادات الدفع\n  getPaymentConfig: () => {\n    return api.get('/payment/config');\n  },\n\n  // إنشاء Payment Intent\n  createPaymentIntent: (amount, transactionId, currency = 'sar') => {\n    return api.post('/payment/create-payment-intent', { amount, transactionId, currency });\n  },\n\n  // التحقق من حالة الدفع\n  getPaymentStatus: (paymentIntentId) => {\n    return api.get(`/payment/payment-status/${paymentIntentId}`);\n  },\n\n  // محاكاة دفع NFC\n  simulateNFCPayment: (transactionId, cardNumber = '****************') => {\n    return api.post('/payment/simulate-nfc-payment', { transactionId, cardNumber });\n  },\n};\n\n// خدمات المالك\nexport const ownerService = {\n  // الحصول على إحصائيات لوحة التحكم\n  getDashboardStats: () => {\n    return api.get('/owner/dashboard-stats');\n  },\n\n  // الحصول على الإعدادات\n  getSettings: () => {\n    return api.get('/owner/settings');\n  },\n\n  // تحديث الإعدادات\n  updateSettings: (settings) => {\n    return api.put('/owner/settings', { settings });\n  },\n\n  // الحصول على تقرير الإيرادات\n  getRevenueReport: (params = {}) => {\n    return api.get('/owner/revenue-report', { params });\n  },\n\n  // الحصول على تقرير استخدام الشاشات\n  getDisplayUsageReport: (params = {}) => {\n    return api.get('/owner/display-usage-report', { params });\n  },\n\n  // إنهاء معاملة يدوياً\n  endTransaction: (transactionId, reason) => {\n    return api.post(`/owner/end-transaction/${transactionId}`, { reason });\n  },\n};\n\n// خدمات عامة\nexport const generalService = {\n  // اختبار الاتصال\n  ping: () => {\n    return api.get('/ping');\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AACA,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF;AACA,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC/C,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,CAAG,UAAUH,KAAK,EAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAK,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,EAAK,CACZ,MAAO,CAAAA,QAAQ,CAACC,IAAI,CACtB,CAAC,CACAJ,KAAK,EAAK,KAAAK,eAAA,CAAAC,gBAAA,CACT,GAAI,EAAAD,eAAA,CAAAL,KAAK,CAACG,QAAQ,UAAAE,eAAA,iBAAdA,eAAA,CAAgBE,MAAM,IAAK,GAAG,CAAE,CAClC;AACAV,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC,CACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC,CACnCC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC5B,CACA,MAAO,CAAAV,OAAO,CAACC,MAAM,CAAC,EAAAI,gBAAA,CAAAN,KAAK,CAACG,QAAQ,UAAAG,gBAAA,iBAAdA,gBAAA,CAAgBF,IAAI,GAAIJ,KAAK,CAACY,OAAO,CAAC,CAC9D,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzB;AACAC,OAAO,CAAE,QAAAA,CAACC,WAAW,CAAEC,KAAK,CAAsB,IAApB,CAAAC,QAAQ,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC3C,MAAO,CAAA/B,GAAG,CAACkC,IAAI,CAAC,gBAAgB,CAAE,CAAEN,WAAW,CAAEC,KAAK,CAAEC,QAAS,CAAC,CAAC,CACrE,CAAC,CAED;AACAK,SAAS,CAAEA,CAACP,WAAW,CAAEQ,OAAO,GAAK,CACnC,MAAO,CAAApC,GAAG,CAACkC,IAAI,CAAC,kBAAkB,CAAE,CAAEN,WAAW,CAAEQ,OAAQ,CAAC,CAAC,CAC/D,CAAC,CAED;AACAC,UAAU,CAAGC,QAAQ,EAAK,CACxB,MAAO,CAAAtC,GAAG,CAACkC,IAAI,CAAC,mBAAmB,CAAE,CAAEI,QAAS,CAAC,CAAC,CACpD,CAAC,CAED;AACAC,WAAW,CAAEA,CAAA,GAAM,CACjB,MAAO,CAAAvC,GAAG,CAACwC,GAAG,CAAC,oBAAoB,CAAC,CACtC,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAG,CAC5B;AACAC,cAAc,CAAEA,CAAA,GAAM,CACpB,MAAO,CAAA1C,GAAG,CAACwC,GAAG,CAAC,WAAW,CAAC,CAC7B,CAAC,CAED;AACAG,UAAU,CAAGC,SAAS,EAAK,CACzB,MAAO,CAAA5C,GAAG,CAACwC,GAAG,CAAC,aAAaI,SAAS,EAAE,CAAC,CAC1C,CAAC,CAED;AACAC,cAAc,CAAEA,CAACD,SAAS,CAAEE,YAAY,GAAK,CAC3C,MAAO,CAAA9C,GAAG,CAACkC,IAAI,CAAC,aAAaU,SAAS,UAAU,CAAE,CAAEE,YAAa,CAAC,CAAC,CACrE,CAAC,CAED;AACAC,iBAAiB,CAAGH,SAAS,EAAK,CAChC,MAAO,CAAA5C,GAAG,CAACkC,IAAI,CAAC,aAAaU,SAAS,qBAAqB,CAAC,CAC9D,CAAC,CAED;AACAI,aAAa,CAAEA,CAACJ,SAAS,CAAE3B,IAAI,GAAK,CAClC,MAAO,CAAAjB,GAAG,CAACiD,GAAG,CAAC,aAAaL,SAAS,EAAE,CAAE3B,IAAI,CAAC,CAChD,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAiC,kBAAkB,CAAG,CAChC;AACAC,iBAAiB,CAAEA,CAACP,SAAS,CAAEE,YAAY,CAAEM,MAAM,CAAEC,QAAQ,GAAK,CAChE,MAAO,CAAArD,GAAG,CAACkC,IAAI,CAAC,eAAe,CAAE,CAAEU,SAAS,CAAEE,YAAY,CAAEM,MAAM,CAAEC,QAAS,CAAC,CAAC,CACjF,CAAC,CAED;AACAC,kBAAkB,CAAEA,CAACC,aAAa,CAAEC,eAAe,GAAK,CACtD,MAAO,CAAAxD,GAAG,CAACkC,IAAI,CAAC,iBAAiBqB,aAAa,UAAU,CAAE,CAAEC,eAAgB,CAAC,CAAC,CAChF,CAAC,CAED;AACAC,iBAAiB,CAAEA,CAAA,GAAM,CACvB,MAAO,CAAAzD,GAAG,CAACwC,GAAG,CAAC,+BAA+B,CAAC,CACjD,CAAC,CAED;AACAkB,kBAAkB,CAAE,QAAAA,CAAA,CAAiB,IAAhB,CAAAC,MAAM,CAAA5B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC9B,MAAO,CAAA/B,GAAG,CAACwC,GAAG,CAAC,mBAAmB,CAAE,CAAEmB,MAAO,CAAC,CAAC,CACjD,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAG,CAC5B;AACAC,gBAAgB,CAAEA,CAAA,GAAM,CACtB,MAAO,CAAA7D,GAAG,CAACwC,GAAG,CAAC,iBAAiB,CAAC,CACnC,CAAC,CAED;AACAsB,mBAAmB,CAAE,QAAAA,CAACV,MAAM,CAAEG,aAAa,CAAuB,IAArB,CAAAQ,QAAQ,CAAAhC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC3D,MAAO,CAAA/B,GAAG,CAACkC,IAAI,CAAC,gCAAgC,CAAE,CAAEkB,MAAM,CAAEG,aAAa,CAAEQ,QAAS,CAAC,CAAC,CACxF,CAAC,CAED;AACAC,gBAAgB,CAAGR,eAAe,EAAK,CACrC,MAAO,CAAAxD,GAAG,CAACwC,GAAG,CAAC,2BAA2BgB,eAAe,EAAE,CAAC,CAC9D,CAAC,CAED;AACAS,kBAAkB,CAAE,QAAAA,CAACV,aAAa,CAAsC,IAApC,CAAAW,UAAU,CAAAnC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,kBAAkB,CACjE,MAAO,CAAA/B,GAAG,CAACkC,IAAI,CAAC,+BAA+B,CAAE,CAAEqB,aAAa,CAAEW,UAAW,CAAC,CAAC,CACjF,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,CAC1B;AACAC,iBAAiB,CAAEA,CAAA,GAAM,CACvB,MAAO,CAAApE,GAAG,CAACwC,GAAG,CAAC,wBAAwB,CAAC,CAC1C,CAAC,CAED;AACA6B,WAAW,CAAEA,CAAA,GAAM,CACjB,MAAO,CAAArE,GAAG,CAACwC,GAAG,CAAC,iBAAiB,CAAC,CACnC,CAAC,CAED;AACA8B,cAAc,CAAGC,QAAQ,EAAK,CAC5B,MAAO,CAAAvE,GAAG,CAACiD,GAAG,CAAC,iBAAiB,CAAE,CAAEsB,QAAS,CAAC,CAAC,CACjD,CAAC,CAED;AACAC,gBAAgB,CAAE,QAAAA,CAAA,CAAiB,IAAhB,CAAAb,MAAM,CAAA5B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC5B,MAAO,CAAA/B,GAAG,CAACwC,GAAG,CAAC,uBAAuB,CAAE,CAAEmB,MAAO,CAAC,CAAC,CACrD,CAAC,CAED;AACAc,qBAAqB,CAAE,QAAAA,CAAA,CAAiB,IAAhB,CAAAd,MAAM,CAAA5B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACjC,MAAO,CAAA/B,GAAG,CAACwC,GAAG,CAAC,6BAA6B,CAAE,CAAEmB,MAAO,CAAC,CAAC,CAC3D,CAAC,CAED;AACAe,cAAc,CAAEA,CAACnB,aAAa,CAAEoB,MAAM,GAAK,CACzC,MAAO,CAAA3E,GAAG,CAACkC,IAAI,CAAC,0BAA0BqB,aAAa,EAAE,CAAE,CAAEoB,MAAO,CAAC,CAAC,CACxE,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAG,CAC5B;AACAC,IAAI,CAAEA,CAAA,GAAM,CACV,MAAO,CAAA7E,GAAG,CAACwC,GAAG,CAAC,OAAO,CAAC,CACzB,CACF,CAAC,CAED,cAAe,CAAAxC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}