{"ast": null, "code": "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj) {\n  for (var _len = arguments.length, attr = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    attr[_key - 1] = arguments[_key];\n  }\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n  }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n  if (typeof obj === \"string\") {\n    return utf8Length(obj);\n  }\n  // arraybuffer or blob\n  return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n  let c = 0,\n    length = 0;\n  for (let i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    } else if (c < 0x800) {\n      length += 2;\n    } else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    } else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n  return Date.now().toString(36).substring(3) + Math.random().toString(36).substring(2, 5);\n}", "map": {"version": 3, "names": ["globalThisShim", "globalThis", "pick", "obj", "_len", "arguments", "length", "attr", "Array", "_key", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "byteLength", "utf8Length", "Math", "ceil", "size", "str", "c", "i", "l", "charCodeAt", "randomString", "Date", "now", "toString", "substring", "random"], "sources": ["D:/برمجة/tste 1/node_modules/engine.io-client/build/esm/util.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n"], "mappings": "AAAA,SAASA,cAAc,IAAIC,UAAU,QAAQ,mBAAmB;AAChE,OAAO,SAASC,IAAIA,CAACC,GAAG,EAAW;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC7B,OAAOF,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAK;IAC3B,IAAIT,GAAG,CAACU,cAAc,CAACD,CAAC,CAAC,EAAE;MACvBD,GAAG,CAACC,CAAC,CAAC,GAAGT,GAAG,CAACS,CAAC,CAAC;IACnB;IACA,OAAOD,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA;AACA,MAAMG,kBAAkB,GAAGb,UAAU,CAACc,UAAU;AAChD,MAAMC,oBAAoB,GAAGf,UAAU,CAACgB,YAAY;AACpD,OAAO,SAASC,qBAAqBA,CAACf,GAAG,EAAEgB,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACC,eAAe,EAAE;IACtBjB,GAAG,CAACkB,YAAY,GAAGP,kBAAkB,CAACQ,IAAI,CAACrB,UAAU,CAAC;IACtDE,GAAG,CAACoB,cAAc,GAAGP,oBAAoB,CAACM,IAAI,CAACrB,UAAU,CAAC;EAC9D,CAAC,MACI;IACDE,GAAG,CAACkB,YAAY,GAAGpB,UAAU,CAACc,UAAU,CAACO,IAAI,CAACrB,UAAU,CAAC;IACzDE,GAAG,CAACoB,cAAc,GAAGtB,UAAU,CAACgB,YAAY,CAACK,IAAI,CAACrB,UAAU,CAAC;EACjE;AACJ;AACA;AACA,MAAMuB,eAAe,GAAG,IAAI;AAC5B;AACA,OAAO,SAASC,UAAUA,CAACtB,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,OAAOuB,UAAU,CAACvB,GAAG,CAAC;EAC1B;EACA;EACA,OAAOwB,IAAI,CAACC,IAAI,CAAC,CAACzB,GAAG,CAACsB,UAAU,IAAItB,GAAG,CAAC0B,IAAI,IAAIL,eAAe,CAAC;AACpE;AACA,SAASE,UAAUA,CAACI,GAAG,EAAE;EACrB,IAAIC,CAAC,GAAG,CAAC;IAAEzB,MAAM,GAAG,CAAC;EACrB,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,GAAG,CAACxB,MAAM,EAAE0B,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IACxCD,CAAC,GAAGD,GAAG,CAACI,UAAU,CAACF,CAAC,CAAC;IACrB,IAAID,CAAC,GAAG,IAAI,EAAE;MACVzB,MAAM,IAAI,CAAC;IACf,CAAC,MACI,IAAIyB,CAAC,GAAG,KAAK,EAAE;MAChBzB,MAAM,IAAI,CAAC;IACf,CAAC,MACI,IAAIyB,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAChCzB,MAAM,IAAI,CAAC;IACf,CAAC,MACI;MACD0B,CAAC,EAAE;MACH1B,MAAM,IAAI,CAAC;IACf;EACJ;EACA,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA,OAAO,SAAS6B,YAAYA,CAAA,EAAG;EAC3B,OAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,GACxCZ,IAAI,CAACa,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}