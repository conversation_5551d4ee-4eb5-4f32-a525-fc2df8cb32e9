<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | User Authentication</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <style>
        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary: #64748b;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            --font-ar: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-ar);
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gradient-primary);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-6);
        }

        body.en {
            font-family: var(--font-en);
            direction: ltr;
        }

        /* Login Container */
        .login-container {
            width: 100%;
            max-width: 480px;
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
            position: relative;
        }

        .login-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: var(--space-8);
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-icon {
            width: 4rem;
            height: 4rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            font-size: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .login-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
            position: relative;
            z-index: 1;
        }

        .login-subtitle {
            opacity: 0.9;
            font-size: 0.875rem;
            position: relative;
            z-index: 1;
        }

        .login-form {
            padding: var(--space-8);
        }

        .form-group {
            margin-bottom: var(--space-6);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: var(--space-2);
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: var(--space-4);
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: var(--transition-normal);
            background: var(--white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-input.error {
            border-color: var(--error);
        }

        .form-error {
            color: var(--error);
            font-size: 0.75rem;
            margin-top: var(--space-1);
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: var(--space-4);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1rem;
        }

        .input-group .form-input {
            padding-left: var(--space-12);
        }

        body.en .input-icon {
            left: auto;
            right: var(--space-4);
        }

        body.en .input-group .form-input {
            padding-left: var(--space-4);
            padding-right: var(--space-12);
        }

        .btn {
            width: 100%;
            padding: var(--space-4);
            border: none;
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--white);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 2px solid var(--gray-200);
        }

        .btn-secondary:hover:not(:disabled) {
            background: var(--gray-200);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-loading {
            pointer-events: none;
        }

        .loading-spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid var(--white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-link {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--gray-600);
            text-decoration: none;
            font-size: 0.875rem;
            margin-bottom: var(--space-6);
            transition: var(--transition-fast);
        }

        .back-link:hover {
            color: var(--primary);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: var(--space-6) 0;
            color: var(--gray-400);
            font-size: 0.875rem;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--gray-200);
        }

        .divider span {
            padding: 0 var(--space-4);
        }

        /* OTP Input */
        .otp-container {
            display: flex;
            gap: var(--space-3);
            justify-content: center;
            margin: var(--space-6) 0;
        }

        .otp-input {
            width: 3rem;
            height: 3rem;
            text-align: center;
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-lg);
            font-size: 1.25rem;
            font-weight: 600;
            transition: var(--transition-normal);
        }

        .otp-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Success Message */
        .success-message {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            color: var(--success);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-6);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 0.875rem;
        }

        /* Responsive */
        @media (max-width: 480px) {
            body {
                padding: var(--space-4);
            }
            
            .login-container {
                max-width: 100%;
            }
            
            .login-header,
            .login-form {
                padding: var(--space-6);
            }
            
            .otp-container {
                gap: var(--space-2);
            }
            
            .otp-input {
                width: 2.5rem;
                height: 2.5rem;
                font-size: 1rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Hidden state */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="login-container animate-fade-in-up">
        <!-- Header -->
        <div class="login-header">
            <div class="login-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <h1 class="login-title" data-key="login-title">تسجيل الدخول الآمن</h1>
            <p class="login-subtitle" data-key="login-subtitle">أدخل بياناتك للمتابعة إلى الشاشة المختارة</p>
        </div>

        <!-- Form -->
        <div class="login-form">
            <a href="displays.html" class="back-link">
                <i class="fas fa-arrow-right"></i>
                <span data-key="back-to-displays">العودة لاختيار الشاشة</span>
            </a>

            <!-- Step 1: Phone & Email -->
            <form id="contact-form" class="login-step">
                <div class="form-group">
                    <label class="form-label" data-key="phone-label">رقم الهاتف</label>
                    <div class="input-group">
                        <i class="input-icon fas fa-phone"></i>
                        <input type="tel" class="form-input" id="phone" placeholder="+966 5X XXX XXXX" required>
                    </div>
                    <div class="form-error hidden" id="phone-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <span data-key="invalid-phone">رقم الهاتف غير صحيح</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" data-key="email-label">البريد الإلكتروني</label>
                    <div class="input-group">
                        <i class="input-icon fas fa-envelope"></i>
                        <input type="email" class="form-input" id="email" placeholder="<EMAIL>" required>
                    </div>
                    <div class="form-error hidden" id="email-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <span data-key="invalid-email">البريد الإلكتروني غير صحيح</span>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary" id="send-otp-btn">
                    <i class="fas fa-paper-plane"></i>
                    <span data-key="send-otp">إرسال رمز التحقق</span>
                </button>
            </form>

            <!-- Step 2: OTP Verification -->
            <div id="otp-step" class="login-step hidden">
                <div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    <span data-key="otp-sent">تم إرسال رمز التحقق إلى بريدك الإلكتروني</span>
                </div>

                <div class="form-group">
                    <label class="form-label" data-key="otp-label">رمز التحقق (6 أرقام)</label>
                    <div class="otp-container">
                        <input type="text" class="otp-input" maxlength="1" data-index="0">
                        <input type="text" class="otp-input" maxlength="1" data-index="1">
                        <input type="text" class="otp-input" maxlength="1" data-index="2">
                        <input type="text" class="otp-input" maxlength="1" data-index="3">
                        <input type="text" class="otp-input" maxlength="1" data-index="4">
                        <input type="text" class="otp-input" maxlength="1" data-index="5">
                    </div>
                    <div class="form-error hidden" id="otp-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <span data-key="invalid-otp">رمز التحقق غير صحيح</span>
                    </div>
                </div>

                <button type="button" class="btn btn-primary" id="verify-otp-btn">
                    <i class="fas fa-shield-alt"></i>
                    <span data-key="verify-otp">تحقق من الرمز</span>
                </button>

                <div class="divider">
                    <span data-key="or">أو</span>
                </div>

                <button type="button" class="btn btn-secondary" id="resend-otp-btn">
                    <i class="fas fa-redo"></i>
                    <span data-key="resend-otp">إعادة إرسال الرمز</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Translation System
        const translations = {
            ar: {
                'login-title': 'تسجيل الدخول الآمن',
                'login-subtitle': 'أدخل بياناتك للمتابعة إلى الشاشة المختارة',
                'back-to-displays': 'العودة لاختيار الشاشة',
                'phone-label': 'رقم الهاتف',
                'email-label': 'البريد الإلكتروني',
                'send-otp': 'إرسال رمز التحقق',
                'otp-sent': 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',
                'otp-label': 'رمز التحقق (6 أرقام)',
                'verify-otp': 'تحقق من الرمز',
                'resend-otp': 'إعادة إرسال الرمز',
                'or': 'أو',
                'invalid-phone': 'رقم الهاتف غير صحيح',
                'invalid-email': 'البريد الإلكتروني غير صحيح',
                'invalid-otp': 'رمز التحقق غير صحيح'
            },
            en: {
                'login-title': 'Secure Login',
                'login-subtitle': 'Enter your details to continue to the selected display',
                'back-to-displays': 'Back to Display Selection',
                'phone-label': 'Phone Number',
                'email-label': 'Email Address',
                'send-otp': 'Send Verification Code',
                'otp-sent': 'Verification code sent to your email',
                'otp-label': 'Verification Code (6 digits)',
                'verify-otp': 'Verify Code',
                'resend-otp': 'Resend Code',
                'or': 'or',
                'invalid-phone': 'Invalid phone number',
                'invalid-email': 'Invalid email address',
                'invalid-otp': 'Invalid verification code'
            }
        };

        // Login Manager
        class LoginManager {
            constructor() {
                this.currentLang = 'ar';
                this.currentStep = 1;
                this.generatedOTP = '';
                this.init();
            }

            init() {
                this.setupLanguage();
                this.setupEventListeners();
                this.setupOTPInputs();
                this.updateLanguage();
            }

            setupLanguage() {
                const urlParams = new URLSearchParams(window.location.search);
                const lang = urlParams.get('lang') || 'ar';
                this.setLanguage(lang);
            }

            setLanguage(lang) {
                this.currentLang = lang;
                document.body.className = lang === 'en' ? 'en' : '';
                document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
                document.documentElement.lang = lang;
                this.updateLanguage();
            }

            updateLanguage() {
                document.querySelectorAll('[data-key]').forEach(element => {
                    const key = element.dataset.key;
                    if (translations[this.currentLang][key]) {
                        element.textContent = translations[this.currentLang][key];
                    }
                });
            }

            setupEventListeners() {
                // Contact form submission
                document.getElementById('contact-form')?.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleContactSubmit();
                });

                // OTP verification
                document.getElementById('verify-otp-btn')?.addEventListener('click', () => {
                    this.handleOTPVerification();
                });

                // Resend OTP
                document.getElementById('resend-otp-btn')?.addEventListener('click', () => {
                    this.handleResendOTP();
                });
            }

            setupOTPInputs() {
                const otpInputs = document.querySelectorAll('.otp-input');
                
                otpInputs.forEach((input, index) => {
                    input.addEventListener('input', (e) => {
                        const value = e.target.value;
                        
                        if (value && index < otpInputs.length - 1) {
                            otpInputs[index + 1].focus();
                        }
                    });

                    input.addEventListener('keydown', (e) => {
                        if (e.key === 'Backspace' && !e.target.value && index > 0) {
                            otpInputs[index - 1].focus();
                        }
                    });
                });
            }

            validatePhone(phone) {
                const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
                return phoneRegex.test(phone.replace(/\s/g, ''));
            }

            validateEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            showError(fieldId, show = true) {
                const errorElement = document.getElementById(`${fieldId}-error`);
                const inputElement = document.getElementById(fieldId);
                
                if (errorElement && inputElement) {
                    errorElement.classList.toggle('hidden', !show);
                    inputElement.classList.toggle('error', show);
                }
            }

            async handleContactSubmit() {
                const phone = document.getElementById('phone').value.trim();
                const email = document.getElementById('email').value.trim();
                const submitBtn = document.getElementById('send-otp-btn');

                // Reset errors
                this.showError('phone', false);
                this.showError('email', false);

                // Validate inputs
                let hasErrors = false;

                if (!this.validatePhone(phone)) {
                    this.showError('phone', true);
                    hasErrors = true;
                }

                if (!this.validateEmail(email)) {
                    this.showError('email', true);
                    hasErrors = true;
                }

                if (hasErrors) return;

                // Show loading
                submitBtn.disabled = true;
                submitBtn.innerHTML = `
                    <div class="loading-spinner"></div>
                    <span>${translations[this.currentLang]['send-otp']}</span>
                `;

                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Generate OTP
                this.generatedOTP = Math.floor(100000 + Math.random() * 900000).toString();
                console.log(`🔐 Generated OTP: ${this.generatedOTP}`);

                // Show OTP in development
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    alert(`رمز التحقق للاختبار: ${this.generatedOTP}\nDevelopment OTP: ${this.generatedOTP}`);
                }

                // Move to OTP step
                this.showOTPStep();

                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = `
                    <i class="fas fa-paper-plane"></i>
                    <span>${translations[this.currentLang]['send-otp']}</span>
                `;
            }

            showOTPStep() {
                document.getElementById('contact-form').classList.add('hidden');
                document.getElementById('otp-step').classList.remove('hidden');
                
                // Focus first OTP input
                document.querySelector('.otp-input').focus();
            }

            getOTPValue() {
                const inputs = document.querySelectorAll('.otp-input');
                return Array.from(inputs).map(input => input.value).join('');
            }

            async handleOTPVerification() {
                const enteredOTP = this.getOTPValue();
                const verifyBtn = document.getElementById('verify-otp-btn');

                if (enteredOTP.length !== 6) {
                    this.showError('otp', true);
                    return;
                }

                // Show loading
                verifyBtn.disabled = true;
                verifyBtn.innerHTML = `
                    <div class="loading-spinner"></div>
                    <span>${translations[this.currentLang]['verify-otp']}</span>
                `;

                // Simulate verification
                await new Promise(resolve => setTimeout(resolve, 1500));

                if (enteredOTP === this.generatedOTP) {
                    // Success - redirect to customer info page
                    const urlParams = new URLSearchParams(window.location.search);
                    const displayId = urlParams.get('display');
                    const lang = this.currentLang;
                    
                    window.location.href = `customer-info.html?display=${displayId}&lang=${lang}`;
                } else {
                    // Show error
                    this.showError('otp', true);
                    
                    // Reset button
                    verifyBtn.disabled = false;
                    verifyBtn.innerHTML = `
                        <i class="fas fa-shield-alt"></i>
                        <span>${translations[this.currentLang]['verify-otp']}</span>
                    `;
                    
                    // Clear OTP inputs
                    document.querySelectorAll('.otp-input').forEach(input => {
                        input.value = '';
                    });
                    document.querySelector('.otp-input').focus();
                }
            }

            async handleResendOTP() {
                const resendBtn = document.getElementById('resend-otp-btn');
                
                // Show loading
                resendBtn.disabled = true;
                resendBtn.innerHTML = `
                    <div class="loading-spinner"></div>
                    <span>${translations[this.currentLang]['resend-otp']}</span>
                `;

                // Simulate resend
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Generate new OTP
                this.generatedOTP = Math.floor(100000 + Math.random() * 900000).toString();
                console.log(`🔐 New OTP: ${this.generatedOTP}`);

                // Show OTP in development
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    alert(`رمز التحقق الجديد: ${this.generatedOTP}\nNew OTP: ${this.generatedOTP}`);
                }

                // Reset button
                resendBtn.disabled = false;
                resendBtn.innerHTML = `
                    <i class="fas fa-redo"></i>
                    <span>${translations[this.currentLang]['resend-otp']}</span>
                `;

                // Clear previous error
                this.showError('otp', false);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });
    </script>
</body>
</html>
