{"ast": null, "code": "// خدمة محاكاة البيانات - تحفظ البيانات في الذاكرة والـ localStorage\n\nclass MockDataService {\n  constructor() {\n    this.displays = [];\n    this.customers = [];\n    this.transactions = [];\n    this.otpCodes = [];\n    this.paymentIntents = [];\n    this.settings = void 0;\n    this.settings = {\n      displayPrice: 50,\n      displayDuration: 300,\n      // 5 دقائق\n      otpExpiryMinutes: 5,\n      companyName: 'نظام إدارة الشاشات الاحترافي',\n      welcomeMessageAr: 'أهلاً وسهلاً بكم',\n      welcomeMessageEn: 'Welcome',\n      maxDisplays: 5\n    };\n    this.initializeData();\n    this.loadFromStorage();\n  }\n  initializeData() {\n    // إنشاء الشاشات الافتراضية\n    for (let i = 1; i <= this.settings.maxDisplays; i++) {\n      this.displays.push({\n        id: `display-${i}`,\n        displayNumber: i,\n        name: `شاشة ${i} - Display ${i}`,\n        status: 'available'\n      });\n    }\n\n    // إضافة بعض البيانات التجريبية\n    this.displays[1].status = 'occupied';\n    this.displays[1].customerName = 'أحمد محمد علي';\n    this.displays[1].startTime = new Date(Date.now() - 2 * 60 * 1000).toISOString();\n    this.displays[1].endTime = new Date(Date.now() + 3 * 60 * 1000).toISOString();\n    this.displays[3].status = 'reserved';\n    this.displays[3].customerName = 'فاطمة أحمد';\n  }\n  loadFromStorage() {\n    try {\n      const savedData = localStorage.getItem('displaySystemData');\n      if (savedData) {\n        const data = JSON.parse(savedData);\n        this.displays = data.displays || this.displays;\n        this.customers = data.customers || [];\n        this.transactions = data.transactions || [];\n        this.settings = {\n          ...this.settings,\n          ...data.settings\n        };\n      }\n    } catch (error) {\n      console.warn('فشل في تحميل البيانات من التخزين المحلي:', error);\n    }\n  }\n  saveToStorage() {\n    try {\n      const data = {\n        displays: this.displays,\n        customers: this.customers,\n        transactions: this.transactions,\n        settings: this.settings,\n        lastUpdated: new Date().toISOString()\n      };\n      localStorage.setItem('displaySystemData', JSON.stringify(data));\n    } catch (error) {\n      console.warn('فشل في حفظ البيانات:', error);\n    }\n  }\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n  generateTransactionNumber() {\n    return 'TXN' + Date.now().toString().slice(-8);\n  }\n  generateOTPCode() {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n  }\n\n  // خدمات الشاشات\n  async getAllDisplays() {\n    // تحديث حالة الشاشات المنتهية الصلاحية\n    this.updateExpiredDisplays();\n    return {\n      success: true,\n      data: [...this.displays],\n      message: 'تم الحصول على الشاشات بنجاح'\n    };\n  }\n  async getDisplay(displayId) {\n    const display = this.displays.find(d => d.id === displayId);\n    if (!display) {\n      return {\n        success: false,\n        error: 'الشاشة غير موجودة'\n      };\n    }\n    return {\n      success: true,\n      data: {\n        ...display\n      },\n      message: 'تم الحصول على الشاشة بنجاح'\n    };\n  }\n  async reserveDisplay(displayId, customerName) {\n    const displayIndex = this.displays.findIndex(d => d.id === displayId);\n    if (displayIndex === -1) {\n      return {\n        success: false,\n        error: 'الشاشة غير موجودة'\n      };\n    }\n    if (this.displays[displayIndex].status !== 'available') {\n      return {\n        success: false,\n        error: 'الشاشة غير متاحة للحجز'\n      };\n    }\n\n    // حجز الشاشة لمدة 10 دقائق\n    this.displays[displayIndex].status = 'reserved';\n    this.displays[displayIndex].customerName = customerName;\n    this.displays[displayIndex].startTime = new Date().toISOString();\n    this.displays[displayIndex].endTime = new Date(Date.now() + 10 * 60 * 1000).toISOString();\n    this.saveToStorage();\n    return {\n      success: true,\n      data: {\n        ...this.displays[displayIndex]\n      },\n      message: 'تم حجز الشاشة بنجاح'\n    };\n  }\n\n  // خدمات المصادقة\n  async sendOTP(phoneNumber, email) {\n    // محاكاة إرسال OTP\n    const otpCode = {\n      id: this.generateId(),\n      phoneNumber,\n      email,\n      code: this.generateOTPCode(),\n      expiresAt: new Date(Date.now() + this.settings.otpExpiryMinutes * 60 * 1000).toISOString(),\n      verified: false,\n      createdAt: new Date().toISOString()\n    };\n\n    // إزالة الرموز القديمة لنفس الرقم\n    this.otpCodes = this.otpCodes.filter(otp => otp.phoneNumber !== phoneNumber);\n    this.otpCodes.push(otpCode);\n\n    // محاكاة إرسال البريد الإلكتروني\n    console.log(`📧 OTP Code sent to ${email}: ${otpCode.code}`);\n\n    // في بيئة التطوير، نعرض الرمز في console\n    if (process.env.NODE_ENV === 'development') {\n      alert(`رمز التحقق: ${otpCode.code}\\n(سيتم إرساله للبريد الإلكتروني في البيئة الحقيقية)`);\n    }\n    return {\n      success: true,\n      data: {\n        expiresAt: otpCode.expiresAt\n      },\n      message: 'تم إرسال رمز التحقق بنجاح'\n    };\n  }\n  async verifyOTP(phoneNumber, code) {\n    const otpCode = this.otpCodes.find(otp => otp.phoneNumber === phoneNumber && otp.code === code && !otp.verified && new Date(otp.expiresAt) > new Date());\n    if (!otpCode) {\n      return {\n        success: false,\n        error: 'رمز التحقق غير صحيح أو منتهي الصلاحية'\n      };\n    }\n\n    // تحديد الرمز كمستخدم\n    otpCode.verified = true;\n\n    // البحث عن العميل أو إنشاء عميل جديد\n    let customer = this.customers.find(c => c.phoneNumber === phoneNumber);\n    if (!customer) {\n      customer = {\n        id: this.generateId(),\n        phoneNumber,\n        email: otpCode.email,\n        createdAt: new Date().toISOString()\n      };\n      this.customers.push(customer);\n    }\n    this.saveToStorage();\n\n    // إنشاء token وهمي\n    const token = `token_${customer.id}_${Date.now()}`;\n    return {\n      success: true,\n      data: {\n        customer,\n        token\n      },\n      message: 'تم التحقق بنجاح'\n    };\n  }\n\n  // خدمات المعاملات\n  async createTransaction(customerId, displayId, customerName, amount, duration) {\n    const customer = this.customers.find(c => c.id === customerId);\n    const display = this.displays.find(d => d.id === displayId);\n    if (!customer || !display) {\n      return {\n        success: false,\n        error: 'بيانات غير صحيحة'\n      };\n    }\n    if (display.status !== 'reserved') {\n      return {\n        success: false,\n        error: 'الشاشة غير محجوزة'\n      };\n    }\n    const transaction = {\n      id: this.generateId(),\n      transactionNumber: this.generateTransactionNumber(),\n      customerId,\n      displayId,\n      customerName,\n      phoneNumber: customer.phoneNumber,\n      email: customer.email,\n      amount,\n      duration,\n      paymentStatus: 'pending',\n      status: 'pending',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    this.transactions.push(transaction);\n    this.saveToStorage();\n    return {\n      success: true,\n      data: transaction,\n      message: 'تم إنشاء المعاملة بنجاح'\n    };\n  }\n  async confirmTransaction(transactionId, paymentIntentId) {\n    const transactionIndex = this.transactions.findIndex(t => t.id === transactionId);\n    if (transactionIndex === -1) {\n      return {\n        success: false,\n        error: 'المعاملة غير موجودة'\n      };\n    }\n    const transaction = this.transactions[transactionIndex];\n    const displayIndex = this.displays.findIndex(d => d.id === transaction.displayId);\n    if (displayIndex === -1) {\n      return {\n        success: false,\n        error: 'الشاشة غير موجودة'\n      };\n    }\n\n    // تحديث المعاملة\n    const startTime = new Date();\n    const endTime = new Date(startTime.getTime() + transaction.duration * 1000);\n    this.transactions[transactionIndex] = {\n      ...transaction,\n      paymentStatus: 'completed',\n      status: 'active',\n      startTime: startTime.toISOString(),\n      endTime: endTime.toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    // تحديث الشاشة\n    this.displays[displayIndex].status = 'occupied';\n    this.displays[displayIndex].transactionId = transactionId;\n    this.displays[displayIndex].startTime = startTime.toISOString();\n    this.displays[displayIndex].endTime = endTime.toISOString();\n    this.saveToStorage();\n    return {\n      success: true,\n      data: this.transactions[transactionIndex],\n      message: 'تم تأكيد المعاملة بنجاح'\n    };\n  }\n\n  // خدمات الدفع\n  async createPaymentIntent(amount, transactionId) {\n    const paymentIntent = {\n      id: `pi_${this.generateId()}`,\n      amount,\n      currency: 'sar',\n      status: 'requires_payment_method',\n      clientSecret: `pi_${this.generateId()}_secret`,\n      transactionId,\n      createdAt: new Date().toISOString()\n    };\n    this.paymentIntents.push(paymentIntent);\n    return {\n      success: true,\n      data: paymentIntent,\n      message: 'تم إنشاء نية الدفع بنجاح'\n    };\n  }\n  async simulateNFCPayment(transactionId) {\n    // محاكاة دفع NFC ناجح\n    await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة وقت المعالجة\n\n    const paymentIntentId = `pi_nfc_${this.generateId()}`;\n    return {\n      success: true,\n      data: {\n        paymentIntentId\n      },\n      message: 'تم الدفع بنجاح عبر NFC'\n    };\n  }\n\n  // خدمات المالك\n  async getDashboardStats() {\n    const today = new Date().toDateString();\n    const todayTransactions = this.transactions.filter(t => new Date(t.createdAt).toDateString() === today && t.paymentStatus === 'completed');\n    const completedTransactions = this.transactions.filter(t => t.paymentStatus === 'completed');\n    const activeTransactions = this.transactions.filter(t => t.status === 'active');\n    const displayStats = {\n      available: this.displays.filter(d => d.status === 'available').length,\n      occupied: this.displays.filter(d => d.status === 'occupied').length,\n      reserved: this.displays.filter(d => d.status === 'reserved').length,\n      maintenance: this.displays.filter(d => d.status === 'maintenance').length\n    };\n    const stats = {\n      today: {\n        transactions: todayTransactions.length,\n        revenue: todayTransactions.reduce((sum, t) => sum + t.amount, 0)\n      },\n      total: {\n        transactions: completedTransactions.length,\n        revenue: completedTransactions.reduce((sum, t) => sum + t.amount, 0)\n      },\n      displays: displayStats,\n      activeTransactions\n    };\n    return {\n      success: true,\n      data: stats,\n      message: 'تم الحصول على الإحصائيات بنجاح'\n    };\n  }\n  async ownerLogin(password) {\n    // كلمة المرور الافتراضية\n    if (password === 'admin123') {\n      return {\n        success: true,\n        data: {\n          token: `owner_token_${Date.now()}`\n        },\n        message: 'تم تسجيل دخول المالك بنجاح'\n      };\n    }\n    return {\n      success: false,\n      error: 'كلمة المرور غير صحيحة'\n    };\n  }\n\n  // تحديث الشاشات المنتهية الصلاحية\n  updateExpiredDisplays() {\n    const now = new Date();\n    this.displays.forEach((display, index) => {\n      if (display.endTime && new Date(display.endTime) <= now) {\n        if (display.status === 'occupied' || display.status === 'reserved') {\n          this.displays[index] = {\n            ...display,\n            status: 'available',\n            customerName: undefined,\n            transactionId: undefined,\n            startTime: undefined,\n            endTime: undefined\n          };\n        }\n      }\n    });\n\n    // تحديث المعاملات المنتهية\n    this.transactions.forEach((transaction, index) => {\n      if (transaction.endTime && new Date(transaction.endTime) <= now && transaction.status === 'active') {\n        this.transactions[index] = {\n          ...transaction,\n          status: 'completed',\n          updatedAt: new Date().toISOString()\n        };\n      }\n    });\n    this.saveToStorage();\n  }\n\n  // الحصول على الإعدادات\n  getSettings() {\n    return {\n      ...this.settings\n    };\n  }\n\n  // تحديث الإعدادات\n  updateSettings(newSettings) {\n    this.settings = {\n      ...this.settings,\n      ...newSettings\n    };\n    this.saveToStorage();\n  }\n}\n\n// إنشاء instance واحد للاستخدام في التطبيق\nexport const mockDataService = new MockDataService();", "map": {"version": 3, "names": ["MockDataService", "constructor", "displays", "customers", "transactions", "otpCodes", "paymentIntents", "settings", "displayPrice", "displayDuration", "otpExpiryMinutes", "companyName", "welcomeMessageAr", "welcomeMessageEn", "maxDisplays", "initializeData", "loadFromStorage", "i", "push", "id", "displayNumber", "name", "status", "customerName", "startTime", "Date", "now", "toISOString", "endTime", "savedData", "localStorage", "getItem", "data", "JSON", "parse", "error", "console", "warn", "saveToStorage", "lastUpdated", "setItem", "stringify", "generateId", "toString", "Math", "random", "substr", "generateTransactionNumber", "slice", "generateOTPCode", "floor", "getAllDisplays", "updateExpiredDisplays", "success", "message", "getDisplay", "displayId", "display", "find", "d", "reserveDisplay", "displayIndex", "findIndex", "sendOTP", "phoneNumber", "email", "otpCode", "code", "expiresAt", "verified", "createdAt", "filter", "otp", "log", "process", "env", "NODE_ENV", "alert", "verifyOTP", "customer", "c", "token", "createTransaction", "customerId", "amount", "duration", "transaction", "transactionNumber", "paymentStatus", "updatedAt", "confirmTransaction", "transactionId", "paymentIntentId", "transactionIndex", "t", "getTime", "createPaymentIntent", "paymentIntent", "currency", "clientSecret", "simulateNFCPayment", "Promise", "resolve", "setTimeout", "getDashboardStats", "today", "toDateString", "todayTransactions", "completedTransactions", "activeTransactions", "displayStats", "available", "length", "occupied", "reserved", "maintenance", "stats", "revenue", "reduce", "sum", "total", "owner<PERSON><PERSON><PERSON>", "password", "for<PERSON>ach", "index", "undefined", "getSettings", "updateSettings", "newSettings", "mockDataService"], "sources": ["D:/برمجة/tste 1/professional-display-system/src/services/mockDataService.ts"], "sourcesContent": ["// خدمة محاكاة البيانات - تحفظ البيانات في الذاكرة والـ localStorage\n\nimport { \n  Display, \n  Customer, \n  Transaction, \n  OTPCode, \n  AppSettings, \n  DashboardStats,\n  PaymentIntent,\n  ApiResponse \n} from '../types';\n\nclass MockDataService {\n  private displays: Display[] = [];\n  private customers: Customer[] = [];\n  private transactions: Transaction[] = [];\n  private otpCodes: OTPCode[] = [];\n  private paymentIntents: PaymentIntent[] = [];\n  private settings: AppSettings;\n\n  constructor() {\n    this.settings = {\n      displayPrice: 50,\n      displayDuration: 300, // 5 دقائق\n      otpExpiryMinutes: 5,\n      companyName: 'نظام إدارة الشاشات الاحترافي',\n      welcomeMessageAr: 'أهلاً وسهلاً بكم',\n      welcomeMessageEn: 'Welcome',\n      maxDisplays: 5\n    };\n\n    this.initializeData();\n    this.loadFromStorage();\n  }\n\n  private initializeData() {\n    // إنشاء الشاشات الافتراضية\n    for (let i = 1; i <= this.settings.maxDisplays; i++) {\n      this.displays.push({\n        id: `display-${i}`,\n        displayNumber: i,\n        name: `شاشة ${i} - Display ${i}`,\n        status: 'available'\n      });\n    }\n\n    // إضافة بعض البيانات التجريبية\n    this.displays[1].status = 'occupied';\n    this.displays[1].customerName = 'أحمد محمد علي';\n    this.displays[1].startTime = new Date(Date.now() - 2 * 60 * 1000).toISOString();\n    this.displays[1].endTime = new Date(Date.now() + 3 * 60 * 1000).toISOString();\n\n    this.displays[3].status = 'reserved';\n    this.displays[3].customerName = 'فاطمة أحمد';\n  }\n\n  private loadFromStorage() {\n    try {\n      const savedData = localStorage.getItem('displaySystemData');\n      if (savedData) {\n        const data = JSON.parse(savedData);\n        this.displays = data.displays || this.displays;\n        this.customers = data.customers || [];\n        this.transactions = data.transactions || [];\n        this.settings = { ...this.settings, ...data.settings };\n      }\n    } catch (error) {\n      console.warn('فشل في تحميل البيانات من التخزين المحلي:', error);\n    }\n  }\n\n  private saveToStorage() {\n    try {\n      const data = {\n        displays: this.displays,\n        customers: this.customers,\n        transactions: this.transactions,\n        settings: this.settings,\n        lastUpdated: new Date().toISOString()\n      };\n      localStorage.setItem('displaySystemData', JSON.stringify(data));\n    } catch (error) {\n      console.warn('فشل في حفظ البيانات:', error);\n    }\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  private generateTransactionNumber(): string {\n    return 'TXN' + Date.now().toString().slice(-8);\n  }\n\n  private generateOTPCode(): string {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n  }\n\n  // خدمات الشاشات\n  async getAllDisplays(): Promise<ApiResponse<Display[]>> {\n    // تحديث حالة الشاشات المنتهية الصلاحية\n    this.updateExpiredDisplays();\n    \n    return {\n      success: true,\n      data: [...this.displays],\n      message: 'تم الحصول على الشاشات بنجاح'\n    };\n  }\n\n  async getDisplay(displayId: string): Promise<ApiResponse<Display>> {\n    const display = this.displays.find(d => d.id === displayId);\n    \n    if (!display) {\n      return {\n        success: false,\n        error: 'الشاشة غير موجودة'\n      };\n    }\n\n    return {\n      success: true,\n      data: { ...display },\n      message: 'تم الحصول على الشاشة بنجاح'\n    };\n  }\n\n  async reserveDisplay(displayId: string, customerName: string): Promise<ApiResponse<Display>> {\n    const displayIndex = this.displays.findIndex(d => d.id === displayId);\n    \n    if (displayIndex === -1) {\n      return {\n        success: false,\n        error: 'الشاشة غير موجودة'\n      };\n    }\n\n    if (this.displays[displayIndex].status !== 'available') {\n      return {\n        success: false,\n        error: 'الشاشة غير متاحة للحجز'\n      };\n    }\n\n    // حجز الشاشة لمدة 10 دقائق\n    this.displays[displayIndex].status = 'reserved';\n    this.displays[displayIndex].customerName = customerName;\n    this.displays[displayIndex].startTime = new Date().toISOString();\n    this.displays[displayIndex].endTime = new Date(Date.now() + 10 * 60 * 1000).toISOString();\n\n    this.saveToStorage();\n\n    return {\n      success: true,\n      data: { ...this.displays[displayIndex] },\n      message: 'تم حجز الشاشة بنجاح'\n    };\n  }\n\n  // خدمات المصادقة\n  async sendOTP(phoneNumber: string, email: string): Promise<ApiResponse<{ expiresAt: string }>> {\n    // محاكاة إرسال OTP\n    const otpCode: OTPCode = {\n      id: this.generateId(),\n      phoneNumber,\n      email,\n      code: this.generateOTPCode(),\n      expiresAt: new Date(Date.now() + this.settings.otpExpiryMinutes * 60 * 1000).toISOString(),\n      verified: false,\n      createdAt: new Date().toISOString()\n    };\n\n    // إزالة الرموز القديمة لنفس الرقم\n    this.otpCodes = this.otpCodes.filter(otp => otp.phoneNumber !== phoneNumber);\n    this.otpCodes.push(otpCode);\n\n    // محاكاة إرسال البريد الإلكتروني\n    console.log(`📧 OTP Code sent to ${email}: ${otpCode.code}`);\n    \n    // في بيئة التطوير، نعرض الرمز في console\n    if (process.env.NODE_ENV === 'development') {\n      alert(`رمز التحقق: ${otpCode.code}\\n(سيتم إرساله للبريد الإلكتروني في البيئة الحقيقية)`);\n    }\n\n    return {\n      success: true,\n      data: { expiresAt: otpCode.expiresAt },\n      message: 'تم إرسال رمز التحقق بنجاح'\n    };\n  }\n\n  async verifyOTP(phoneNumber: string, code: string): Promise<ApiResponse<{ customer: Customer; token: string }>> {\n    const otpCode = this.otpCodes.find(\n      otp => otp.phoneNumber === phoneNumber && \n             otp.code === code && \n             !otp.verified &&\n             new Date(otp.expiresAt) > new Date()\n    );\n\n    if (!otpCode) {\n      return {\n        success: false,\n        error: 'رمز التحقق غير صحيح أو منتهي الصلاحية'\n      };\n    }\n\n    // تحديد الرمز كمستخدم\n    otpCode.verified = true;\n\n    // البحث عن العميل أو إنشاء عميل جديد\n    let customer = this.customers.find(c => c.phoneNumber === phoneNumber);\n    \n    if (!customer) {\n      customer = {\n        id: this.generateId(),\n        phoneNumber,\n        email: otpCode.email,\n        createdAt: new Date().toISOString()\n      };\n      this.customers.push(customer);\n    }\n\n    this.saveToStorage();\n\n    // إنشاء token وهمي\n    const token = `token_${customer.id}_${Date.now()}`;\n\n    return {\n      success: true,\n      data: { customer, token },\n      message: 'تم التحقق بنجاح'\n    };\n  }\n\n  // خدمات المعاملات\n  async createTransaction(\n    customerId: string, \n    displayId: string, \n    customerName: string, \n    amount: number, \n    duration: number\n  ): Promise<ApiResponse<Transaction>> {\n    const customer = this.customers.find(c => c.id === customerId);\n    const display = this.displays.find(d => d.id === displayId);\n\n    if (!customer || !display) {\n      return {\n        success: false,\n        error: 'بيانات غير صحيحة'\n      };\n    }\n\n    if (display.status !== 'reserved') {\n      return {\n        success: false,\n        error: 'الشاشة غير محجوزة'\n      };\n    }\n\n    const transaction: Transaction = {\n      id: this.generateId(),\n      transactionNumber: this.generateTransactionNumber(),\n      customerId,\n      displayId,\n      customerName,\n      phoneNumber: customer.phoneNumber,\n      email: customer.email,\n      amount,\n      duration,\n      paymentStatus: 'pending',\n      status: 'pending',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    this.transactions.push(transaction);\n    this.saveToStorage();\n\n    return {\n      success: true,\n      data: transaction,\n      message: 'تم إنشاء المعاملة بنجاح'\n    };\n  }\n\n  async confirmTransaction(transactionId: string, paymentIntentId: string): Promise<ApiResponse<Transaction>> {\n    const transactionIndex = this.transactions.findIndex(t => t.id === transactionId);\n    \n    if (transactionIndex === -1) {\n      return {\n        success: false,\n        error: 'المعاملة غير موجودة'\n      };\n    }\n\n    const transaction = this.transactions[transactionIndex];\n    const displayIndex = this.displays.findIndex(d => d.id === transaction.displayId);\n\n    if (displayIndex === -1) {\n      return {\n        success: false,\n        error: 'الشاشة غير موجودة'\n      };\n    }\n\n    // تحديث المعاملة\n    const startTime = new Date();\n    const endTime = new Date(startTime.getTime() + transaction.duration * 1000);\n\n    this.transactions[transactionIndex] = {\n      ...transaction,\n      paymentStatus: 'completed',\n      status: 'active',\n      startTime: startTime.toISOString(),\n      endTime: endTime.toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    // تحديث الشاشة\n    this.displays[displayIndex].status = 'occupied';\n    this.displays[displayIndex].transactionId = transactionId;\n    this.displays[displayIndex].startTime = startTime.toISOString();\n    this.displays[displayIndex].endTime = endTime.toISOString();\n\n    this.saveToStorage();\n\n    return {\n      success: true,\n      data: this.transactions[transactionIndex],\n      message: 'تم تأكيد المعاملة بنجاح'\n    };\n  }\n\n  // خدمات الدفع\n  async createPaymentIntent(amount: number, transactionId: string): Promise<ApiResponse<PaymentIntent>> {\n    const paymentIntent: PaymentIntent = {\n      id: `pi_${this.generateId()}`,\n      amount,\n      currency: 'sar',\n      status: 'requires_payment_method',\n      clientSecret: `pi_${this.generateId()}_secret`,\n      transactionId,\n      createdAt: new Date().toISOString()\n    };\n\n    this.paymentIntents.push(paymentIntent);\n\n    return {\n      success: true,\n      data: paymentIntent,\n      message: 'تم إنشاء نية الدفع بنجاح'\n    };\n  }\n\n  async simulateNFCPayment(transactionId: string): Promise<ApiResponse<{ paymentIntentId: string }>> {\n    // محاكاة دفع NFC ناجح\n    await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة وقت المعالجة\n\n    const paymentIntentId = `pi_nfc_${this.generateId()}`;\n\n    return {\n      success: true,\n      data: { paymentIntentId },\n      message: 'تم الدفع بنجاح عبر NFC'\n    };\n  }\n\n  // خدمات المالك\n  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {\n    const today = new Date().toDateString();\n    const todayTransactions = this.transactions.filter(\n      t => new Date(t.createdAt).toDateString() === today && t.paymentStatus === 'completed'\n    );\n\n    const completedTransactions = this.transactions.filter(t => t.paymentStatus === 'completed');\n    const activeTransactions = this.transactions.filter(t => t.status === 'active');\n\n    const displayStats = {\n      available: this.displays.filter(d => d.status === 'available').length,\n      occupied: this.displays.filter(d => d.status === 'occupied').length,\n      reserved: this.displays.filter(d => d.status === 'reserved').length,\n      maintenance: this.displays.filter(d => d.status === 'maintenance').length\n    };\n\n    const stats: DashboardStats = {\n      today: {\n        transactions: todayTransactions.length,\n        revenue: todayTransactions.reduce((sum, t) => sum + t.amount, 0)\n      },\n      total: {\n        transactions: completedTransactions.length,\n        revenue: completedTransactions.reduce((sum, t) => sum + t.amount, 0)\n      },\n      displays: displayStats,\n      activeTransactions\n    };\n\n    return {\n      success: true,\n      data: stats,\n      message: 'تم الحصول على الإحصائيات بنجاح'\n    };\n  }\n\n  async ownerLogin(password: string): Promise<ApiResponse<{ token: string }>> {\n    // كلمة المرور الافتراضية\n    if (password === 'admin123') {\n      return {\n        success: true,\n        data: { token: `owner_token_${Date.now()}` },\n        message: 'تم تسجيل دخول المالك بنجاح'\n      };\n    }\n\n    return {\n      success: false,\n      error: 'كلمة المرور غير صحيحة'\n    };\n  }\n\n  // تحديث الشاشات المنتهية الصلاحية\n  private updateExpiredDisplays() {\n    const now = new Date();\n    \n    this.displays.forEach((display, index) => {\n      if (display.endTime && new Date(display.endTime) <= now) {\n        if (display.status === 'occupied' || display.status === 'reserved') {\n          this.displays[index] = {\n            ...display,\n            status: 'available',\n            customerName: undefined,\n            transactionId: undefined,\n            startTime: undefined,\n            endTime: undefined\n          };\n        }\n      }\n    });\n\n    // تحديث المعاملات المنتهية\n    this.transactions.forEach((transaction, index) => {\n      if (transaction.endTime && new Date(transaction.endTime) <= now && transaction.status === 'active') {\n        this.transactions[index] = {\n          ...transaction,\n          status: 'completed',\n          updatedAt: new Date().toISOString()\n        };\n      }\n    });\n\n    this.saveToStorage();\n  }\n\n  // الحصول على الإعدادات\n  getSettings(): AppSettings {\n    return { ...this.settings };\n  }\n\n  // تحديث الإعدادات\n  updateSettings(newSettings: Partial<AppSettings>): void {\n    this.settings = { ...this.settings, ...newSettings };\n    this.saveToStorage();\n  }\n}\n\n// إنشاء instance واحد للاستخدام في التطبيق\nexport const mockDataService = new MockDataService();\n"], "mappings": "AAAA;;AAaA,MAAMA,eAAe,CAAC;EAQpBC,WAAWA,CAAA,EAAG;IAAA,KAPNC,QAAQ,GAAc,EAAE;IAAA,KACxBC,SAAS,GAAe,EAAE;IAAA,KAC1BC,YAAY,GAAkB,EAAE;IAAA,KAChCC,QAAQ,GAAc,EAAE;IAAA,KACxBC,cAAc,GAAoB,EAAE;IAAA,KACpCC,QAAQ;IAGd,IAAI,CAACA,QAAQ,GAAG;MACdC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE,GAAG;MAAE;MACtBC,gBAAgB,EAAE,CAAC;MACnBC,WAAW,EAAE,8BAA8B;MAC3CC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,SAAS;MAC3BC,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB;EAEQD,cAAcA,CAAA,EAAG;IACvB;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACV,QAAQ,CAACO,WAAW,EAAEG,CAAC,EAAE,EAAE;MACnD,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC;QACjBC,EAAE,EAAE,WAAWF,CAAC,EAAE;QAClBG,aAAa,EAAEH,CAAC;QAChBI,IAAI,EAAE,QAAQJ,CAAC,cAAcA,CAAC,EAAE;QAChCK,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC,CAACoB,MAAM,GAAG,UAAU;IACpC,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC,CAACqB,YAAY,GAAG,eAAe;IAC/C,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAACsB,SAAS,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/E,IAAI,CAACzB,QAAQ,CAAC,CAAC,CAAC,CAAC0B,OAAO,GAAG,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;IAE7E,IAAI,CAACzB,QAAQ,CAAC,CAAC,CAAC,CAACoB,MAAM,GAAG,UAAU;IACpC,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC,CAACqB,YAAY,GAAG,YAAY;EAC9C;EAEQP,eAAeA,CAAA,EAAG;IACxB,IAAI;MACF,MAAMa,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;MAC3D,IAAIF,SAAS,EAAE;QACb,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;QAClC,IAAI,CAAC3B,QAAQ,GAAG8B,IAAI,CAAC9B,QAAQ,IAAI,IAAI,CAACA,QAAQ;QAC9C,IAAI,CAACC,SAAS,GAAG6B,IAAI,CAAC7B,SAAS,IAAI,EAAE;QACrC,IAAI,CAACC,YAAY,GAAG4B,IAAI,CAAC5B,YAAY,IAAI,EAAE;QAC3C,IAAI,CAACG,QAAQ,GAAG;UAAE,GAAG,IAAI,CAACA,QAAQ;UAAE,GAAGyB,IAAI,CAACzB;QAAS,CAAC;MACxD;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,KAAK,CAAC;IACjE;EACF;EAEQG,aAAaA,CAAA,EAAG;IACtB,IAAI;MACF,MAAMN,IAAI,GAAG;QACX9B,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BG,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBgC,WAAW,EAAE,IAAId,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;MACtC,CAAC;MACDG,YAAY,CAACU,OAAO,CAAC,mBAAmB,EAAEP,IAAI,CAACQ,SAAS,CAACT,IAAI,CAAC,CAAC;IACjE,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAEF,KAAK,CAAC;IAC7C;EACF;EAEQO,UAAUA,CAAA,EAAW;IAC3B,OAAOjB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACiB,QAAQ,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC;EACvE;EAEQC,yBAAyBA,CAAA,EAAW;IAC1C,OAAO,KAAK,GAAGtB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;EAChD;EAEQC,eAAeA,CAAA,EAAW;IAChC,OAAOL,IAAI,CAACM,KAAK,CAAC,MAAM,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAACF,QAAQ,CAAC,CAAC;EAC/D;;EAEA;EACA,MAAMQ,cAAcA,CAAA,EAAoC;IACtD;IACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAE5B,OAAO;MACLC,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC9B,QAAQ,CAAC;MACxBoD,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMC,UAAUA,CAACC,SAAiB,EAAiC;IACjE,MAAMC,OAAO,GAAG,IAAI,CAACvD,QAAQ,CAACwD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAKqC,SAAS,CAAC;IAE3D,IAAI,CAACC,OAAO,EAAE;MACZ,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;IAEA,OAAO;MACLkB,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE;QAAE,GAAGyB;MAAQ,CAAC;MACpBH,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMM,cAAcA,CAACJ,SAAiB,EAAEjC,YAAoB,EAAiC;IAC3F,MAAMsC,YAAY,GAAG,IAAI,CAAC3D,QAAQ,CAAC4D,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAKqC,SAAS,CAAC;IAErE,IAAIK,YAAY,KAAK,CAAC,CAAC,EAAE;MACvB,OAAO;QACLR,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;IAEA,IAAI,IAAI,CAACjC,QAAQ,CAAC2D,YAAY,CAAC,CAACvC,MAAM,KAAK,WAAW,EAAE;MACtD,OAAO;QACL+B,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA,IAAI,CAACjC,QAAQ,CAAC2D,YAAY,CAAC,CAACvC,MAAM,GAAG,UAAU;IAC/C,IAAI,CAACpB,QAAQ,CAAC2D,YAAY,CAAC,CAACtC,YAAY,GAAGA,YAAY;IACvD,IAAI,CAACrB,QAAQ,CAAC2D,YAAY,CAAC,CAACrC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;IAChE,IAAI,CAACzB,QAAQ,CAAC2D,YAAY,CAAC,CAACjC,OAAO,GAAG,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;IAEzF,IAAI,CAACW,aAAa,CAAC,CAAC;IAEpB,OAAO;MACLe,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE;QAAE,GAAG,IAAI,CAAC9B,QAAQ,CAAC2D,YAAY;MAAE,CAAC;MACxCP,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACA,MAAMS,OAAOA,CAACC,WAAmB,EAAEC,KAAa,EAA+C;IAC7F;IACA,MAAMC,OAAgB,GAAG;MACvB/C,EAAE,EAAE,IAAI,CAACuB,UAAU,CAAC,CAAC;MACrBsB,WAAW;MACXC,KAAK;MACLE,IAAI,EAAE,IAAI,CAAClB,eAAe,CAAC,CAAC;MAC5BmB,SAAS,EAAE,IAAI3C,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACnB,QAAQ,CAACG,gBAAgB,GAAG,EAAE,GAAG,IAAI,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC1F0C,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI7C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;IACpC,CAAC;;IAED;IACA,IAAI,CAACtB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACkE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACR,WAAW,KAAKA,WAAW,CAAC;IAC5E,IAAI,CAAC3D,QAAQ,CAACa,IAAI,CAACgD,OAAO,CAAC;;IAE3B;IACA9B,OAAO,CAACqC,GAAG,CAAC,uBAAuBR,KAAK,KAAKC,OAAO,CAACC,IAAI,EAAE,CAAC;;IAE5D;IACA,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1CC,KAAK,CAAC,eAAeX,OAAO,CAACC,IAAI,sDAAsD,CAAC;IAC1F;IAEA,OAAO;MACLd,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE;QAAEoC,SAAS,EAAEF,OAAO,CAACE;MAAU,CAAC;MACtCd,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMwB,SAASA,CAACd,WAAmB,EAAEG,IAAY,EAA+D;IAC9G,MAAMD,OAAO,GAAG,IAAI,CAAC7D,QAAQ,CAACqD,IAAI,CAChCc,GAAG,IAAIA,GAAG,CAACR,WAAW,KAAKA,WAAW,IAC/BQ,GAAG,CAACL,IAAI,KAAKA,IAAI,IACjB,CAACK,GAAG,CAACH,QAAQ,IACb,IAAI5C,IAAI,CAAC+C,GAAG,CAACJ,SAAS,CAAC,GAAG,IAAI3C,IAAI,CAAC,CAC5C,CAAC;IAED,IAAI,CAACyC,OAAO,EAAE;MACZ,OAAO;QACLb,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA+B,OAAO,CAACG,QAAQ,GAAG,IAAI;;IAEvB;IACA,IAAIU,QAAQ,GAAG,IAAI,CAAC5E,SAAS,CAACuD,IAAI,CAACsB,CAAC,IAAIA,CAAC,CAAChB,WAAW,KAAKA,WAAW,CAAC;IAEtE,IAAI,CAACe,QAAQ,EAAE;MACbA,QAAQ,GAAG;QACT5D,EAAE,EAAE,IAAI,CAACuB,UAAU,CAAC,CAAC;QACrBsB,WAAW;QACXC,KAAK,EAAEC,OAAO,CAACD,KAAK;QACpBK,SAAS,EAAE,IAAI7C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;MACpC,CAAC;MACD,IAAI,CAACxB,SAAS,CAACe,IAAI,CAAC6D,QAAQ,CAAC;IAC/B;IAEA,IAAI,CAACzC,aAAa,CAAC,CAAC;;IAEpB;IACA,MAAM2C,KAAK,GAAG,SAASF,QAAQ,CAAC5D,EAAE,IAAIM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IAElD,OAAO;MACL2B,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE;QAAE+C,QAAQ;QAAEE;MAAM,CAAC;MACzB3B,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACA,MAAM4B,iBAAiBA,CACrBC,UAAkB,EAClB3B,SAAiB,EACjBjC,YAAoB,EACpB6D,MAAc,EACdC,QAAgB,EACmB;IACnC,MAAMN,QAAQ,GAAG,IAAI,CAAC5E,SAAS,CAACuD,IAAI,CAACsB,CAAC,IAAIA,CAAC,CAAC7D,EAAE,KAAKgE,UAAU,CAAC;IAC9D,MAAM1B,OAAO,GAAG,IAAI,CAACvD,QAAQ,CAACwD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAKqC,SAAS,CAAC;IAE3D,IAAI,CAACuB,QAAQ,IAAI,CAACtB,OAAO,EAAE;MACzB,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;IAEA,IAAIsB,OAAO,CAACnC,MAAM,KAAK,UAAU,EAAE;MACjC,OAAO;QACL+B,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;IAEA,MAAMmD,WAAwB,GAAG;MAC/BnE,EAAE,EAAE,IAAI,CAACuB,UAAU,CAAC,CAAC;MACrB6C,iBAAiB,EAAE,IAAI,CAACxC,yBAAyB,CAAC,CAAC;MACnDoC,UAAU;MACV3B,SAAS;MACTjC,YAAY;MACZyC,WAAW,EAAEe,QAAQ,CAACf,WAAW;MACjCC,KAAK,EAAEc,QAAQ,CAACd,KAAK;MACrBmB,MAAM;MACNC,QAAQ;MACRG,aAAa,EAAE,SAAS;MACxBlE,MAAM,EAAE,SAAS;MACjBgD,SAAS,EAAE,IAAI7C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnC8D,SAAS,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;IACpC,CAAC;IAED,IAAI,CAACvB,YAAY,CAACc,IAAI,CAACoE,WAAW,CAAC;IACnC,IAAI,CAAChD,aAAa,CAAC,CAAC;IAEpB,OAAO;MACLe,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAEsD,WAAW;MACjBhC,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMoC,kBAAkBA,CAACC,aAAqB,EAAEC,eAAuB,EAAqC;IAC1G,MAAMC,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAAC0D,SAAS,CAACgC,CAAC,IAAIA,CAAC,CAAC3E,EAAE,KAAKwE,aAAa,CAAC;IAEjF,IAAIE,gBAAgB,KAAK,CAAC,CAAC,EAAE;MAC3B,OAAO;QACLxC,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;IAEA,MAAMmD,WAAW,GAAG,IAAI,CAAClF,YAAY,CAACyF,gBAAgB,CAAC;IACvD,MAAMhC,YAAY,GAAG,IAAI,CAAC3D,QAAQ,CAAC4D,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAKmE,WAAW,CAAC9B,SAAS,CAAC;IAEjF,IAAIK,YAAY,KAAK,CAAC,CAAC,EAAE;MACvB,OAAO;QACLR,OAAO,EAAE,KAAK;QACdlB,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA,MAAMX,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC5B,MAAMG,OAAO,GAAG,IAAIH,IAAI,CAACD,SAAS,CAACuE,OAAO,CAAC,CAAC,GAAGT,WAAW,CAACD,QAAQ,GAAG,IAAI,CAAC;IAE3E,IAAI,CAACjF,YAAY,CAACyF,gBAAgB,CAAC,GAAG;MACpC,GAAGP,WAAW;MACdE,aAAa,EAAE,WAAW;MAC1BlE,MAAM,EAAE,QAAQ;MAChBE,SAAS,EAAEA,SAAS,CAACG,WAAW,CAAC,CAAC;MAClCC,OAAO,EAAEA,OAAO,CAACD,WAAW,CAAC,CAAC;MAC9B8D,SAAS,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;IACpC,CAAC;;IAED;IACA,IAAI,CAACzB,QAAQ,CAAC2D,YAAY,CAAC,CAACvC,MAAM,GAAG,UAAU;IAC/C,IAAI,CAACpB,QAAQ,CAAC2D,YAAY,CAAC,CAAC8B,aAAa,GAAGA,aAAa;IACzD,IAAI,CAACzF,QAAQ,CAAC2D,YAAY,CAAC,CAACrC,SAAS,GAAGA,SAAS,CAACG,WAAW,CAAC,CAAC;IAC/D,IAAI,CAACzB,QAAQ,CAAC2D,YAAY,CAAC,CAACjC,OAAO,GAAGA,OAAO,CAACD,WAAW,CAAC,CAAC;IAE3D,IAAI,CAACW,aAAa,CAAC,CAAC;IAEpB,OAAO;MACLe,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE,IAAI,CAAC5B,YAAY,CAACyF,gBAAgB,CAAC;MACzCvC,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACA,MAAM0C,mBAAmBA,CAACZ,MAAc,EAAEO,aAAqB,EAAuC;IACpG,MAAMM,aAA4B,GAAG;MACnC9E,EAAE,EAAE,MAAM,IAAI,CAACuB,UAAU,CAAC,CAAC,EAAE;MAC7B0C,MAAM;MACNc,QAAQ,EAAE,KAAK;MACf5E,MAAM,EAAE,yBAAyB;MACjC6E,YAAY,EAAE,MAAM,IAAI,CAACzD,UAAU,CAAC,CAAC,SAAS;MAC9CiD,aAAa;MACbrB,SAAS,EAAE,IAAI7C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;IACpC,CAAC;IAED,IAAI,CAACrB,cAAc,CAACY,IAAI,CAAC+E,aAAa,CAAC;IAEvC,OAAO;MACL5C,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAEiE,aAAa;MACnB3C,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAM8C,kBAAkBA,CAACT,aAAqB,EAAqD;IACjG;IACA,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;IAEzD,MAAMV,eAAe,GAAG,UAAU,IAAI,CAAClD,UAAU,CAAC,CAAC,EAAE;IAErD,OAAO;MACLW,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAE;QAAE4D;MAAgB,CAAC;MACzBtC,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACA,MAAMkD,iBAAiBA,CAAA,EAAyC;IAC9D,MAAMC,KAAK,GAAG,IAAIhF,IAAI,CAAC,CAAC,CAACiF,YAAY,CAAC,CAAC;IACvC,MAAMC,iBAAiB,GAAG,IAAI,CAACvG,YAAY,CAACmE,MAAM,CAChDuB,CAAC,IAAI,IAAIrE,IAAI,CAACqE,CAAC,CAACxB,SAAS,CAAC,CAACoC,YAAY,CAAC,CAAC,KAAKD,KAAK,IAAIX,CAAC,CAACN,aAAa,KAAK,WAC7E,CAAC;IAED,MAAMoB,qBAAqB,GAAG,IAAI,CAACxG,YAAY,CAACmE,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAACN,aAAa,KAAK,WAAW,CAAC;IAC5F,MAAMqB,kBAAkB,GAAG,IAAI,CAACzG,YAAY,CAACmE,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAACxE,MAAM,KAAK,QAAQ,CAAC;IAE/E,MAAMwF,YAAY,GAAG;MACnBC,SAAS,EAAE,IAAI,CAAC7G,QAAQ,CAACqE,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,WAAW,CAAC,CAAC0F,MAAM;MACrEC,QAAQ,EAAE,IAAI,CAAC/G,QAAQ,CAACqE,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,UAAU,CAAC,CAAC0F,MAAM;MACnEE,QAAQ,EAAE,IAAI,CAAChH,QAAQ,CAACqE,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,UAAU,CAAC,CAAC0F,MAAM;MACnEG,WAAW,EAAE,IAAI,CAACjH,QAAQ,CAACqE,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAK,aAAa,CAAC,CAAC0F;IACrE,CAAC;IAED,MAAMI,KAAqB,GAAG;MAC5BX,KAAK,EAAE;QACLrG,YAAY,EAAEuG,iBAAiB,CAACK,MAAM;QACtCK,OAAO,EAAEV,iBAAiB,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEzB,CAAC,KAAKyB,GAAG,GAAGzB,CAAC,CAACV,MAAM,EAAE,CAAC;MACjE,CAAC;MACDoC,KAAK,EAAE;QACLpH,YAAY,EAAEwG,qBAAqB,CAACI,MAAM;QAC1CK,OAAO,EAAET,qBAAqB,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEzB,CAAC,KAAKyB,GAAG,GAAGzB,CAAC,CAACV,MAAM,EAAE,CAAC;MACrE,CAAC;MACDlF,QAAQ,EAAE4G,YAAY;MACtBD;IACF,CAAC;IAED,OAAO;MACLxD,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAEoF,KAAK;MACX9D,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMmE,UAAUA,CAACC,QAAgB,EAA2C;IAC1E;IACA,IAAIA,QAAQ,KAAK,UAAU,EAAE;MAC3B,OAAO;QACLrE,OAAO,EAAE,IAAI;QACbrB,IAAI,EAAE;UAAEiD,KAAK,EAAE,eAAexD,IAAI,CAACC,GAAG,CAAC,CAAC;QAAG,CAAC;QAC5C4B,OAAO,EAAE;MACX,CAAC;IACH;IAEA,OAAO;MACLD,OAAO,EAAE,KAAK;MACdlB,KAAK,EAAE;IACT,CAAC;EACH;;EAEA;EACQiB,qBAAqBA,CAAA,EAAG;IAC9B,MAAM1B,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IAEtB,IAAI,CAACvB,QAAQ,CAACyH,OAAO,CAAC,CAAClE,OAAO,EAAEmE,KAAK,KAAK;MACxC,IAAInE,OAAO,CAAC7B,OAAO,IAAI,IAAIH,IAAI,CAACgC,OAAO,CAAC7B,OAAO,CAAC,IAAIF,GAAG,EAAE;QACvD,IAAI+B,OAAO,CAACnC,MAAM,KAAK,UAAU,IAAImC,OAAO,CAACnC,MAAM,KAAK,UAAU,EAAE;UAClE,IAAI,CAACpB,QAAQ,CAAC0H,KAAK,CAAC,GAAG;YACrB,GAAGnE,OAAO;YACVnC,MAAM,EAAE,WAAW;YACnBC,YAAY,EAAEsG,SAAS;YACvBlC,aAAa,EAAEkC,SAAS;YACxBrG,SAAS,EAAEqG,SAAS;YACpBjG,OAAO,EAAEiG;UACX,CAAC;QACH;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACzH,YAAY,CAACuH,OAAO,CAAC,CAACrC,WAAW,EAAEsC,KAAK,KAAK;MAChD,IAAItC,WAAW,CAAC1D,OAAO,IAAI,IAAIH,IAAI,CAAC6D,WAAW,CAAC1D,OAAO,CAAC,IAAIF,GAAG,IAAI4D,WAAW,CAAChE,MAAM,KAAK,QAAQ,EAAE;QAClG,IAAI,CAAClB,YAAY,CAACwH,KAAK,CAAC,GAAG;UACzB,GAAGtC,WAAW;UACdhE,MAAM,EAAE,WAAW;UACnBmE,SAAS,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;QACpC,CAAC;MACH;IACF,CAAC,CAAC;IAEF,IAAI,CAACW,aAAa,CAAC,CAAC;EACtB;;EAEA;EACAwF,WAAWA,CAAA,EAAgB;IACzB,OAAO;MAAE,GAAG,IAAI,CAACvH;IAAS,CAAC;EAC7B;;EAEA;EACAwH,cAAcA,CAACC,WAAiC,EAAQ;IACtD,IAAI,CAACzH,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGyH;IAAY,CAAC;IACpD,IAAI,CAAC1F,aAAa,CAAC,CAAC;EACtB;AACF;;AAEA;AACA,OAAO,MAAM2F,eAAe,GAAG,IAAIjI,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}