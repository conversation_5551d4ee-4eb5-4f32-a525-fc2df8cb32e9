{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\professional-display-system\\\\src\\\\components\\\\Button.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  onClick,\n  type = 'button',\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  fullWidth = false,\n  className = '',\n  style = {}\n}) => {\n  const getVariantClass = () => {\n    switch (variant) {\n      case 'secondary':\n        return 'btn-secondary';\n      case 'success':\n        return 'btn-success';\n      case 'danger':\n        return 'btn-danger';\n      case 'warning':\n        return 'btn-warning';\n      case 'info':\n        return 'btn-info';\n      default:\n        return 'btn-primary';\n    }\n  };\n  const getSizeClass = () => {\n    switch (size) {\n      case 'small':\n        return 'btn-small';\n      case 'large':\n        return 'btn-large';\n      default:\n        return 'btn-medium';\n    }\n  };\n  const classes = ['btn', getVariantClass(), getSizeClass(), fullWidth ? 'btn-full-width' : '', loading ? 'btn-loading' : '', disabled ? 'btn-disabled' : '', className].filter(Boolean).join(' ');\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: classes,\n    onClick: onClick,\n    disabled: disabled || loading,\n    style: style,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"btn-spinner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-mini\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: loading ? 'btn-content-loading' : 'btn-content',\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "onClick", "type", "variant", "size", "disabled", "loading", "fullWidth", "className", "style", "getVariantClass", "getSizeClass", "classes", "filter", "Boolean", "join", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/professional-display-system/src/components/Button.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;\n  type?: 'button' | 'submit' | 'reset';\n  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';\n  size?: 'small' | 'medium' | 'large';\n  disabled?: boolean;\n  loading?: boolean;\n  fullWidth?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  onClick,\n  type = 'button',\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  fullWidth = false,\n  className = '',\n  style = {}\n}) => {\n  const getVariantClass = () => {\n    switch (variant) {\n      case 'secondary': return 'btn-secondary';\n      case 'success': return 'btn-success';\n      case 'danger': return 'btn-danger';\n      case 'warning': return 'btn-warning';\n      case 'info': return 'btn-info';\n      default: return 'btn-primary';\n    }\n  };\n\n  const getSizeClass = () => {\n    switch (size) {\n      case 'small': return 'btn-small';\n      case 'large': return 'btn-large';\n      default: return 'btn-medium';\n    }\n  };\n\n  const classes = [\n    'btn',\n    getVariantClass(),\n    getSizeClass(),\n    fullWidth ? 'btn-full-width' : '',\n    loading ? 'btn-loading' : '',\n    disabled ? 'btn-disabled' : '',\n    className\n  ].filter(Boolean).join(' ');\n\n  return (\n    <button\n      type={type}\n      className={classes}\n      onClick={onClick}\n      disabled={disabled || loading}\n      style={style}\n    >\n      {loading && (\n        <span className=\"btn-spinner\">\n          <div className=\"spinner-mini\"></div>\n        </span>\n      )}\n      <span className={loading ? 'btn-content-loading' : 'btn-content'}>\n        {children}\n      </span>\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,QAAQ;EACfC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQP,OAAO;MACb,KAAK,WAAW;QAAE,OAAO,eAAe;MACxC,KAAK,SAAS;QAAE,OAAO,aAAa;MACpC,KAAK,QAAQ;QAAE,OAAO,YAAY;MAClC,KAAK,SAAS;QAAE,OAAO,aAAa;MACpC,KAAK,MAAM;QAAE,OAAO,UAAU;MAC9B;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQP,IAAI;MACV,KAAK,OAAO;QAAE,OAAO,WAAW;MAChC,KAAK,OAAO;QAAE,OAAO,WAAW;MAChC;QAAS,OAAO,YAAY;IAC9B;EACF,CAAC;EAED,MAAMQ,OAAO,GAAG,CACd,KAAK,EACLF,eAAe,CAAC,CAAC,EACjBC,YAAY,CAAC,CAAC,EACdJ,SAAS,GAAG,gBAAgB,GAAG,EAAE,EACjCD,OAAO,GAAG,aAAa,GAAG,EAAE,EAC5BD,QAAQ,GAAG,cAAc,GAAG,EAAE,EAC9BG,SAAS,CACV,CAACK,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3B,oBACEjB,OAAA;IACEI,IAAI,EAAEA,IAAK;IACXM,SAAS,EAAEI,OAAQ;IACnBX,OAAO,EAAEA,OAAQ;IACjBI,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAC9BG,KAAK,EAAEA,KAAM;IAAAT,QAAA,GAEZM,OAAO,iBACNR,OAAA;MAAMU,SAAS,EAAC,aAAa;MAAAR,QAAA,eAC3BF,OAAA;QAAKU,SAAS,EAAC;MAAc;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACP,eACDrB,OAAA;MAAMU,SAAS,EAAEF,OAAO,GAAG,qBAAqB,GAAG,aAAc;MAAAN,QAAA,EAC9DA;IAAQ;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAACC,EAAA,GA3DIrB,MAA6B;AA6DnC,eAAeA,MAAM;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}