import React, { useState, useEffect } from 'react';
import { useNavigate } from '../Router';
import { useApp, useTranslation } from '../context/AppContext';
import { useNotification } from '../components/Notification';
import { mockDataService } from '../services/mockDataService';
import { Display } from '../types';
import Button from '../components/Button';
import LoadingSpinner from '../components/LoadingSpinner';

const DisplaySelectionPage: React.FC = () => {
  const navigate = useNavigate();
  const { state, selectDisplay } = useApp();
  const { t } = useTranslation();
  const { showNotification } = useNotification();
  
  const [displays, setDisplays] = useState<Display[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDisplayId, setSelectedDisplayId] = useState<string | null>(null);

  useEffect(() => {
    loadDisplays();
    
    // تحديث الشاشات كل 30 ثانية
    const interval = setInterval(loadDisplays, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDisplays = async () => {
    try {
      const response = await mockDataService.getAllDisplays();
      if (response.success && response.data) {
        setDisplays(response.data);
      } else {
        showNotification({
          type: 'error',
          message: response.error || t('serverError')
        });
      }
    } catch (error) {
      showNotification({
        type: 'error',
        message: t('networkError')
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDisplaySelect = async (display: Display) => {
    if (display.status !== 'available') {
      showNotification({
        type: 'warning',
        message: state.language === 'ar' 
          ? 'هذه الشاشة غير متاحة حالياً' 
          : 'This display is not available'
      });
      return;
    }

    setSelectedDisplayId(display.id);
    selectDisplay(display);
    
    // الانتقال لصفحة تسجيل الدخول
    navigate('/login');
  };

  const getStatusColor = (status: Display['status']) => {
    switch (status) {
      case 'available': return '#28a745';
      case 'occupied': return '#dc3545';
      case 'reserved': return '#ffc107';
      case 'maintenance': return '#6c757d';
      default: return '#ddd';
    }
  };

  const getStatusText = (status: Display['status']) => {
    switch (status) {
      case 'available': return t('available');
      case 'occupied': return t('occupied');
      case 'reserved': return t('reserved');
      case 'maintenance': return t('maintenance');
      default: return status;
    }
  };

  const formatTimeRemaining = (endTime?: string) => {
    if (!endTime) return '';
    
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();
    
    if (diff <= 0) return '';
    
    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="page-container">
        <LoadingSpinner 
          fullScreen 
          text={t('loading')}
          size="large"
        />
      </div>
    );
  }

  return (
    <div className={`display-selection-page ${state.language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="page-container">
        {/* الهيدر */}
        <div className="page-header">
          <Button
            variant="secondary"
            onClick={() => navigate('/')}
            className="back-button"
          >
            <span className="btn-icon">
              {state.language === 'ar' ? '→' : '←'}
            </span>
            {t('back')}
          </Button>
          
          <h1 className="page-title">{t('selectDisplay')}</h1>
          
          <button 
            className="refresh-button"
            onClick={loadDisplays}
            title={state.language === 'ar' ? 'تحديث' : 'Refresh'}
          >
            🔄
          </button>
        </div>

        {/* شبكة الشاشات */}
        <div className="displays-grid">
          {displays.map(display => {
            const timeRemaining = formatTimeRemaining(display.endTime);
            
            return (
              <div
                key={display.id}
                className={`display-card ${display.status} ${selectedDisplayId === display.id ? 'selected' : ''}`}
                onClick={() => handleDisplaySelect(display)}
                style={{ 
                  borderColor: getStatusColor(display.status),
                  cursor: display.status === 'available' ? 'pointer' : 'not-allowed'
                }}
              >
                {/* رقم الشاشة */}
                <div className="display-number">
                  <span className="number">{display.displayNumber}</span>
                  <div className="number-bg" style={{ backgroundColor: getStatusColor(display.status) }}></div>
                </div>

                {/* اسم الشاشة */}
                <div className="display-name">
                  {display.name}
                </div>

                {/* حالة الشاشة */}
                <div 
                  className="display-status"
                  style={{ backgroundColor: getStatusColor(display.status) }}
                >
                  <span className="status-text">{getStatusText(display.status)}</span>
                  {display.status === 'occupied' && timeRemaining && (
                    <span className="time-remaining">
                      ⏱️ {timeRemaining}
                    </span>
                  )}
                </div>

                {/* معلومات العميل */}
                {display.customerName && (
                  <div className="customer-info">
                    <span className="customer-label">
                      {state.language === 'ar' ? 'العميل:' : 'Customer:'}
                    </span>
                    <span className="customer-name">{display.customerName}</span>
                  </div>
                )}

                {/* زر الاختيار */}
                {display.status === 'available' && (
                  <div className="select-button">
                    <Button
                      variant="success"
                      size="small"
                      fullWidth
                      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                        e.stopPropagation();
                        handleDisplaySelect(display);
                      }}
                    >
                      <span className="btn-icon">✓</span>
                      {t('selectThisDisplay')}
                    </Button>
                  </div>
                )}

                {/* مؤشر الحالة */}
                <div className="status-indicator">
                  <div 
                    className={`indicator-dot ${display.status}`}
                    style={{ backgroundColor: getStatusColor(display.status) }}
                  ></div>
                </div>

                {/* تأثير الانتقاء */}
                {selectedDisplayId === display.id && (
                  <div className="selection-overlay">
                    <div className="selection-checkmark">✓</div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* معلومات إضافية */}
        <div className="page-info">
          <div className="info-card">
            <h3>{state.language === 'ar' ? 'معلومات الخدمة' : 'Service Information'}</h3>
            <div className="info-grid">
              <div className="info-item">
                <span className="info-icon">💰</span>
                <span className="info-text">
                  {t('displayPrice', { price: state.settings.displayPrice })}
                </span>
              </div>
              <div className="info-item">
                <span className="info-icon">⏰</span>
                <span className="info-text">
                  {t('displayDuration', { duration: Math.floor(state.settings.displayDuration / 60) })}
                </span>
              </div>
              <div className="info-item">
                <span className="info-icon">📱</span>
                <span className="info-text">
                  {state.language === 'ar' 
                    ? 'دفع آمن بالبطاقة اللاسلكية' 
                    : 'Secure NFC Payment'
                  }
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* مؤشر الحالات */}
        <div className="status-legend">
          <div className="legend-item">
            <div className="legend-dot" style={{ backgroundColor: getStatusColor('available') }}></div>
            <span>{t('available')}</span>
          </div>
          <div className="legend-item">
            <div className="legend-dot" style={{ backgroundColor: getStatusColor('occupied') }}></div>
            <span>{t('occupied')}</span>
          </div>
          <div className="legend-item">
            <div className="legend-dot" style={{ backgroundColor: getStatusColor('reserved') }}></div>
            <span>{t('reserved')}</span>
          </div>
          <div className="legend-item">
            <div className="legend-dot" style={{ backgroundColor: getStatusColor('maintenance') }}></div>
            <span>{t('maintenance')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DisplaySelectionPage;
