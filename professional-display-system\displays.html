<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار الشاشة | Display Selection</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <style>
        /* Import base styles from index.html */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary: #64748b;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            --font-ar: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-ar);
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
        }

        body.en {
            font-family: var(--font-en);
            direction: ltr;
        }

        /* Page Layout */
        .page {
            min-height: 100vh;
            background: var(--gradient-primary);
            padding: var(--space-8);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Header */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-12);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-6);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-xl);
            color: var(--white);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--white);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .refresh-btn {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-full);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--white);
            cursor: pointer;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(180deg);
        }

        /* Display Grid */
        .displays-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .display-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-2xl);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .display-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .display-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .display-card.available {
            border-color: var(--success);
            cursor: pointer;
        }

        .display-card.available::before {
            background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
        }

        .display-card.occupied {
            border-color: var(--error);
            opacity: 0.7;
        }

        .display-card.occupied::before {
            background: linear-gradient(135deg, var(--error) 0%, #f87171 100%);
        }

        .display-card.reserved {
            border-color: var(--warning);
            opacity: 0.8;
        }

        .display-card.reserved::before {
            background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
        }

        .display-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-6);
        }

        .display-number {
            width: 4rem;
            height: 4rem;
            border-radius: var(--radius-xl);
            background: var(--gradient-primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 800;
        }

        .display-status {
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-full);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-available {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-occupied {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .status-reserved {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .display-info {
            margin-bottom: var(--space-6);
        }

        .display-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: var(--space-2);
        }

        .display-details {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .customer-info {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin: var(--space-4) 0;
        }

        .customer-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--gray-500);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-1);
        }

        .customer-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .select-btn {
            width: 100%;
            padding: var(--space-4);
            background: var(--gradient-success);
            color: var(--white);
            border: none;
            border-radius: var(--radius-xl);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
        }

        .select-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .select-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Info Panel */
        .info-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--white);
        }

        .info-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
            text-align: center;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-6);
        }

        .info-item {
            text-align: center;
        }

        .info-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-full);
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-3);
            font-size: 1.25rem;
        }

        .info-label {
            font-size: 0.875rem;
            opacity: 0.8;
            margin-bottom: var(--space-1);
        }

        .info-value {
            font-size: 1.25rem;
            font-weight: 700;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page {
                padding: var(--space-4);
            }
            
            .page-header {
                flex-direction: column;
                gap: var(--space-4);
                text-align: center;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .displays-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-delay-1 { animation-delay: 0.1s; }
        .animate-delay-2 { animation-delay: 0.2s; }
        .animate-delay-3 { animation-delay: 0.3s; }
        .animate-delay-4 { animation-delay: 0.4s; }
        .animate-delay-5 { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="page">
        <div class="container">
            <!-- Page Header -->
            <header class="page-header animate-fade-in-up">
                <a href="index.html" class="back-btn">
                    <i class="fas fa-arrow-right"></i>
                    <span data-key="back">العودة</span>
                </a>
                
                <h1 class="page-title" data-key="select-display">اختيار الشاشة</h1>
                
                <button class="refresh-btn" id="refresh-displays" title="تحديث">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </header>

            <!-- Displays Grid -->
            <div class="displays-grid" id="displays-grid">
                <!-- Displays will be loaded here -->
            </div>

            <!-- Info Panel -->
            <div class="info-panel animate-fade-in-up animate-delay-5">
                <h2 class="info-title" data-key="service-info">معلومات الخدمة</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="info-label" data-key="price">السعر</div>
                        <div class="info-value">50 ريال</div>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-label" data-key="duration">المدة</div>
                        <div class="info-value">5 دقائق</div>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="info-label" data-key="payment">الدفع</div>
                        <div class="info-value" data-key="nfc-payment">NFC / بطاقة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Translation System
        const translations = {
            ar: {
                'back': 'العودة',
                'select-display': 'اختيار الشاشة',
                'service-info': 'معلومات الخدمة',
                'price': 'السعر',
                'duration': 'المدة',
                'payment': 'الدفع',
                'nfc-payment': 'NFC / بطاقة',
                'available': 'متاحة',
                'occupied': 'مشغولة',
                'reserved': 'محجوزة',
                'customer': 'العميل',
                'select-this-display': 'اختيار هذه الشاشة',
                'display': 'شاشة'
            },
            en: {
                'back': 'Back',
                'select-display': 'Select Display',
                'service-info': 'Service Information',
                'price': 'Price',
                'duration': 'Duration',
                'payment': 'Payment',
                'nfc-payment': 'NFC / Card',
                'available': 'Available',
                'occupied': 'Occupied',
                'reserved': 'Reserved',
                'customer': 'Customer',
                'select-this-display': 'Select This Display',
                'display': 'Display'
            }
        };

        // Mock Display Data
        const displays = [
            {
                id: 1,
                number: 1,
                name: 'شاشة العرض الرئيسية',
                nameEn: 'Main Display Screen',
                status: 'available',
                location: 'الطابق الأول - المدخل الرئيسي',
                locationEn: 'First Floor - Main Entrance'
            },
            {
                id: 2,
                number: 2,
                name: 'شاشة العرض الثانوية',
                nameEn: 'Secondary Display Screen',
                status: 'occupied',
                customerName: 'أحمد محمد العلي',
                customerNameEn: 'Ahmed Mohammed Ali',
                location: 'الطابق الأول - الصالة الرئيسية',
                locationEn: 'First Floor - Main Hall',
                timeRemaining: 180
            },
            {
                id: 3,
                number: 3,
                name: 'شاشة العرض التفاعلية',
                nameEn: 'Interactive Display Screen',
                status: 'available',
                location: 'الطابق الثاني - قاعة المؤتمرات',
                locationEn: 'Second Floor - Conference Hall'
            },
            {
                id: 4,
                number: 4,
                name: 'شاشة العرض المتقدمة',
                nameEn: 'Advanced Display Screen',
                status: 'reserved',
                customerName: 'فاطمة أحمد السالم',
                customerNameEn: 'Fatima Ahmed Al-Salem',
                location: 'الطابق الثاني - القاعة الذهبية',
                locationEn: 'Second Floor - Golden Hall'
            },
            {
                id: 5,
                number: 5,
                name: 'شاشة العرض الذكية',
                nameEn: 'Smart Display Screen',
                status: 'available',
                location: 'الطابق الثالث - المعرض',
                locationEn: 'Third Floor - Exhibition'
            }
        ];

        // Display Management
        class DisplayManager {
            constructor() {
                this.currentLang = 'ar';
                this.init();
            }

            init() {
                this.loadDisplays();
                this.setupEventListeners();
                this.updateLanguage();
            }

            setupEventListeners() {
                // Refresh button
                document.getElementById('refresh-displays')?.addEventListener('click', () => {
                    this.refreshDisplays();
                });

                // Language detection from URL or localStorage
                const urlParams = new URLSearchParams(window.location.search);
                const lang = urlParams.get('lang') || localStorage.getItem('language') || 'ar';
                this.setLanguage(lang);
            }

            setLanguage(lang) {
                this.currentLang = lang;
                document.body.className = lang === 'en' ? 'en' : '';
                document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
                document.documentElement.lang = lang;
                
                this.updateLanguage();
                this.loadDisplays(); // Reload displays with new language
            }

            updateLanguage() {
                document.querySelectorAll('[data-key]').forEach(element => {
                    const key = element.dataset.key;
                    if (translations[this.currentLang][key]) {
                        element.textContent = translations[this.currentLang][key];
                    }
                });
            }

            loadDisplays() {
                const grid = document.getElementById('displays-grid');
                if (!grid) return;

                grid.innerHTML = '';

                displays.forEach((display, index) => {
                    const card = this.createDisplayCard(display);
                    card.classList.add('animate-fade-in-up', `animate-delay-${Math.min(index + 1, 5)}`);
                    grid.appendChild(card);
                });
            }

            createDisplayCard(display) {
                const card = document.createElement('div');
                card.className = `display-card ${display.status}`;
                
                const displayName = this.currentLang === 'ar' ? display.name : display.nameEn;
                const location = this.currentLang === 'ar' ? display.location : display.locationEn;
                const statusText = translations[this.currentLang][display.status];

                card.innerHTML = `
                    <div class="display-header">
                        <div class="display-number">${display.number}</div>
                        <div class="display-status status-${display.status}">${statusText}</div>
                    </div>
                    
                    <div class="display-info">
                        <h3 class="display-name">${displayName}</h3>
                        <p class="display-details">${location}</p>
                    </div>
                    
                    ${display.customerName ? `
                        <div class="customer-info">
                            <div class="customer-label">${translations[this.currentLang]['customer']}</div>
                            <div class="customer-name">${this.currentLang === 'ar' ? display.customerName : display.customerNameEn}</div>
                        </div>
                    ` : ''}
                    
                    ${display.status === 'available' ? `
                        <button class="select-btn" onclick="displayManager.selectDisplay(${display.id})">
                            <i class="fas fa-check"></i>
                            <span>${translations[this.currentLang]['select-this-display']}</span>
                        </button>
                    ` : ''}
                `;

                if (display.status === 'available') {
                    card.addEventListener('click', () => this.selectDisplay(display.id));
                }

                return card;
            }

            selectDisplay(displayId) {
                const display = displays.find(d => d.id === displayId);
                if (!display || display.status !== 'available') return;

                console.log(`🎯 Display ${displayId} selected`);
                
                // Show professional confirmation
                const displayName = this.currentLang === 'ar' ? display.name : display.nameEn;
                const message = this.currentLang === 'ar' 
                    ? `تم اختيار ${displayName}\n\nسيتم الانتقال إلى صفحة تسجيل الدخول...`
                    : `Selected ${displayName}\n\nRedirecting to login page...`;
                
                alert(message);
                
                // Navigate to login page (will be created next)
                setTimeout(() => {
                    window.location.href = `login.html?display=${displayId}&lang=${this.currentLang}`;
                }, 1000);
            }

            refreshDisplays() {
                console.log('🔄 Refreshing displays...');
                
                // Add loading animation
                const refreshBtn = document.getElementById('refresh-displays');
                if (refreshBtn) {
                    refreshBtn.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        refreshBtn.style.transform = '';
                    }, 500);
                }

                // Simulate data refresh
                setTimeout(() => {
                    this.loadDisplays();
                }, 500);
            }
        }

        // Initialize
        let displayManager;
        document.addEventListener('DOMContentLoaded', () => {
            displayManager = new DisplayManager();
        });
    </script>
</body>
</html>
