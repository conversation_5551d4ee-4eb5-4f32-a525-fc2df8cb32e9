<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار الشاشة | Display Selection</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <style>
        /* Import base styles from index.html */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary: #64748b;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            --font-ar: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-ar);
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
        }

        body.en {
            font-family: var(--font-en);
            direction: ltr;
        }

        /* Page Layout */
        .page {
            min-height: 100vh;
            background: var(--gradient-primary);
            padding: var(--space-8);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Header */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-12);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-6);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-xl);
            color: var(--white);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--white);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .refresh-btn {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-full);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--white);
            cursor: pointer;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(180deg);
        }

        /* Display Grid */
        .displays-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }

        .display-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-2xl);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .display-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .display-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .display-card.available {
            border-color: var(--success);
            cursor: pointer;
        }

        .display-card.available::before {
            background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
        }

        .display-card.occupied {
            border-color: var(--warning);
            cursor: pointer;
            position: relative;
        }

        .display-card.occupied::before {
            background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
        }

        .display-card.occupied:hover {
            border-color: var(--primary);
            transform: translateY(-8px);
        }

        .display-card.occupied::after {
            content: '';
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 0.75rem;
            height: 0.75rem;
            background: var(--warning);
            border-radius: 50%;
            animation: pulse-warning 2s infinite;
        }

        @keyframes pulse-warning {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }

        .display-card.reserved {
            border-color: var(--warning);
            opacity: 0.8;
        }

        .display-card.reserved::before {
            background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
        }

        .display-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-6);
        }

        .display-number {
            width: 4rem;
            height: 4rem;
            border-radius: var(--radius-xl);
            background: var(--gradient-primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 800;
            position: relative;
        }

        .display-number::after {
            content: attr(data-display-name);
            position: absolute;
            bottom: -1.75rem;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--primary);
            white-space: nowrap;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            background: var(--white);
            color: var(--primary);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            box-shadow: var(--shadow-md);
            border: 2px solid var(--primary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .display-status {
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-full);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-available {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-occupied {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .status-expired {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .status-reserved {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .display-info {
            margin-bottom: var(--space-6);
        }

        .display-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: var(--space-2);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .display-name::before {
            content: '📺';
            font-size: 1.25rem;
        }

        .display-details {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .display-details::before {
            content: '📍';
            font-size: 1rem;
        }

        .display-id-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            background: var(--gray-100);
            color: var(--gray-700);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-left: auto;
        }

        .customer-info {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin: var(--space-4) 0;
        }

        .customer-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--gray-500);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-1);
        }

        .customer-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .time-info {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-3);
            margin: var(--space-3) 0;
            border-left: 4px solid var(--warning);
        }

        .time-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--gray-500);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-1);
        }

        .time-remaining {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--warning);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .time-expired {
            color: var(--error);
            border-left-color: var(--error);
        }

        .time-expired .time-remaining {
            color: var(--error);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .badge-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .badge-expired {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .extension-info {
            margin-top: var(--space-2);
            font-size: 0.75rem;
            color: var(--info);
            display: flex;
            align-items: center;
            gap: var(--space-1);
            background: rgba(59, 130, 246, 0.1);
            padding: var(--space-2);
            border-radius: var(--radius-md);
            border-left: 3px solid var(--info);
        }

        .extension-info i {
            font-size: 0.875rem;
        }

        .extension-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            background: var(--info);
            color: var(--white);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-full);
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }



        .content-display {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.1) 100%);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin: var(--space-4) 0;
            position: relative;
            overflow: hidden;
        }

        .content-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
        }

        .content-header {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-bottom: var(--space-2);
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--success);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .content-title {
            font-size: 0.875rem;
            font-weight: 700;
            color: var(--gray-800);
            line-height: 1.4;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .content-status {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            background: var(--success);
            color: var(--white);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-full);
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-left: auto;
        }

        .no-content {
            background: linear-gradient(135deg, rgba(148, 163, 184, 0.1) 0%, rgba(203, 213, 225, 0.1) 100%);
            border: 1px solid rgba(148, 163, 184, 0.2);
            color: var(--gray-500);
        }

        .no-content::before {
            background: linear-gradient(135deg, var(--gray-400) 0%, var(--gray-500) 100%);
        }

        .no-content .content-header {
            color: var(--gray-500);
        }

        .no-content .content-status {
            background: var(--gray-400);
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: all var(--transition-normal);
            position: relative;
        }

        .modal-overlay.active .modal {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: var(--space-6);
            text-align: center;
            position: relative;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .modal-icon {
            width: 4rem;
            height: 4rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            font-size: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
            position: relative;
            z-index: 1;
        }

        .modal-subtitle {
            opacity: 0.9;
            font-size: 0.875rem;
            position: relative;
            z-index: 1;
        }

        .modal-body {
            padding: var(--space-8);
        }

        .modal-content {
            text-align: center;
            margin-bottom: var(--space-6);
        }

        .modal-message {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-700);
            margin-bottom: var(--space-6);
        }

        .modal-details {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin: var(--space-4) 0;
            border-left: 4px solid var(--primary);
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-2) 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .detail-value {
            font-weight: 700;
            color: var(--gray-800);
            font-size: 0.875rem;
        }

        .modal-actions {
            display: flex;
            gap: var(--space-3);
            justify-content: center;
        }

        .modal-btn {
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            min-width: 120px;
            justify-content: center;
        }

        .modal-btn-primary {
            background: var(--gradient-primary);
            color: var(--white);
            box-shadow: var(--shadow-lg);
        }

        .modal-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .modal-btn-secondary {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 2px solid var(--gray-200);
        }

        .modal-btn-secondary:hover {
            background: var(--gray-200);
        }

        .modal-btn-success {
            background: var(--gradient-success);
            color: var(--white);
            box-shadow: var(--shadow-lg);
        }

        .modal-btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .modal-btn-warning {
            background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
            color: var(--white);
            box-shadow: var(--shadow-lg);
        }

        .modal-btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .close-btn {
            position: absolute;
            top: var(--space-4);
            right: var(--space-4);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: var(--radius-full);
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            cursor: pointer;
            transition: var(--transition-fast);
            z-index: 2;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .modal {
                width: 95%;
                margin: var(--space-4);
            }

            .modal-actions {
                flex-direction: column;
            }

            .modal-btn {
                width: 100%;
            }
        }



        .select-btn {
            width: 100%;
            padding: var(--space-4);
            background: var(--gradient-success);
            color: var(--white);
            border: none;
            border-radius: var(--radius-xl);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
        }

        .select-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .select-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .reuse-btn {
            width: 100%;
            padding: var(--space-4);
            background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
            color: var(--white);
            border: none;
            border-radius: var(--radius-xl);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .reuse-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .extend-btn {
            flex: 1;
            padding: var(--space-3);
            background: linear-gradient(135deg, var(--info) 0%, #60a5fa 100%);
            color: var(--white);
            border: none;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
        }

        .extend-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .new-session-btn {
            flex: 1;
            padding: var(--space-3);
            background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
            color: var(--white);
            border: none;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
        }

        .new-session-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        }

        .action-buttons {
            display: flex;
            gap: var(--space-3);
            margin-top: var(--space-3);
        }

        .action-buttons.single {
            flex-direction: column;
        }

        .action-buttons.single .extend-btn,
        .action-buttons.single .new-session-btn,
        .action-buttons.single .reuse-btn {
            width: 100%;
        }

        /* Info Panel */
        .info-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            color: var(--white);
        }

        .info-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--space-6);
            text-align: center;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-6);
        }

        .info-item {
            text-align: center;
        }

        .info-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-full);
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-3);
            font-size: 1.25rem;
        }

        .info-label {
            font-size: 0.875rem;
            opacity: 0.8;
            margin-bottom: var(--space-1);
        }

        .info-value {
            font-size: 1.25rem;
            font-weight: 700;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page {
                padding: var(--space-4);
            }
            
            .page-header {
                flex-direction: column;
                gap: var(--space-4);
                text-align: center;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .displays-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-delay-1 { animation-delay: 0.1s; }
        .animate-delay-2 { animation-delay: 0.2s; }
        .animate-delay-3 { animation-delay: 0.3s; }
        .animate-delay-4 { animation-delay: 0.4s; }
        .animate-delay-5 { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="page">
        <div class="container">
            <!-- Page Header -->
            <header class="page-header animate-fade-in-up">
                <a href="index.html" class="back-btn">
                    <i class="fas fa-arrow-right"></i>
                    <span data-key="back">العودة</span>
                </a>
                
                <h1 class="page-title" data-key="select-display">اختيار الشاشة</h1>
                
                <button class="refresh-btn" id="refresh-displays" title="تحديث">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </header>

            <!-- Displays Grid -->
            <div class="displays-grid" id="displays-grid">
                <!-- Displays will be loaded here -->
            </div>

            <!-- Info Panel -->
            <div class="info-panel animate-fade-in-up animate-delay-5">
                <h2 class="info-title" data-key="service-info">معلومات الخدمة</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="info-label" data-key="price">السعر</div>
                        <div class="info-value">50 ريال</div>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-label" data-key="duration">المدة</div>
                        <div class="info-value">5 دقائق</div>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="info-label" data-key="payment">الدفع</div>
                        <div class="info-value" data-key="nfc-payment">NFC / بطاقة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal-overlay" id="confirmModal">
        <div class="modal">
            <div class="modal-header">
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-icon" id="modalIcon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h2 class="modal-title" id="modalTitle">تأكيد العملية</h2>
                <p class="modal-subtitle" id="modalSubtitle">يرجى مراجعة التفاصيل والتأكيد</p>
            </div>
            <div class="modal-body">
                <div class="modal-content">
                    <p class="modal-message" id="modalMessage">
                        هل أنت متأكد من أنك تريد المتابعة؟
                    </p>
                    <div class="modal-details" id="modalDetails">
                        <!-- التفاصيل ستضاف هنا ديناميكياً -->
                    </div>
                </div>
                <div class="modal-actions" id="modalActions">
                    <button class="modal-btn modal-btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                        <span data-key="cancel">إلغاء</span>
                    </button>
                    <button class="modal-btn modal-btn-primary" id="confirmBtn">
                        <i class="fas fa-check"></i>
                        <span data-key="confirm">تأكيد</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal-overlay" id="confirmModal">
        <div class="modal">
            <div class="modal-header">
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-icon" id="modalIcon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h2 class="modal-title" id="modalTitle">تأكيد العملية</h2>
                <p class="modal-subtitle" id="modalSubtitle">يرجى مراجعة التفاصيل والتأكيد</p>
            </div>
            <div class="modal-body">
                <div class="modal-content">
                    <p class="modal-message" id="modalMessage">
                        هل أنت متأكد من أنك تريد المتابعة؟
                    </p>
                    <div class="modal-details" id="modalDetails">
                        <!-- التفاصيل ستضاف هنا ديناميكياً -->
                    </div>
                </div>
                <div class="modal-actions" id="modalActions">
                    <button class="modal-btn modal-btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                        <span data-key="cancel">إلغاء</span>
                    </button>
                    <button class="modal-btn modal-btn-primary" id="confirmBtn">
                        <i class="fas fa-check"></i>
                        <span data-key="confirm">تأكيد</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Translation System
        const translations = {
            ar: {
                'back': 'العودة',
                'select-display': 'اختيار الشاشة',
                'service-info': 'معلومات الخدمة',
                'price': 'السعر',
                'duration': 'المدة',
                'payment': 'الدفع',
                'nfc-payment': 'NFC / بطاقة',
                'available': 'متاحة',
                'occupied': 'مشغولة',
                'reserved': 'محجوزة',
                'customer': 'العميل',
                'select-this-display': 'اختيار هذه الشاشة',
                'display': 'شاشة',
                'time-remaining': 'الوقت المتبقي',
                'time-expired': 'انتهت المدة',
                'reuse-display': 'إعادة استخدام الشاشة',
                'extend-time': 'تمديد الوقت',
                'new-session': 'معاملة جديدة',
                'expired': 'منتهية الصلاحية',
                'in-use': 'قيد الاستخدام',
                'minutes': 'دقيقة',
                'seconds': 'ثانية',
                'current-content': 'المحتوى المعروض',
                'no-content': 'لا يوجد محتوى',
                'content-type': 'نوع المحتوى',
                'confirm': 'تأكيد',
                'cancel': 'إلغاء',
                'confirm-operation': 'تأكيد العملية',
                'review-details': 'يرجى مراجعة التفاصيل والتأكيد',
                'display-name': 'اسم الشاشة',
                'current-user': 'المستخدم الحالي',
                'cost': 'التكلفة',
                'duration': 'المدة',
                'extension-number': 'رقم التمديد',
                'total-cost': 'التكلفة الإجمالية',
                'total-time': 'الوقت الإجمالي'
            },
            en: {
                'back': 'Back',
                'select-display': 'Select Display',
                'service-info': 'Service Information',
                'price': 'Price',
                'duration': 'Duration',
                'payment': 'Payment',
                'nfc-payment': 'NFC / Card',
                'available': 'Available',
                'occupied': 'Occupied',
                'reserved': 'Reserved',
                'customer': 'Customer',
                'select-this-display': 'Select This Display',
                'display': 'Display',
                'time-remaining': 'Time Remaining',
                'time-expired': 'Time Expired',
                'reuse-display': 'Reuse Display',
                'extend-time': 'Extend Time',
                'new-session': 'New Session',
                'expired': 'Expired',
                'in-use': 'In Use',
                'minutes': 'minutes',
                'seconds': 'seconds',
                'current-content': 'Current Content',
                'no-content': 'No Content',
                'content-type': 'Content Type',
                'confirm': 'Confirm',
                'cancel': 'Cancel',
                'confirm-operation': 'Confirm Operation',
                'review-details': 'Please review the details and confirm',
                'display-name': 'Display Name',
                'current-user': 'Current User',
                'cost': 'Cost',
                'duration': 'Duration',
                'extension-number': 'Extension Number',
                'total-cost': 'Total Cost',
                'total-time': 'Total Time'
            }
        };

        // Mock Display Data
        let displays = [
            {
                id: 1,
                number: 1,
                name: 'شاشة العرض الرئيسية',
                nameEn: 'Main Display Screen',
                status: 'available',
                location: 'الطابق الأول - المدخل الرئيسي',
                locationEn: 'First Floor - Main Entrance',
                currentContent: null, // لا يوجد محتوى معروض
                currentContentEn: null
            },
            {
                id: 2,
                number: 2,
                name: 'شاشة العرض الثانوية',
                nameEn: 'Secondary Display Screen',
                status: 'occupied',
                customerName: 'أحمد محمد العلي',
                customerNameEn: 'Ahmed Mohammed Ali',
                location: 'الطابق الأول - الصالة الرئيسية',
                locationEn: 'First Floor - Main Hall',
                startTime: new Date(Date.now() - 2 * 60 * 1000), // بدأت منذ دقيقتين
                endTime: new Date(Date.now() + 3 * 60 * 1000), // تنتهي بعد 3 دقائق
                totalDuration: 5 * 60 * 1000, // 5 دقائق إجمالي
                canExtend: true,
                canReuse: false,
                allowNewSession: true, // السماح بمعاملة جديدة
                extensionCount: 0, // عدد التمديدات
                originalDuration: 5 * 60 * 1000, // المدة الأصلية
                currentContent: 'عرض تقديمي - خطة التطوير 2024',
                currentContentEn: 'Presentation - Development Plan 2024'
            },
            {
                id: 3,
                number: 3,
                name: 'شاشة العرض التفاعلية',
                nameEn: 'Interactive Display Screen',
                status: 'available',
                location: 'الطابق الثاني - قاعة المؤتمرات',
                locationEn: 'Second Floor - Conference Hall',
                currentContent: null,
                currentContentEn: null
            },
            {
                id: 4,
                number: 4,
                name: 'شاشة العرض المتقدمة',
                nameEn: 'Advanced Display Screen',
                status: 'occupied',
                customerName: 'فاطمة أحمد السالم',
                customerNameEn: 'Fatima Ahmed Al-Salem',
                location: 'الطابق الثاني - القاعة الذهبية',
                locationEn: 'Second Floor - Golden Hall',
                startTime: new Date(Date.now() - 6 * 60 * 1000), // بدأت منذ 6 دقائق
                endTime: new Date(Date.now() - 1 * 60 * 1000), // انتهت منذ دقيقة
                totalDuration: 5 * 60 * 1000,
                canExtend: false,
                canReuse: true,
                currentContent: 'فيديو تعليمي - أساسيات البرمجة',
                currentContentEn: 'Educational Video - Programming Basics'
            },
            {
                id: 5,
                number: 5,
                name: 'شاشة العرض الذكية',
                nameEn: 'Smart Display Screen',
                status: 'available',
                location: 'الطابق الثالث - المعرض',
                locationEn: 'Third Floor - Exhibition',
                currentContent: null,
                currentContentEn: null
            },
            {
                id: 6,
                number: 6,
                name: 'شاشة العرض المحمولة',
                nameEn: 'Portable Display Screen',
                status: 'occupied',
                customerName: 'سارة خالد المطيري',
                customerNameEn: 'Sarah Khalid Al-Mutairi',
                location: 'الطابق الثالث - القاعة الفضية',
                locationEn: 'Third Floor - Silver Hall',
                startTime: new Date(Date.now() - 1 * 60 * 1000), // بدأت منذ دقيقة
                endTime: new Date(Date.now() + 4 * 60 * 1000), // تنتهي بعد 4 دقائق
                totalDuration: 5 * 60 * 1000,
                canExtend: true,
                canReuse: false,
                allowNewSession: true,
                extensionCount: 0,
                originalDuration: 5 * 60 * 1000,
                currentContent: 'عرض مباشر - مؤتمر التقنية 2024',
                currentContentEn: 'Live Stream - Tech Conference 2024'
            }
        ];

        // Modal Management
        class ModalManager {
            constructor() {
                this.modal = document.getElementById('confirmModal');
                this.currentCallback = null;
            }

            show(options) {
                const {
                    title,
                    subtitle,
                    message,
                    details,
                    icon,
                    confirmText,
                    confirmClass,
                    onConfirm
                } = options;

                // تحديث المحتوى
                document.getElementById('modalTitle').textContent = title;
                document.getElementById('modalSubtitle').textContent = subtitle;
                document.getElementById('modalMessage').textContent = message;

                // تحديث الأيقونة
                const modalIcon = document.getElementById('modalIcon');
                modalIcon.innerHTML = `<i class="fas fa-${icon || 'question-circle'}"></i>`;

                // تحديث التفاصيل
                const detailsContainer = document.getElementById('modalDetails');
                if (details && details.length > 0) {
                    detailsContainer.innerHTML = details.map(detail => `
                        <div class="detail-item">
                            <span class="detail-label">${detail.label}</span>
                            <span class="detail-value">${detail.value}</span>
                        </div>
                    `).join('');
                    detailsContainer.style.display = 'block';
                } else {
                    detailsContainer.style.display = 'none';
                }

                // تحديث زر التأكيد
                const confirmBtn = document.getElementById('confirmBtn');
                confirmBtn.className = `modal-btn ${confirmClass || 'modal-btn-primary'}`;
                confirmBtn.querySelector('span').textContent = confirmText || translations[displayManager.currentLang]['confirm'];

                // حفظ callback
                this.currentCallback = onConfirm;

                // إظهار النافذة
                this.modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }

            hide() {
                this.modal.classList.remove('active');
                document.body.style.overflow = '';
                this.currentCallback = null;
            }

            confirm() {
                if (this.currentCallback) {
                    this.currentCallback();
                }
                this.hide();
            }
        }

        // Display Management
        class DisplayManager {
            constructor() {
                this.currentLang = 'ar';
                this.modalManager = new ModalManager();
                this.init();
            }

            init() {
                this.loadDisplays();
                this.setupEventListeners();
                this.updateLanguage();
            }

            setupEventListeners() {
                // Refresh button
                document.getElementById('refresh-displays')?.addEventListener('click', () => {
                    this.refreshDisplays();
                });

                // Language detection from URL or localStorage
                const urlParams = new URLSearchParams(window.location.search);
                const lang = urlParams.get('lang') || localStorage.getItem('language') || 'ar';
                this.setLanguage(lang);
            }

            setLanguage(lang) {
                this.currentLang = lang;
                document.body.className = lang === 'en' ? 'en' : '';
                document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
                document.documentElement.lang = lang;
                
                this.updateLanguage();
                this.loadDisplays(); // Reload displays with new language
            }

            updateLanguage() {
                document.querySelectorAll('[data-key]').forEach(element => {
                    const key = element.dataset.key;
                    if (translations[this.currentLang][key]) {
                        element.textContent = translations[this.currentLang][key];
                    }
                });
            }

            loadDisplays() {
                const grid = document.getElementById('displays-grid');
                if (!grid) return;

                grid.innerHTML = '';

                displays.forEach((display, index) => {
                    const card = this.createDisplayCard(display);
                    card.classList.add('animate-fade-in-up', `animate-delay-${Math.min(index + 1, 5)}`);
                    grid.appendChild(card);
                });
            }

            createDisplayCard(display) {
                const card = document.createElement('div');

                // تحديث حالة الشاشة بناءً على الوقت
                const updatedDisplay = this.updateDisplayStatus(display);
                card.className = `display-card ${updatedDisplay.status}`;

                const displayName = this.currentLang === 'ar' ? updatedDisplay.name : updatedDisplay.nameEn;
                const location = this.currentLang === 'ar' ? updatedDisplay.location : updatedDisplay.locationEn;
                const statusText = this.getStatusText(updatedDisplay);

                let cardContent = `
                    <div class="display-header">
                        <div class="display-number" data-display-name="${this.currentLang === 'ar' ? 'شاشة' : 'Display'} ${updatedDisplay.number}">${updatedDisplay.number}</div>
                        <div class="display-status status-${updatedDisplay.status}">${statusText}</div>
                    </div>

                    <div class="display-info">
                        <h3 class="display-name">
                            ${displayName}
                            <span class="display-id-badge">
                                <i class="fas fa-hashtag"></i>
                                ${updatedDisplay.id}
                            </span>
                        </h3>
                        <p class="display-details">${location}</p>
                    </div>
                `;



                // عرض المحتوى المعروض
                if (updatedDisplay.status === 'occupied' || updatedDisplay.currentContent) {
                    const contentText = updatedDisplay.currentContent
                        ? (this.currentLang === 'ar' ? updatedDisplay.currentContent : updatedDisplay.currentContentEn)
                        : translations[this.currentLang]['no-content'];

                    const hasContent = !!updatedDisplay.currentContent;

                    cardContent += `
                        <div class="content-display ${hasContent ? '' : 'no-content'}">
                            <div class="content-header">
                                <i class="fas fa-${hasContent ? 'play-circle' : 'pause-circle'}"></i>
                                <span>${translations[this.currentLang]['current-content']}</span>
                                <div class="content-status">
                                    <i class="fas fa-${hasContent ? 'broadcast-tower' : 'stop-circle'}"></i>
                                    <span>${hasContent ? (this.currentLang === 'ar' ? 'مباشر' : 'LIVE') : (this.currentLang === 'ar' ? 'متوقف' : 'IDLE')}</span>
                                </div>
                            </div>
                            <div class="content-title">
                                <i class="fas fa-${hasContent ? 'video' : 'tv'}" style="color: ${hasContent ? 'var(--success)' : 'var(--gray-400)'}"></i>
                                ${contentText}
                            </div>
                        </div>
                    `;
                }

                // معلومات العميل
                if (updatedDisplay.customerName) {
                    cardContent += `
                        <div class="customer-info">
                            <div class="customer-label">${translations[this.currentLang]['customer']}</div>
                            <div class="customer-name">${this.currentLang === 'ar' ? updatedDisplay.customerName : updatedDisplay.customerNameEn}</div>
                        </div>
                    `;
                }

                // معلومات الوقت للشاشات المشغولة
                if (updatedDisplay.status === 'occupied' && updatedDisplay.endTime) {
                    const timeInfo = this.getTimeInfo(updatedDisplay);
                    const extensionCount = updatedDisplay.extensionCount || 0;
                    const totalMinutes = Math.round((updatedDisplay.totalDuration || updatedDisplay.originalDuration) / (60 * 1000));

                    cardContent += `
                        <div class="time-info ${timeInfo.expired ? 'time-expired' : ''}">
                            <div class="time-label">${timeInfo.expired ? translations[this.currentLang]['time-expired'] : translations[this.currentLang]['time-remaining']}</div>
                            <div class="time-remaining">
                                <i class="fas fa-clock"></i>
                                <span id="timer-${updatedDisplay.id}">${timeInfo.display}</span>
                            </div>
                            ${extensionCount > 0 ? `
                                <div class="extension-info">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>${extensionCount} ${this.currentLang === 'ar' ? 'تمديد' : 'extensions'}</span>
                                    <span>•</span>
                                    <span>${totalMinutes} ${this.currentLang === 'ar' ? 'دقيقة إجمالي' : 'min total'}</span>
                                    <div class="extension-badge">
                                        <i class="fas fa-fire"></i>
                                        <span>${this.currentLang === 'ar' ? 'ممدد' : 'EXTENDED'}</span>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `;
                }

                // أزرار التفاعل
                if (updatedDisplay.status === 'available') {
                    cardContent += `
                        <div class="action-buttons single">
                            <button class="select-btn" onclick="displayManager.selectDisplay(${updatedDisplay.id})">
                                <i class="fas fa-check"></i>
                                <span>${translations[this.currentLang]['select-this-display']}</span>
                            </button>
                        </div>
                    `;
                } else if (updatedDisplay.status === 'occupied') {
                    if (updatedDisplay.canReuse) {
                        cardContent += `
                            <div class="action-buttons single">
                                <button class="reuse-btn" onclick="displayManager.reuseDisplay(${updatedDisplay.id})">
                                    <i class="fas fa-recycle"></i>
                                    <span>${translations[this.currentLang]['reuse-display']}</span>
                                </button>
                            </div>
                        `;
                    } else if (updatedDisplay.canExtend) {
                        const extensionCount = updatedDisplay.extensionCount || 0;
                        const extendButtonText = extensionCount > 0
                            ? `${translations[this.currentLang]['extend-time']} (+${extensionCount + 1})`
                            : translations[this.currentLang]['extend-time'];

                        cardContent += `
                            <div class="action-buttons">
                                <button class="extend-btn" onclick="displayManager.extendTime(${updatedDisplay.id})">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>${extendButtonText}</span>
                                </button>
                                <button class="new-session-btn" onclick="displayManager.newSession(${updatedDisplay.id})">
                                    <i class="fas fa-plus"></i>
                                    <span>${translations[this.currentLang]['new-session']}</span>
                                </button>
                            </div>
                        `;
                    }
                }

                card.innerHTML = cardContent;

                // إضافة أحداث النقر
                if (updatedDisplay.status === 'available') {
                    card.addEventListener('click', () => this.selectDisplay(updatedDisplay.id));
                } else if (updatedDisplay.status === 'occupied' && updatedDisplay.canReuse) {
                    card.addEventListener('click', () => this.reuseDisplay(updatedDisplay.id));
                }

                return card;
            }

            updateDisplayStatus(display) {
                if (display.status === 'occupied' && display.endTime) {
                    const now = new Date();
                    const endTime = new Date(display.endTime);

                    if (now > endTime) {
                        // انتهت المدة - يمكن إعادة الاستخدام
                        display.canReuse = true;
                        display.canExtend = false;
                    }
                }
                return display;
            }

            getStatusText(display) {
                if (display.status === 'occupied') {
                    const now = new Date();
                    const endTime = new Date(display.endTime);

                    if (now > endTime) {
                        return translations[this.currentLang]['expired'];
                    } else {
                        return translations[this.currentLang]['in-use'];
                    }
                }
                return translations[this.currentLang][display.status];
            }



            getTimeInfo(display) {
                const now = new Date();
                const endTime = new Date(display.endTime);
                const diff = endTime.getTime() - now.getTime();

                if (diff <= 0) {
                    const expiredTime = Math.abs(diff);
                    const expiredMinutes = Math.floor(expiredTime / (1000 * 60));
                    const expiredSeconds = Math.floor((expiredTime % (1000 * 60)) / 1000);

                    return {
                        expired: true,
                        display: `${expiredMinutes}:${expiredSeconds.toString().padStart(2, '0')} ${translations[this.currentLang]['minutes']}`
                    };
                } else {
                    const minutes = Math.floor(diff / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

                    return {
                        expired: false,
                        display: `${minutes}:${seconds.toString().padStart(2, '0')}`
                    };
                }
            }

            selectDisplay(displayId) {
                const display = displays.find(d => d.id === displayId);
                if (!display || display.status !== 'available') return;

                console.log(`🎯 Display ${displayId} selected`);

                const displayName = this.currentLang === 'ar' ? display.name : display.nameEn;
                const location = this.currentLang === 'ar' ? display.location : display.locationEn;

                this.modalManager.show({
                    title: this.currentLang === 'ar' ? 'تأكيد اختيار الشاشة' : 'Confirm Display Selection',
                    subtitle: this.currentLang === 'ar' ? 'بدء جلسة جديدة' : 'Start new session',
                    message: this.currentLang === 'ar'
                        ? `هل تريد بدء جلسة جديدة على هذه الشاشة؟`
                        : `Do you want to start a new session on this display?`,
                    icon: 'tv',
                    confirmText: this.currentLang === 'ar' ? 'اختيار الشاشة' : 'Select Display',
                    confirmClass: 'modal-btn-primary',
                    details: [
                        {
                            label: translations[this.currentLang]['display-name'],
                            value: displayName
                        },
                        {
                            label: this.currentLang === 'ar' ? 'الموقع' : 'Location',
                            value: location
                        },
                        {
                            label: translations[this.currentLang]['duration'],
                            value: this.currentLang === 'ar' ? '5 دقائق' : '5 minutes'
                        },
                        {
                            label: translations[this.currentLang]['cost'],
                            value: '50 ريال'
                        },
                        {
                            label: this.currentLang === 'ar' ? 'الخطوة التالية' : 'Next Step',
                            value: this.currentLang === 'ar' ? 'تسجيل الدخول' : 'User Login'
                        }
                    ],
                    onConfirm: () => {
                        // الانتقال لصفحة تسجيل الدخول
                        window.location.href = `login.html?display=${displayId}&lang=${this.currentLang}`;
                    }
                });
            }

            reuseDisplay(displayId) {
                const display = displays.find(d => d.id === displayId);
                if (!display || !display.canReuse) return;

                console.log(`🔄 Display ${displayId} reuse requested`);

                const displayName = this.currentLang === 'ar' ? display.name : display.nameEn;
                const previousUser = this.currentLang === 'ar' ? display.customerName : display.customerNameEn;
                const currentContent = display.currentContent
                    ? (this.currentLang === 'ar' ? display.currentContent : display.currentContentEn)
                    : (this.currentLang === 'ar' ? 'لا يوجد محتوى' : 'No content');

                this.modalManager.show({
                    title: this.currentLang === 'ar' ? 'إعادة استخدام الشاشة' : 'Reuse Display',
                    subtitle: this.currentLang === 'ar' ? 'الشاشة متاحة للاستخدام الفوري' : 'Display available for immediate use',
                    message: this.currentLang === 'ar'
                        ? `هذه الشاشة انتهت مدتها وأصبحت متاحة للاستخدام الفوري. هل تريد بدء جلسة جديدة؟`
                        : `This display session has expired and is now available for immediate use. Do you want to start a new session?`,
                    icon: 'recycle',
                    confirmText: this.currentLang === 'ar' ? 'إعادة الاستخدام' : 'Reuse Display',
                    confirmClass: 'modal-btn-warning',
                    details: [
                        {
                            label: translations[this.currentLang]['display-name'],
                            value: displayName
                        },
                        {
                            label: this.currentLang === 'ar' ? 'المستخدم السابق' : 'Previous User',
                            value: previousUser
                        },
                        {
                            label: translations[this.currentLang]['current-content'],
                            value: currentContent
                        },
                        {
                            label: translations[this.currentLang]['duration'],
                            value: this.currentLang === 'ar' ? '5 دقائق' : '5 minutes'
                        },
                        {
                            label: translations[this.currentLang]['cost'],
                            value: '50 ريال'
                        }
                    ],
                    onConfirm: () => {
                        // الانتقال لصفحة تسجيل الدخول مع علامة إعادة الاستخدام
                        window.location.href = `login.html?display=${displayId}&reuse=true&lang=${this.currentLang}`;
                    }
                });
            }

            extendTime(displayId) {
                const display = displays.find(d => d.id === displayId);
                if (!display || !display.canExtend) return;

                console.log(`⏰ Display ${displayId} time extension requested`);

                // تحديث عداد التمديدات
                const extensionCount = display.extensionCount || 0;
                const newExtensionCount = extensionCount + 1;
                const totalCost = 25 * newExtensionCount;
                const displayName = this.currentLang === 'ar' ? display.name : display.nameEn;
                const totalMinutes = Math.round((display.totalDuration + 5 * 60 * 1000) / (60 * 1000));

                this.modalManager.show({
                    title: this.currentLang === 'ar' ? 'تمديد وقت الشاشة' : 'Extend Display Time',
                    subtitle: this.currentLang === 'ar' ? 'تأكيد إضافة وقت إضافي' : 'Confirm adding extra time',
                    message: this.currentLang === 'ar'
                        ? `هل تريد تمديد وقت الشاشة لمدة 5 دقائق إضافية؟`
                        : `Do you want to extend the display time for 5 more minutes?`,
                    icon: 'clock',
                    confirmText: this.currentLang === 'ar' ? 'تمديد الوقت' : 'Extend Time',
                    confirmClass: 'modal-btn-warning',
                    details: [
                        {
                            label: translations[this.currentLang]['display-name'],
                            value: displayName
                        },
                        {
                            label: translations[this.currentLang]['extension-number'],
                            value: `#${newExtensionCount}`
                        },
                        {
                            label: translations[this.currentLang]['duration'],
                            value: this.currentLang === 'ar' ? '5 دقائق' : '5 minutes'
                        },
                        {
                            label: translations[this.currentLang]['cost'],
                            value: '25 ريال'
                        },
                        {
                            label: translations[this.currentLang]['total-cost'],
                            value: `${totalCost} ريال`
                        },
                        {
                            label: translations[this.currentLang]['total-time'],
                            value: this.currentLang === 'ar' ? `${totalMinutes} دقيقة` : `${totalMinutes} minutes`
                        }
                    ],
                    onConfirm: () => {
                        // تمديد الوقت
                        display.endTime = new Date(display.endTime.getTime() + 5 * 60 * 1000);
                        display.extensionCount = newExtensionCount;
                        display.totalDuration += 5 * 60 * 1000;

                        // إبقاء الأزرار متاحة
                        display.canExtend = true;
                        display.allowNewSession = true;

                        // إعادة تحميل الشاشات
                        this.loadDisplays();

                        // إظهار رسالة نجاح
                        setTimeout(() => {
                            this.modalManager.show({
                                title: this.currentLang === 'ar' ? 'تم التمديد بنجاح' : 'Extension Successful',
                                subtitle: this.currentLang === 'ar' ? 'تم إضافة الوقت الإضافي' : 'Extra time has been added',
                                message: this.currentLang === 'ar'
                                    ? `تم تمديد وقت الشاشة بنجاح! يمكنك التمديد مرة أخرى أو بدء معاملة جديدة.`
                                    : `Display time extended successfully! You can extend again or start a new session.`,
                                icon: 'check-circle',
                                confirmText: this.currentLang === 'ar' ? 'حسناً' : 'OK',
                                confirmClass: 'modal-btn-success',
                                details: [
                                    {
                                        label: translations[this.currentLang]['extension-number'],
                                        value: `${newExtensionCount}`
                                    },
                                    {
                                        label: translations[this.currentLang]['total-time'],
                                        value: this.currentLang === 'ar' ? `${totalMinutes} دقيقة` : `${totalMinutes} minutes`
                                    }
                                ],
                                onConfirm: () => {}
                            });
                        }, 500);
                    }
                });
            }

            newSession(displayId) {
                const display = displays.find(d => d.id === displayId);
                if (!display) return;

                console.log(`🆕 Display ${displayId} new session requested`);

                const displayName = this.currentLang === 'ar' ? display.name : display.nameEn;
                const currentUser = this.currentLang === 'ar' ? display.customerName : display.customerNameEn;
                const currentContent = display.currentContent
                    ? (this.currentLang === 'ar' ? display.currentContent : display.currentContentEn)
                    : (this.currentLang === 'ar' ? 'لا يوجد محتوى' : 'No content');

                this.modalManager.show({
                    title: this.currentLang === 'ar' ? 'معاملة جديدة' : 'New Session',
                    subtitle: this.currentLang === 'ar' ? 'بدء جلسة جديدة على شاشة مشغولة' : 'Start new session on occupied display',
                    message: this.currentLang === 'ar'
                        ? `هل تريد بدء معاملة جديدة على هذه الشاشة؟ سيتم إنهاء الجلسة الحالية وبدء جلسة جديدة.`
                        : `Do you want to start a new session on this display? The current session will be ended and a new session will begin.`,
                    icon: 'plus-circle',
                    confirmText: this.currentLang === 'ar' ? 'بدء المعاملة' : 'Start Session',
                    confirmClass: 'modal-btn-success',
                    details: [
                        {
                            label: translations[this.currentLang]['display-name'],
                            value: displayName
                        },
                        {
                            label: translations[this.currentLang]['current-user'],
                            value: currentUser
                        },
                        {
                            label: translations[this.currentLang]['current-content'],
                            value: currentContent
                        },
                        {
                            label: translations[this.currentLang]['duration'],
                            value: this.currentLang === 'ar' ? '5 دقائق' : '5 minutes'
                        },
                        {
                            label: translations[this.currentLang]['cost'],
                            value: '50 ريال'
                        }
                    ],
                    onConfirm: () => {
                        // إظهار رسالة تأكيد
                        this.modalManager.show({
                            title: this.currentLang === 'ar' ? 'تم تأكيد المعاملة' : 'Session Confirmed',
                            subtitle: this.currentLang === 'ar' ? 'جاري الانتقال لصفحة تسجيل الدخول' : 'Redirecting to login page',
                            message: this.currentLang === 'ar'
                                ? `تم تأكيد المعاملة الجديدة بنجاح! سيتم الانتقال إلى صفحة تسجيل الدخول لبدء الجلسة الجديدة.`
                                : `New session confirmed successfully! Redirecting to login page to start the new session.`,
                            icon: 'check-circle',
                            confirmText: this.currentLang === 'ar' ? 'متابعة' : 'Continue',
                            confirmClass: 'modal-btn-success',
                            details: [
                                {
                                    label: translations[this.currentLang]['display-name'],
                                    value: displayName
                                },
                                {
                                    label: this.currentLang === 'ar' ? 'الخطوة التالية' : 'Next Step',
                                    value: this.currentLang === 'ar' ? 'تسجيل الدخول' : 'User Login'
                                }
                            ],
                            onConfirm: () => {
                                // الانتقال لصفحة تسجيل الدخول
                                window.location.href = `login.html?display=${displayId}&newSession=true&lang=${this.currentLang}`;
                            }
                        });
                    }
                });
            }

            updateTimers() {
                displays.forEach(display => {
                    if (display.status === 'occupied' && display.endTime) {
                        const timerElement = document.getElementById(`timer-${display.id}`);
                        if (timerElement) {
                            const timeInfo = this.getTimeInfo(display);
                            timerElement.textContent = timeInfo.display;

                            // تحديث لون العداد إذا انتهت المدة
                            const timeContainer = timerElement.closest('.time-info');
                            if (timeContainer) {
                                timeContainer.classList.toggle('time-expired', timeInfo.expired);
                            }

                            // إعادة تحميل الشاشات إذا تغيرت الحالة
                            if (timeInfo.expired && !display.canReuse) {
                                display.canReuse = true;
                                display.canExtend = false;
                                this.loadDisplays();
                            }
                        }
                    }
                });
            }

            refreshDisplays() {
                console.log('🔄 Refreshing displays...');

                // Add loading animation
                const refreshBtn = document.getElementById('refresh-displays');
                if (refreshBtn) {
                    refreshBtn.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        refreshBtn.style.transform = '';
                    }, 500);
                }

                // Simulate data refresh
                setTimeout(() => {
                    this.loadDisplays();
                }, 500);
            }
        }

        // Global Modal Functions
        function closeModal() {
            if (displayManager && displayManager.modalManager) {
                displayManager.modalManager.hide();
            }
        }

        function confirmModal() {
            if (displayManager && displayManager.modalManager) {
                displayManager.modalManager.confirm();
            }
        }

        // Initialize
        let displayManager;
        document.addEventListener('DOMContentLoaded', () => {
            displayManager = new DisplayManager();

            // ربط أحداث النافذة المنبثقة
            document.getElementById('confirmBtn').addEventListener('click', confirmModal);

            // إغلاق النافذة عند النقر خارجها
            document.getElementById('confirmModal').addEventListener('click', (e) => {
                if (e.target.id === 'confirmModal') {
                    closeModal();
                }
            });

            // إغلاق النافذة بمفتاح Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });

            // تحديث العدادات كل ثانية
            setInterval(() => {
                displayManager.updateTimers();
            }, 1000);
        });
    </script>
</body>
</html>
