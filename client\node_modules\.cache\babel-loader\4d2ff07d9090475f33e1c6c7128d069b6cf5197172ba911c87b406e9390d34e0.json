{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate}from'react-router-dom';import styled from'styled-components';import{useAuth}from'../hooks/useAuth';import{ownerService,displayService}from'../services/api';import{formatCurrency,formatTimeRemaining}from'../utils/helpers';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Container=styled.div`\n  min-height: 100vh;\n  background: #f8f9fa;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Header=styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n`;const HeaderContent=styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;const Title=styled.h1`\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0;\n`;const LogoutButton=styled.button`\n  background: rgba(255,255,255,0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: white;\n    color: #667eea;\n  }\n`;const Content=styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 30px 20px;\n`;const StatsGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n`;const StatCard=styled.div`\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  text-align: center;\n`;const StatValue=styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: ${props=>props.color||'#667eea'};\n  margin-bottom: 10px;\n`;const StatLabel=styled.div`\n  font-size: 1rem;\n  color: #666;\n  font-weight: 600;\n`;const Section=styled.div`\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n`;const SectionTitle=styled.h2`\n  font-size: 1.5rem;\n  color: #333;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;const DisplayGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;const DisplayCard=styled.div`\n  border: 3px solid ${props=>{switch(props.status){case'available':return'#28a745';case'occupied':return'#dc3545';case'reserved':return'#ffc107';case'maintenance':return'#6c757d';default:return'#ddd';}}};\n  border-radius: 10px;\n  padding: 20px;\n  background: #f8f9fa;\n`;const DisplayNumber=styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n`;const DisplayStatus=styled.div`\n  display: inline-block;\n  padding: 5px 15px;\n  border-radius: 15px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 10px;\n  background: ${props=>{switch(props.status){case'available':return'#28a745';case'occupied':return'#dc3545';case'reserved':return'#ffc107';case'maintenance':return'#6c757d';default:return'#ddd';}}};\n`;const CustomerInfo=styled.div`\n  background: white;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 10px;\n`;const TransactionTable=styled.div`\n  overflow-x: auto;\n`;const Table=styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n`;const TableHeader=styled.th`\n  background: #f8f9fa;\n  padding: 12px;\n  text-align: ${props=>props.isRTL?'right':'left'};\n  border-bottom: 2px solid #ddd;\n  font-weight: 600;\n  color: #333;\n`;const TableCell=styled.td`\n  padding: 12px;\n  border-bottom: 1px solid #eee;\n  text-align: ${props=>props.isRTL?'right':'left'};\n`;const LoadingSpinner=styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  font-size: 1.2rem;\n  color: #666;\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n  margin: 20px 0;\n`;const OwnerDashboard=()=>{const{t,i18n}=useTranslation();const navigate=useNavigate();const{user,logout}=useAuth();const[dashboardData,setDashboardData]=useState(null);const[displays,setDisplays]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const isRTL=i18n.language==='ar';useEffect(()=>{// التحقق من صلاحية المالك\nif(!user||user.type!=='owner'){navigate('/owner-login');return;}fetchDashboardData();fetchDisplays();// تحديث البيانات كل 30 ثانية\nconst interval=setInterval(()=>{fetchDashboardData();fetchDisplays();},30000);return()=>clearInterval(interval);},[user,navigate]);const fetchDashboardData=async()=>{try{const response=await ownerService.getDashboardStats();if(response.success){setDashboardData(response.data);setError(null);}else{setError(response.message||'فشل في تحميل البيانات');}}catch(error){setError('خطأ في الشبكة');console.error('Error fetching dashboard data:',error);}};const fetchDisplays=async()=>{try{const response=await displayService.getAllDisplays();if(response.success){setDisplays(response.data);}}catch(error){console.error('Error fetching displays:',error);}finally{setLoading(false);}};const handleLogout=()=>{logout();navigate('/');};const getStatusText=status=>{switch(status){case'available':return t('available');case'occupied':return t('occupied');case'reserved':return t('reserved');case'maintenance':return t('maintenance');default:return status;}};if(loading){return/*#__PURE__*/_jsxs(Container,{isRTL:isRTL,children:[/*#__PURE__*/_jsx(Header,{children:/*#__PURE__*/_jsx(HeaderContent,{children:/*#__PURE__*/_jsx(Title,{children:t('ownerDashboard')})})}),/*#__PURE__*/_jsx(Content,{children:/*#__PURE__*/_jsx(LoadingSpinner,{children:isRTL?'جاري التحميل...':'Loading...'})})]});}return/*#__PURE__*/_jsxs(Container,{isRTL:isRTL,children:[/*#__PURE__*/_jsx(Header,{children:/*#__PURE__*/_jsxs(HeaderContent,{children:[/*#__PURE__*/_jsx(Title,{children:t('ownerDashboard')}),/*#__PURE__*/_jsx(LogoutButton,{onClick:handleLogout,children:isRTL?'تسجيل الخروج':'Logout'})]})}),/*#__PURE__*/_jsxs(Content,{children:[error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),dashboardData&&/*#__PURE__*/_jsxs(StatsGrid,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{color:\"#28a745\",children:dashboardData.today.transactions}),/*#__PURE__*/_jsx(StatLabel,{children:t('todayTransactions')})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{color:\"#667eea\",children:formatCurrency(dashboardData.today.revenue,'SAR',isRTL?'ar-SA':'en-US')}),/*#__PURE__*/_jsx(StatLabel,{children:t('todayRevenue')})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{color:\"#dc3545\",children:dashboardData.total.transactions}),/*#__PURE__*/_jsx(StatLabel,{children:t('totalTransactions')})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{color:\"#ffc107\",children:formatCurrency(dashboardData.total.revenue,'SAR',isRTL?'ar-SA':'en-US')}),/*#__PURE__*/_jsx(StatLabel,{children:t('totalRevenue')})]})]}),/*#__PURE__*/_jsxs(Section,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:t('displayStatus')}),/*#__PURE__*/_jsx(DisplayGrid,{children:displays.map(display=>/*#__PURE__*/_jsxs(DisplayCard,{status:display.status,children:[/*#__PURE__*/_jsx(DisplayNumber,{children:t('displayNumber',{number:display.displayNumber})}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',marginBottom:'10px'},children:display.name}),/*#__PURE__*/_jsx(DisplayStatus,{status:display.status,children:getStatusText(display.status)}),display.status==='occupied'&&display.customerName&&/*#__PURE__*/_jsxs(CustomerInfo,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:isRTL?'العميل:':'Customer:'}),\" \",display.customerName]}),display.timeRemaining&&!display.timeRemaining.expired&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:isRTL?'الوقت المتبقي:':'Time remaining:'}),\" \",formatTimeRemaining(display.timeRemaining.remaining)]})]})]},display.id))})]}),dashboardData&&dashboardData.activeTransactions.length>0&&/*#__PURE__*/_jsxs(Section,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:t('activeTransactions')}),/*#__PURE__*/_jsx(TransactionTable,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{isRTL:isRTL,children:t('transactionNumber')}),/*#__PURE__*/_jsx(TableHeader,{isRTL:isRTL,children:t('customerName')}),/*#__PURE__*/_jsx(TableHeader,{isRTL:isRTL,children:isRTL?'الشاشة':'Display'}),/*#__PURE__*/_jsx(TableHeader,{isRTL:isRTL,children:t('amount')}),/*#__PURE__*/_jsx(TableHeader,{isRTL:isRTL,children:t('endTime')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:dashboardData.activeTransactions.map(transaction=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableCell,{isRTL:isRTL,children:transaction.transactionNumber}),/*#__PURE__*/_jsx(TableCell,{isRTL:isRTL,children:transaction.customerName}),/*#__PURE__*/_jsx(TableCell,{isRTL:isRTL,children:transaction.displayName}),/*#__PURE__*/_jsx(TableCell,{isRTL:isRTL,children:formatCurrency(transaction.amount,'SAR',isRTL?'ar-SA':'en-US')}),/*#__PURE__*/_jsx(TableCell,{isRTL:isRTL,children:new Date(transaction.endTime).toLocaleTimeString(isRTL?'ar-SA':'en-US')})]},transaction.id))})]})})]})]})]});};export default OwnerDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "styled", "useAuth", "ownerService", "displayService", "formatCurrency", "formatTimeRemaining", "jsx", "_jsx", "jsxs", "_jsxs", "Container", "div", "props", "isRTL", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title", "h1", "LogoutButton", "button", "Content", "StatsGrid", "StatCard", "StatValue", "color", "StatLabel", "Section", "SectionTitle", "h2", "DisplayGrid", "DisplayCard", "status", "DisplayNumber", "DisplayStatus", "CustomerInfo", "TransactionTable", "Table", "table", "TableHeader", "th", "TableCell", "td", "LoadingSpinner", "ErrorMessage", "OwnerDashboard", "t", "i18n", "navigate", "user", "logout", "dashboardData", "setDashboardData", "displays", "setDisplays", "loading", "setLoading", "error", "setError", "language", "type", "fetchDashboardData", "fetchDisplays", "interval", "setInterval", "clearInterval", "response", "getDashboardStats", "success", "data", "message", "console", "getAllDisplays", "handleLogout", "getStatusText", "children", "onClick", "today", "transactions", "revenue", "total", "map", "display", "number", "displayNumber", "style", "fontWeight", "marginBottom", "name", "customerName", "timeRemaining", "expired", "remaining", "id", "activeTransactions", "length", "transaction", "transactionNumber", "displayName", "amount", "Date", "endTime", "toLocaleTimeString"], "sources": ["D:/برمجة/tste 1/client/src/pages/OwnerDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../hooks/useAuth';\nimport { ownerService, displayService } from '../services/api';\nimport { formatCurrency, formatTimeRemaining } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: #f8f9fa;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Header = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n`;\n\nconst HeaderContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0;\n`;\n\nconst LogoutButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: white;\n    color: #667eea;\n  }\n`;\n\nconst Content = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 30px 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n`;\n\nconst StatCard = styled.div`\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  text-align: center;\n`;\n\nconst StatValue = styled.div`\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: ${props => props.color || '#667eea'};\n  margin-bottom: 10px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 1rem;\n  color: #666;\n  font-weight: 600;\n`;\n\nconst Section = styled.div`\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 1.5rem;\n  color: #333;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n\nconst DisplayGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n\nconst DisplayCard = styled.div`\n  border: 3px solid ${props => {\n    switch (props.status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  }};\n  border-radius: 10px;\n  padding: 20px;\n  background: #f8f9fa;\n`;\n\nconst DisplayNumber = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst DisplayStatus = styled.div`\n  display: inline-block;\n  padding: 5px 15px;\n  border-radius: 15px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 10px;\n  background: ${props => {\n    switch (props.status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  }};\n`;\n\nconst CustomerInfo = styled.div`\n  background: white;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 10px;\n`;\n\nconst TransactionTable = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n`;\n\nconst TableHeader = styled.th`\n  background: #f8f9fa;\n  padding: 12px;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n  border-bottom: 2px solid #ddd;\n  font-weight: 600;\n  color: #333;\n`;\n\nconst TableCell = styled.td`\n  padding: 12px;\n  border-bottom: 1px solid #eee;\n  text-align: ${props => props.isRTL ? 'right' : 'left'};\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  font-size: 1.2rem;\n  color: #666;\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n  margin: 20px 0;\n`;\n\nconst OwnerDashboard = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  \n  const [dashboardData, setDashboardData] = useState(null);\n  const [displays, setDisplays] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  const isRTL = i18n.language === 'ar';\n\n  useEffect(() => {\n    // التحقق من صلاحية المالك\n    if (!user || user.type !== 'owner') {\n      navigate('/owner-login');\n      return;\n    }\n\n    fetchDashboardData();\n    fetchDisplays();\n\n    // تحديث البيانات كل 30 ثانية\n    const interval = setInterval(() => {\n      fetchDashboardData();\n      fetchDisplays();\n    }, 30000);\n\n    return () => clearInterval(interval);\n  }, [user, navigate]);\n\n  const fetchDashboardData = async () => {\n    try {\n      const response = await ownerService.getDashboardStats();\n      if (response.success) {\n        setDashboardData(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل البيانات');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching dashboard data:', error);\n    }\n  };\n\n  const fetchDisplays = async () => {\n    try {\n      const response = await displayService.getAllDisplays();\n      if (response.success) {\n        setDisplays(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching displays:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'available': return t('available');\n      case 'occupied': return t('occupied');\n      case 'reserved': return t('reserved');\n      case 'maintenance': return t('maintenance');\n      default: return status;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container isRTL={isRTL}>\n        <Header>\n          <HeaderContent>\n            <Title>{t('ownerDashboard')}</Title>\n          </HeaderContent>\n        </Header>\n        <Content>\n          <LoadingSpinner>\n            {isRTL ? 'جاري التحميل...' : 'Loading...'}\n          </LoadingSpinner>\n        </Content>\n      </Container>\n    );\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Header>\n        <HeaderContent>\n          <Title>{t('ownerDashboard')}</Title>\n          <LogoutButton onClick={handleLogout}>\n            {isRTL ? 'تسجيل الخروج' : 'Logout'}\n          </LogoutButton>\n        </HeaderContent>\n      </Header>\n\n      <Content>\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        {dashboardData && (\n          <StatsGrid>\n            <StatCard>\n              <StatValue color=\"#28a745\">{dashboardData.today.transactions}</StatValue>\n              <StatLabel>{t('todayTransactions')}</StatLabel>\n            </StatCard>\n            <StatCard>\n              <StatValue color=\"#667eea\">\n                {formatCurrency(dashboardData.today.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')}\n              </StatValue>\n              <StatLabel>{t('todayRevenue')}</StatLabel>\n            </StatCard>\n            <StatCard>\n              <StatValue color=\"#dc3545\">{dashboardData.total.transactions}</StatValue>\n              <StatLabel>{t('totalTransactions')}</StatLabel>\n            </StatCard>\n            <StatCard>\n              <StatValue color=\"#ffc107\">\n                {formatCurrency(dashboardData.total.revenue, 'SAR', isRTL ? 'ar-SA' : 'en-US')}\n              </StatValue>\n              <StatLabel>{t('totalRevenue')}</StatLabel>\n            </StatCard>\n          </StatsGrid>\n        )}\n\n        <Section>\n          <SectionTitle>{t('displayStatus')}</SectionTitle>\n          <DisplayGrid>\n            {displays.map((display) => (\n              <DisplayCard key={display.id} status={display.status}>\n                <DisplayNumber>\n                  {t('displayNumber', { number: display.displayNumber })}\n                </DisplayNumber>\n                <div style={{ fontWeight: '600', marginBottom: '10px' }}>\n                  {display.name}\n                </div>\n                <DisplayStatus status={display.status}>\n                  {getStatusText(display.status)}\n                </DisplayStatus>\n                \n                {display.status === 'occupied' && display.customerName && (\n                  <CustomerInfo>\n                    <div><strong>{isRTL ? 'العميل:' : 'Customer:'}</strong> {display.customerName}</div>\n                    {display.timeRemaining && !display.timeRemaining.expired && (\n                      <div><strong>{isRTL ? 'الوقت المتبقي:' : 'Time remaining:'}</strong> {formatTimeRemaining(display.timeRemaining.remaining)}</div>\n                    )}\n                  </CustomerInfo>\n                )}\n              </DisplayCard>\n            ))}\n          </DisplayGrid>\n        </Section>\n\n        {dashboardData && dashboardData.activeTransactions.length > 0 && (\n          <Section>\n            <SectionTitle>{t('activeTransactions')}</SectionTitle>\n            <TransactionTable>\n              <Table>\n                <thead>\n                  <tr>\n                    <TableHeader isRTL={isRTL}>{t('transactionNumber')}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{t('customerName')}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{isRTL ? 'الشاشة' : 'Display'}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{t('amount')}</TableHeader>\n                    <TableHeader isRTL={isRTL}>{t('endTime')}</TableHeader>\n                  </tr>\n                </thead>\n                <tbody>\n                  {dashboardData.activeTransactions.map((transaction) => (\n                    <tr key={transaction.id}>\n                      <TableCell isRTL={isRTL}>{transaction.transactionNumber}</TableCell>\n                      <TableCell isRTL={isRTL}>{transaction.customerName}</TableCell>\n                      <TableCell isRTL={isRTL}>{transaction.displayName}</TableCell>\n                      <TableCell isRTL={isRTL}>\n                        {formatCurrency(transaction.amount, 'SAR', isRTL ? 'ar-SA' : 'en-US')}\n                      </TableCell>\n                      <TableCell isRTL={isRTL}>\n                        {new Date(transaction.endTime).toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US')}\n                      </TableCell>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </TransactionTable>\n          </Section>\n        )}\n      </Content>\n    </Container>\n  );\n};\n\nexport default OwnerDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,kBAAkB,CAC1C,OAASC,YAAY,CAAEC,cAAc,KAAQ,iBAAiB,CAC9D,OAASC,cAAc,CAAEC,mBAAmB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvE,KAAM,CAAAC,SAAS,CAAGV,MAAM,CAACW,GAAG;AAC5B;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGd,MAAM,CAACW,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,aAAa,CAAGf,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAK,KAAK,CAAGhB,MAAM,CAACiB,EAAE;AACvB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGlB,MAAM,CAACmB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGpB,MAAM,CAACW,GAAG;AAC1B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAU,SAAS,CAAGrB,MAAM,CAACW,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,QAAQ,CAAGtB,MAAM,CAACW,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,SAAS,CAAGvB,MAAM,CAACW,GAAG;AAC5B;AACA;AACA,WAAWC,KAAK,EAAIA,KAAK,CAACY,KAAK,EAAI,SAAS;AAC5C;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGzB,MAAM,CAACW,GAAG;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAe,OAAO,CAAG1B,MAAM,CAACW,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAG3B,MAAM,CAAC4B,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG7B,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAmB,WAAW,CAAG9B,MAAM,CAACW,GAAG;AAC9B,sBAAsBC,KAAK,EAAI,CAC3B,OAAQA,KAAK,CAACmB,MAAM,EAClB,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,aAAa,CAAE,MAAO,SAAS,CACpC,QAAS,MAAO,MAAM,CACxB,CACF,CAAC;AACH;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGhC,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAsB,aAAa,CAAGjC,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,EAAI,CACrB,OAAQA,KAAK,CAACmB,MAAM,EAClB,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,aAAa,CAAE,MAAO,SAAS,CACpC,QAAS,MAAO,MAAM,CACxB,CACF,CAAC;AACH,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGlC,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAwB,gBAAgB,CAAGnC,MAAM,CAACW,GAAG;AACnC;AACA,CAAC,CAED,KAAM,CAAAyB,KAAK,CAAGpC,MAAM,CAACqC,KAAK;AAC1B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGtC,MAAM,CAACuC,EAAE;AAC7B;AACA;AACA,gBAAgB3B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,OAAO,CAAG,MAAM;AACvD;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAA2B,SAAS,CAAGxC,MAAM,CAACyC,EAAE;AAC3B;AACA;AACA,gBAAgB7B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,OAAO,CAAG,MAAM;AACvD,CAAC,CAED,KAAM,CAAA6B,cAAc,CAAG1C,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAgC,YAAY,CAAG3C,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAiC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGhD,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAiD,QAAQ,CAAGhD,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEiD,IAAI,CAAEC,MAAO,CAAC,CAAGhD,OAAO,CAAC,CAAC,CAElC,KAAM,CAACiD,aAAa,CAAEC,gBAAgB,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACwD,QAAQ,CAAEC,WAAW,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0D,OAAO,CAAEC,UAAU,CAAC,CAAG3D,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4D,KAAK,CAAEC,QAAQ,CAAC,CAAG7D,QAAQ,CAAC,IAAI,CAAC,CAExC,KAAM,CAAAiB,KAAK,CAAGiC,IAAI,CAACY,QAAQ,GAAK,IAAI,CAEpC7D,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACmD,IAAI,EAAIA,IAAI,CAACW,IAAI,GAAK,OAAO,CAAE,CAClCZ,QAAQ,CAAC,cAAc,CAAC,CACxB,OACF,CAEAa,kBAAkB,CAAC,CAAC,CACpBC,aAAa,CAAC,CAAC,CAEf;AACA,KAAM,CAAAC,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCH,kBAAkB,CAAC,CAAC,CACpBC,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,KAAK,CAAC,CAET,MAAO,IAAMG,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACd,IAAI,CAAED,QAAQ,CAAC,CAAC,CAEpB,KAAM,CAAAa,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA/D,YAAY,CAACgE,iBAAiB,CAAC,CAAC,CACvD,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpBhB,gBAAgB,CAACc,QAAQ,CAACG,IAAI,CAAC,CAC/BX,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,IAAM,CACLA,QAAQ,CAACQ,QAAQ,CAACI,OAAO,EAAI,uBAAuB,CAAC,CACvD,CACF,CAAE,MAAOb,KAAK,CAAE,CACdC,QAAQ,CAAC,eAAe,CAAC,CACzBa,OAAO,CAACd,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAAK,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAA9D,cAAc,CAACoE,cAAc,CAAC,CAAC,CACtD,GAAIN,QAAQ,CAACE,OAAO,CAAE,CACpBd,WAAW,CAACY,QAAQ,CAACG,IAAI,CAAC,CAC5B,CACF,CAAE,MAAOZ,KAAK,CAAE,CACdc,OAAO,CAACd,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiB,YAAY,CAAGA,CAAA,GAAM,CACzBvB,MAAM,CAAC,CAAC,CACRF,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,KAAM,CAAA0B,aAAa,CAAI1C,MAAM,EAAK,CAChC,OAAQA,MAAM,EACZ,IAAK,WAAW,CAAE,MAAO,CAAAc,CAAC,CAAC,WAAW,CAAC,CACvC,IAAK,UAAU,CAAE,MAAO,CAAAA,CAAC,CAAC,UAAU,CAAC,CACrC,IAAK,UAAU,CAAE,MAAO,CAAAA,CAAC,CAAC,UAAU,CAAC,CACrC,IAAK,aAAa,CAAE,MAAO,CAAAA,CAAC,CAAC,aAAa,CAAC,CAC3C,QAAS,MAAO,CAAAd,MAAM,CACxB,CACF,CAAC,CAED,GAAIuB,OAAO,CAAE,CACX,mBACE7C,KAAA,CAACC,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAA6D,QAAA,eACtBnE,IAAA,CAACO,MAAM,EAAA4D,QAAA,cACLnE,IAAA,CAACQ,aAAa,EAAA2D,QAAA,cACZnE,IAAA,CAACS,KAAK,EAAA0D,QAAA,CAAE7B,CAAC,CAAC,gBAAgB,CAAC,CAAQ,CAAC,CACvB,CAAC,CACV,CAAC,cACTtC,IAAA,CAACa,OAAO,EAAAsD,QAAA,cACNnE,IAAA,CAACmC,cAAc,EAAAgC,QAAA,CACZ7D,KAAK,CAAG,iBAAiB,CAAG,YAAY,CAC3B,CAAC,CACV,CAAC,EACD,CAAC,CAEhB,CAEA,mBACEJ,KAAA,CAACC,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAA6D,QAAA,eACtBnE,IAAA,CAACO,MAAM,EAAA4D,QAAA,cACLjE,KAAA,CAACM,aAAa,EAAA2D,QAAA,eACZnE,IAAA,CAACS,KAAK,EAAA0D,QAAA,CAAE7B,CAAC,CAAC,gBAAgB,CAAC,CAAQ,CAAC,cACpCtC,IAAA,CAACW,YAAY,EAACyD,OAAO,CAAEH,YAAa,CAAAE,QAAA,CACjC7D,KAAK,CAAG,cAAc,CAAG,QAAQ,CACtB,CAAC,EACF,CAAC,CACV,CAAC,cAETJ,KAAA,CAACW,OAAO,EAAAsD,QAAA,EACLlB,KAAK,eAAIjD,IAAA,CAACoC,YAAY,EAAA+B,QAAA,CAAElB,KAAK,CAAe,CAAC,CAE7CN,aAAa,eACZzC,KAAA,CAACY,SAAS,EAAAqD,QAAA,eACRjE,KAAA,CAACa,QAAQ,EAAAoD,QAAA,eACPnE,IAAA,CAACgB,SAAS,EAACC,KAAK,CAAC,SAAS,CAAAkD,QAAA,CAAExB,aAAa,CAAC0B,KAAK,CAACC,YAAY,CAAY,CAAC,cACzEtE,IAAA,CAACkB,SAAS,EAAAiD,QAAA,CAAE7B,CAAC,CAAC,mBAAmB,CAAC,CAAY,CAAC,EACvC,CAAC,cACXpC,KAAA,CAACa,QAAQ,EAAAoD,QAAA,eACPnE,IAAA,CAACgB,SAAS,EAACC,KAAK,CAAC,SAAS,CAAAkD,QAAA,CACvBtE,cAAc,CAAC8C,aAAa,CAAC0B,KAAK,CAACE,OAAO,CAAE,KAAK,CAAEjE,KAAK,CAAG,OAAO,CAAG,OAAO,CAAC,CACrE,CAAC,cACZN,IAAA,CAACkB,SAAS,EAAAiD,QAAA,CAAE7B,CAAC,CAAC,cAAc,CAAC,CAAY,CAAC,EAClC,CAAC,cACXpC,KAAA,CAACa,QAAQ,EAAAoD,QAAA,eACPnE,IAAA,CAACgB,SAAS,EAACC,KAAK,CAAC,SAAS,CAAAkD,QAAA,CAAExB,aAAa,CAAC6B,KAAK,CAACF,YAAY,CAAY,CAAC,cACzEtE,IAAA,CAACkB,SAAS,EAAAiD,QAAA,CAAE7B,CAAC,CAAC,mBAAmB,CAAC,CAAY,CAAC,EACvC,CAAC,cACXpC,KAAA,CAACa,QAAQ,EAAAoD,QAAA,eACPnE,IAAA,CAACgB,SAAS,EAACC,KAAK,CAAC,SAAS,CAAAkD,QAAA,CACvBtE,cAAc,CAAC8C,aAAa,CAAC6B,KAAK,CAACD,OAAO,CAAE,KAAK,CAAEjE,KAAK,CAAG,OAAO,CAAG,OAAO,CAAC,CACrE,CAAC,cACZN,IAAA,CAACkB,SAAS,EAAAiD,QAAA,CAAE7B,CAAC,CAAC,cAAc,CAAC,CAAY,CAAC,EAClC,CAAC,EACF,CACZ,cAEDpC,KAAA,CAACiB,OAAO,EAAAgD,QAAA,eACNnE,IAAA,CAACoB,YAAY,EAAA+C,QAAA,CAAE7B,CAAC,CAAC,eAAe,CAAC,CAAe,CAAC,cACjDtC,IAAA,CAACsB,WAAW,EAAA6C,QAAA,CACTtB,QAAQ,CAAC4B,GAAG,CAAEC,OAAO,eACpBxE,KAAA,CAACqB,WAAW,EAAkBC,MAAM,CAAEkD,OAAO,CAAClD,MAAO,CAAA2C,QAAA,eACnDnE,IAAA,CAACyB,aAAa,EAAA0C,QAAA,CACX7B,CAAC,CAAC,eAAe,CAAE,CAAEqC,MAAM,CAAED,OAAO,CAACE,aAAc,CAAC,CAAC,CACzC,CAAC,cAChB5E,IAAA,QAAK6E,KAAK,CAAE,CAAEC,UAAU,CAAE,KAAK,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAZ,QAAA,CACrDO,OAAO,CAACM,IAAI,CACV,CAAC,cACNhF,IAAA,CAAC0B,aAAa,EAACF,MAAM,CAAEkD,OAAO,CAAClD,MAAO,CAAA2C,QAAA,CACnCD,aAAa,CAACQ,OAAO,CAAClD,MAAM,CAAC,CACjB,CAAC,CAEfkD,OAAO,CAAClD,MAAM,GAAK,UAAU,EAAIkD,OAAO,CAACO,YAAY,eACpD/E,KAAA,CAACyB,YAAY,EAAAwC,QAAA,eACXjE,KAAA,QAAAiE,QAAA,eAAKnE,IAAA,WAAAmE,QAAA,CAAS7D,KAAK,CAAG,SAAS,CAAG,WAAW,CAAS,CAAC,IAAC,CAACoE,OAAO,CAACO,YAAY,EAAM,CAAC,CACnFP,OAAO,CAACQ,aAAa,EAAI,CAACR,OAAO,CAACQ,aAAa,CAACC,OAAO,eACtDjF,KAAA,QAAAiE,QAAA,eAAKnE,IAAA,WAAAmE,QAAA,CAAS7D,KAAK,CAAG,gBAAgB,CAAG,iBAAiB,CAAS,CAAC,IAAC,CAACR,mBAAmB,CAAC4E,OAAO,CAACQ,aAAa,CAACE,SAAS,CAAC,EAAM,CACjI,EACW,CACf,GAlBeV,OAAO,CAACW,EAmBb,CACd,CAAC,CACS,CAAC,EACP,CAAC,CAET1C,aAAa,EAAIA,aAAa,CAAC2C,kBAAkB,CAACC,MAAM,CAAG,CAAC,eAC3DrF,KAAA,CAACiB,OAAO,EAAAgD,QAAA,eACNnE,IAAA,CAACoB,YAAY,EAAA+C,QAAA,CAAE7B,CAAC,CAAC,oBAAoB,CAAC,CAAe,CAAC,cACtDtC,IAAA,CAAC4B,gBAAgB,EAAAuC,QAAA,cACfjE,KAAA,CAAC2B,KAAK,EAAAsC,QAAA,eACJnE,IAAA,UAAAmE,QAAA,cACEjE,KAAA,OAAAiE,QAAA,eACEnE,IAAA,CAAC+B,WAAW,EAACzB,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAE7B,CAAC,CAAC,mBAAmB,CAAC,CAAc,CAAC,cACjEtC,IAAA,CAAC+B,WAAW,EAACzB,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAE7B,CAAC,CAAC,cAAc,CAAC,CAAc,CAAC,cAC5DtC,IAAA,CAAC+B,WAAW,EAACzB,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAE7D,KAAK,CAAG,QAAQ,CAAG,SAAS,CAAc,CAAC,cACvEN,IAAA,CAAC+B,WAAW,EAACzB,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAE7B,CAAC,CAAC,QAAQ,CAAC,CAAc,CAAC,cACtDtC,IAAA,CAAC+B,WAAW,EAACzB,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAE7B,CAAC,CAAC,SAAS,CAAC,CAAc,CAAC,EACrD,CAAC,CACA,CAAC,cACRtC,IAAA,UAAAmE,QAAA,CACGxB,aAAa,CAAC2C,kBAAkB,CAACb,GAAG,CAAEe,WAAW,eAChDtF,KAAA,OAAAiE,QAAA,eACEnE,IAAA,CAACiC,SAAS,EAAC3B,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAEqB,WAAW,CAACC,iBAAiB,CAAY,CAAC,cACpEzF,IAAA,CAACiC,SAAS,EAAC3B,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAEqB,WAAW,CAACP,YAAY,CAAY,CAAC,cAC/DjF,IAAA,CAACiC,SAAS,EAAC3B,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CAAEqB,WAAW,CAACE,WAAW,CAAY,CAAC,cAC9D1F,IAAA,CAACiC,SAAS,EAAC3B,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CACrBtE,cAAc,CAAC2F,WAAW,CAACG,MAAM,CAAE,KAAK,CAAErF,KAAK,CAAG,OAAO,CAAG,OAAO,CAAC,CAC5D,CAAC,cACZN,IAAA,CAACiC,SAAS,EAAC3B,KAAK,CAAEA,KAAM,CAAA6D,QAAA,CACrB,GAAI,CAAAyB,IAAI,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,kBAAkB,CAACxF,KAAK,CAAG,OAAO,CAAG,OAAO,CAAC,CACnE,CAAC,GATLkF,WAAW,CAACH,EAUjB,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACQ,CAAC,EACZ,CACV,EACM,CAAC,EACD,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAhD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}