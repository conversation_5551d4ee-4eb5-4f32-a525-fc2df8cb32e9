{"ast": null, "code": "import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context.js';\nexport function I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = useMemo(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value\n  }, children);\n}", "map": {"version": 3, "names": ["createElement", "useMemo", "I18nContext", "I18nextProvider", "i18n", "defaultNS", "children", "value", "Provider"], "sources": ["D:/برمجة/tste 1/node_modules/react-i18next/dist/es/I18nextProvider.js"], "sourcesContent": ["import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context.js';\nexport function I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = useMemo(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value\n  }, children);\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,OAAO,QAAQ,OAAO;AAC9C,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAO,SAASC,eAAeA,CAAC;EAC9BC,IAAI;EACJC,SAAS;EACTC;AACF,CAAC,EAAE;EACD,MAAMC,KAAK,GAAGN,OAAO,CAAC,OAAO;IAC3BG,IAAI;IACJC;EACF,CAAC,CAAC,EAAE,CAACD,IAAI,EAAEC,SAAS,CAAC,CAAC;EACtB,OAAOL,aAAa,CAACE,WAAW,CAACM,QAAQ,EAAE;IACzCD;EACF,CAAC,EAAED,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}