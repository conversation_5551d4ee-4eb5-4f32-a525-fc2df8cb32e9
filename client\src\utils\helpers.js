// تنسيق رقم الهاتف السعودي
export const formatSaudiPhoneNumber = (phoneNumber) => {
  // إزالة المسافات والرموز
  const cleanNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');
  
  // إزالة الرمز الدولي إذا كان موجوداً
  let formattedNumber = cleanNumber;
  if (formattedNumber.startsWith('+966')) {
    formattedNumber = formattedNumber.substring(4);
  } else if (formattedNumber.startsWith('966')) {
    formattedNumber = formattedNumber.substring(3);
  } else if (formattedNumber.startsWith('0')) {
    formattedNumber = formattedNumber.substring(1);
  }
  
  // إضافة الرمز الدولي
  return `+966${formattedNumber}`;
};

// التحقق من صحة رقم الهاتف السعودي
export const validateSaudiPhoneNumber = (phoneNumber) => {
  const cleanNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');
  const saudiPattern = /^(\+966|966|0)?[5][0-9]{8}$/;
  return saudiPattern.test(cleanNumber);
};

// التحقق من صحة البريد الإلكتروني
export const validateEmail = (email) => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

// تنسيق الوقت المتبقي
export const formatTimeRemaining = (seconds) => {
  if (seconds <= 0) return '00:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
};

// تنسيق المبلغ
export const formatCurrency = (amount, currency = 'SAR', locale = 'ar-SA') => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount);
};

// تنسيق التاريخ
export const formatDate = (date, locale = 'ar-SA') => {
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Asia/Riyadh'
  };
  
  return new Intl.DateTimeFormat(locale, options).format(new Date(date));
};

// حساب الوقت المتبقي من تاريخ الانتهاء
export const calculateTimeRemaining = (endTime) => {
  const now = new Date();
  const end = new Date(endTime);
  const diff = end - now;
  
  if (diff <= 0) {
    return { expired: true, remaining: 0 };
  }
  
  return {
    expired: false,
    remaining: Math.floor(diff / 1000),
    minutes: Math.floor(diff / (1000 * 60)),
    seconds: Math.floor((diff % (1000 * 60)) / 1000)
  };
};

// تحويل الثواني إلى دقائق
export const secondsToMinutes = (seconds) => {
  return Math.floor(seconds / 60);
};

// تحويل الدقائق إلى ثواني
export const minutesToSeconds = (minutes) => {
  return minutes * 60;
};

// تنظيف النص من الأحرف الخاصة
export const sanitizeText = (text) => {
  return text.replace(/[<>\"'&]/g, '');
};

// حفظ البيانات في localStorage
export const saveToLocalStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    return false;
  }
};

// استرجاع البيانات من localStorage
export const getFromLocalStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return defaultValue;
  }
};

// إزالة البيانات من localStorage
export const removeFromLocalStorage = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error removing from localStorage:', error);
    return false;
  }
};

// تأخير التنفيذ
export const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// إنشاء معرف فريد
export const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// التحقق من كون الجهاز محمول
export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// التحقق من كون الجهاز tablet
export const isTablet = () => {
  return /iPad|Android/i.test(navigator.userAgent) && window.innerWidth >= 768;
};

// الحصول على حجم الشاشة
export const getScreenSize = () => {
  return {
    width: window.innerWidth,
    height: window.innerHeight
  };
};

// تحويل الأرقام إلى العربية
export const toArabicNumbers = (str) => {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  return str.toString().replace(/[0-9]/g, (digit) => arabicNumbers[parseInt(digit)]);
};

// تحويل الأرقام إلى الإنجليزية
export const toEnglishNumbers = (str) => {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  let result = str.toString();
  arabicNumbers.forEach((arabicNum, index) => {
    result = result.replace(new RegExp(arabicNum, 'g'), index.toString());
  });
  return result;
};

// نسخ النص إلى الحافظة
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    // Fallback للمتصفحات القديمة
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (fallbackError) {
      document.body.removeChild(textArea);
      console.error('Failed to copy text:', fallbackError);
      return false;
    }
  }
};

// إظهار إشعار
export const showNotification = (title, options = {}) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    return new Notification(title, options);
  } else if ('Notification' in window && Notification.permission !== 'denied') {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        return new Notification(title, options);
      }
    });
  }
  return null;
};

// طلب إذن الإشعارات
export const requestNotificationPermission = async () => {
  if ('Notification' in window) {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }
  return false;
};

// التحقق من الاتصال بالإنترنت
export const isOnline = () => {
  return navigator.onLine;
};

// مراقبة حالة الاتصال
export const watchOnlineStatus = (callback) => {
  const handleOnline = () => callback(true);
  const handleOffline = () => callback(false);
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // إرجاع دالة لإلغاء المراقبة
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};
