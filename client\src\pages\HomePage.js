import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Card = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
  margin: 20px;
`;

const Logo = styled.div`
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin: 0 auto 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  font-weight: bold;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 40px;
`;

const LanguageSelector = styled.div`
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 30px;
`;

const LanguageButton = styled.button`
  padding: 10px 20px;
  border: 2px solid ${props => props.active ? '#667eea' : '#ddd'};
  background: ${props => props.active ? '#667eea' : 'white'};
  color: ${props => props.active ? 'white' : '#333'};
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    background: ${props => props.active ? '#5a6fd8' : '#f8f9ff'};
  }
`;

const StartButton = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  font-size: 1.3rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`;

const OwnerLoginLink = styled.button`
  background: none;
  border: none;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  margin-top: 20px;
  text-decoration: underline;

  &:hover {
    color: #333;
  }
`;

const HomePage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  const isRTL = currentLanguage === 'ar';

  const handleLanguageChange = (language) => {
    setCurrentLanguage(language);
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
  };

  const handleStartTransaction = () => {
    navigate('/select-display');
  };

  const handleOwnerLogin = () => {
    navigate('/owner-login');
  };

  return (
    <Container isRTL={isRTL}>
      <Card>
        <Logo>
          📺
        </Logo>
        
        <Title>{t('companyName')}</Title>
        <Subtitle>{t('welcome')}</Subtitle>
        
        <div>
          <p style={{ marginBottom: '15px', color: '#666', fontSize: '1rem' }}>
            {t('selectLanguage')}
          </p>
          <LanguageSelector>
            <LanguageButton
              active={currentLanguage === 'ar'}
              onClick={() => handleLanguageChange('ar')}
            >
              العربية
            </LanguageButton>
            <LanguageButton
              active={currentLanguage === 'en'}
              onClick={() => handleLanguageChange('en')}
            >
              English
            </LanguageButton>
          </LanguageSelector>
        </div>
        
        <StartButton onClick={handleStartTransaction}>
          {t('startTransaction')}
        </StartButton>
        
        <OwnerLoginLink onClick={handleOwnerLogin}>
          {currentLanguage === 'ar' ? 'دخول المالك' : 'Owner Login'}
        </OwnerLoginLink>
      </Card>
    </Container>
  );
};

export default HomePage;
