{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\professional-display-system\\\\src\\\\pages\\\\DisplaySelectionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from '../Router';\nimport { useApp, useTranslation } from '../context/AppContext';\nimport { useNotification } from '../components/Notification';\nimport { mockDataService } from '../services/mockDataService';\nimport Button from '../components/Button';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DisplaySelectionPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    state,\n    selectDisplay\n  } = useApp();\n  const {\n    t\n  } = useTranslation();\n  const {\n    showNotification\n  } = useNotification();\n  const [displays, setDisplays] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedDisplayId, setSelectedDisplayId] = useState(null);\n  useEffect(() => {\n    loadDisplays();\n\n    // تحديث الشاشات كل 30 ثانية\n    const interval = setInterval(loadDisplays, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const loadDisplays = async () => {\n    try {\n      const response = await mockDataService.getAllDisplays();\n      if (response.success && response.data) {\n        setDisplays(response.data);\n      } else {\n        showNotification({\n          type: 'error',\n          message: response.error || t('serverError')\n        });\n      }\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        message: t('networkError')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDisplaySelect = async display => {\n    if (display.status !== 'available') {\n      showNotification({\n        type: 'warning',\n        message: state.language === 'ar' ? 'هذه الشاشة غير متاحة حالياً' : 'This display is not available'\n      });\n      return;\n    }\n    setSelectedDisplayId(display.id);\n    selectDisplay(display);\n\n    // الانتقال لصفحة تسجيل الدخول\n    navigate('/login');\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'available':\n        return '#28a745';\n      case 'occupied':\n        return '#dc3545';\n      case 'reserved':\n        return '#ffc107';\n      case 'maintenance':\n        return '#6c757d';\n      default:\n        return '#ddd';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'available':\n        return t('available');\n      case 'occupied':\n        return t('occupied');\n      case 'reserved':\n        return t('reserved');\n      case 'maintenance':\n        return t('maintenance');\n      default:\n        return status;\n    }\n  };\n  const formatTimeRemaining = endTime => {\n    if (!endTime) return '';\n    const now = new Date();\n    const end = new Date(endTime);\n    const diff = end.getTime() - now.getTime();\n    if (diff <= 0) return '';\n    const minutes = Math.floor(diff / (1000 * 60));\n    const seconds = Math.floor(diff % (1000 * 60) / 1000);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-container\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        fullScreen: true,\n        text: t('loading'),\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `display-selection-page ${state.language === 'ar' ? 'rtl' : 'ltr'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => navigate('/'),\n          className: \"back-button\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-icon\",\n            children: state.language === 'ar' ? '→' : '←'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), t('back')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: t('selectDisplay')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"refresh-button\",\n          onClick: loadDisplays,\n          title: state.language === 'ar' ? 'تحديث' : 'Refresh',\n          children: \"\\uD83D\\uDD04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"displays-grid\",\n        children: displays.map(display => {\n          const timeRemaining = formatTimeRemaining(display.endTime);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `display-card ${display.status} ${selectedDisplayId === display.id ? 'selected' : ''}`,\n            onClick: () => handleDisplaySelect(display),\n            style: {\n              borderColor: getStatusColor(display.status),\n              cursor: display.status === 'available' ? 'pointer' : 'not-allowed'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"display-number\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"number\",\n                children: display.displayNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"number-bg\",\n                style: {\n                  backgroundColor: getStatusColor(display.status)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"display-name\",\n              children: display.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"display-status\",\n              style: {\n                backgroundColor: getStatusColor(display.status)\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: getStatusText(display.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), display.status === 'occupied' && timeRemaining && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"time-remaining\",\n                children: [\"\\u23F1\\uFE0F \", timeRemaining]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), display.customerName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"customer-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"customer-label\",\n                children: state.language === 'ar' ? 'العميل:' : 'Customer:'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"customer-name\",\n                children: display.customerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this), display.status === 'available' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"select-button\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"small\",\n                fullWidth: true,\n                onClick: e => {\n                  e.stopPropagation();\n                  handleDisplaySelect(display);\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-icon\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this), t('selectThisDisplay')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-indicator\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `indicator-dot ${display.status}`,\n                style: {\n                  backgroundColor: getStatusColor(display.status)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), selectedDisplayId === display.id && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selection-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selection-checkmark\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this)]\n          }, display.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-info\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: state.language === 'ar' ? 'معلومات الخدمة' : 'Service Information'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCB0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-text\",\n                children: t('displayPrice', {\n                  price: state.settings.displayPrice\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\u23F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-text\",\n                children: t('displayDuration', {\n                  duration: Math.floor(state.settings.displayDuration / 60)\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-text\",\n                children: state.language === 'ar' ? 'دفع آمن بالبطاقة اللاسلكية' : 'Secure NFC Payment'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-legend\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-dot\",\n            style: {\n              backgroundColor: getStatusColor('available')\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('available')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-dot\",\n            style: {\n              backgroundColor: getStatusColor('occupied')\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('occupied')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-dot\",\n            style: {\n              backgroundColor: getStatusColor('reserved')\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('reserved')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"legend-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"legend-dot\",\n            style: {\n              backgroundColor: getStatusColor('maintenance')\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('maintenance')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(DisplaySelectionPage, \"ln7EG+lmYMNT8o74W65oEKoi020=\", false, function () {\n  return [useNavigate, useApp, useTranslation, useNotification];\n});\n_c = DisplaySelectionPage;\nexport default DisplaySelectionPage;\nvar _c;\n$RefreshReg$(_c, \"DisplaySelectionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useApp", "useTranslation", "useNotification", "mockDataService", "<PERSON><PERSON>", "LoadingSpinner", "jsxDEV", "_jsxDEV", "DisplaySelectionPage", "_s", "navigate", "state", "selectDisplay", "t", "showNotification", "displays", "setDisplays", "loading", "setLoading", "selectedDisplayId", "setSelectedDisplayId", "loadDisplays", "interval", "setInterval", "clearInterval", "response", "getAllDisplays", "success", "data", "type", "message", "error", "handleDisplaySelect", "display", "status", "language", "id", "getStatusColor", "getStatusText", "formatTimeRemaining", "endTime", "now", "Date", "end", "diff", "getTime", "minutes", "Math", "floor", "seconds", "toString", "padStart", "className", "children", "fullScreen", "text", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "title", "map", "timeRemaining", "style", "borderColor", "cursor", "displayNumber", "backgroundColor", "name", "customerName", "fullWidth", "e", "stopPropagation", "price", "settings", "displayPrice", "duration", "displayDuration", "_c", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/professional-display-system/src/pages/DisplaySelectionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from '../Router';\nimport { useApp, useTranslation } from '../context/AppContext';\nimport { useNotification } from '../components/Notification';\nimport { mockDataService } from '../services/mockDataService';\nimport { Display } from '../types';\nimport Button from '../components/Button';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst DisplaySelectionPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { state, selectDisplay } = useApp();\n  const { t } = useTranslation();\n  const { showNotification } = useNotification();\n  \n  const [displays, setDisplays] = useState<Display[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedDisplayId, setSelectedDisplayId] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadDisplays();\n    \n    // تحديث الشاشات كل 30 ثانية\n    const interval = setInterval(loadDisplays, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const loadDisplays = async () => {\n    try {\n      const response = await mockDataService.getAllDisplays();\n      if (response.success && response.data) {\n        setDisplays(response.data);\n      } else {\n        showNotification({\n          type: 'error',\n          message: response.error || t('serverError')\n        });\n      }\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        message: t('networkError')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDisplaySelect = async (display: Display) => {\n    if (display.status !== 'available') {\n      showNotification({\n        type: 'warning',\n        message: state.language === 'ar' \n          ? 'هذه الشاشة غير متاحة حالياً' \n          : 'This display is not available'\n      });\n      return;\n    }\n\n    setSelectedDisplayId(display.id);\n    selectDisplay(display);\n    \n    // الانتقال لصفحة تسجيل الدخول\n    navigate('/login');\n  };\n\n  const getStatusColor = (status: Display['status']) => {\n    switch (status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  };\n\n  const getStatusText = (status: Display['status']) => {\n    switch (status) {\n      case 'available': return t('available');\n      case 'occupied': return t('occupied');\n      case 'reserved': return t('reserved');\n      case 'maintenance': return t('maintenance');\n      default: return status;\n    }\n  };\n\n  const formatTimeRemaining = (endTime?: string) => {\n    if (!endTime) return '';\n    \n    const now = new Date();\n    const end = new Date(endTime);\n    const diff = end.getTime() - now.getTime();\n    \n    if (diff <= 0) return '';\n    \n    const minutes = Math.floor(diff / (1000 * 60));\n    const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n    \n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"page-container\">\n        <LoadingSpinner \n          fullScreen \n          text={t('loading')}\n          size=\"large\"\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className={`display-selection-page ${state.language === 'ar' ? 'rtl' : 'ltr'}`}>\n      <div className=\"page-container\">\n        {/* الهيدر */}\n        <div className=\"page-header\">\n          <Button\n            variant=\"secondary\"\n            onClick={() => navigate('/')}\n            className=\"back-button\"\n          >\n            <span className=\"btn-icon\">\n              {state.language === 'ar' ? '→' : '←'}\n            </span>\n            {t('back')}\n          </Button>\n          \n          <h1 className=\"page-title\">{t('selectDisplay')}</h1>\n          \n          <button \n            className=\"refresh-button\"\n            onClick={loadDisplays}\n            title={state.language === 'ar' ? 'تحديث' : 'Refresh'}\n          >\n            🔄\n          </button>\n        </div>\n\n        {/* شبكة الشاشات */}\n        <div className=\"displays-grid\">\n          {displays.map(display => {\n            const timeRemaining = formatTimeRemaining(display.endTime);\n            \n            return (\n              <div\n                key={display.id}\n                className={`display-card ${display.status} ${selectedDisplayId === display.id ? 'selected' : ''}`}\n                onClick={() => handleDisplaySelect(display)}\n                style={{ \n                  borderColor: getStatusColor(display.status),\n                  cursor: display.status === 'available' ? 'pointer' : 'not-allowed'\n                }}\n              >\n                {/* رقم الشاشة */}\n                <div className=\"display-number\">\n                  <span className=\"number\">{display.displayNumber}</span>\n                  <div className=\"number-bg\" style={{ backgroundColor: getStatusColor(display.status) }}></div>\n                </div>\n\n                {/* اسم الشاشة */}\n                <div className=\"display-name\">\n                  {display.name}\n                </div>\n\n                {/* حالة الشاشة */}\n                <div \n                  className=\"display-status\"\n                  style={{ backgroundColor: getStatusColor(display.status) }}\n                >\n                  <span className=\"status-text\">{getStatusText(display.status)}</span>\n                  {display.status === 'occupied' && timeRemaining && (\n                    <span className=\"time-remaining\">\n                      ⏱️ {timeRemaining}\n                    </span>\n                  )}\n                </div>\n\n                {/* معلومات العميل */}\n                {display.customerName && (\n                  <div className=\"customer-info\">\n                    <span className=\"customer-label\">\n                      {state.language === 'ar' ? 'العميل:' : 'Customer:'}\n                    </span>\n                    <span className=\"customer-name\">{display.customerName}</span>\n                  </div>\n                )}\n\n                {/* زر الاختيار */}\n                {display.status === 'available' && (\n                  <div className=\"select-button\">\n                    <Button\n                      variant=\"success\"\n                      size=\"small\"\n                      fullWidth\n                      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {\n                        e.stopPropagation();\n                        handleDisplaySelect(display);\n                      }}\n                    >\n                      <span className=\"btn-icon\">✓</span>\n                      {t('selectThisDisplay')}\n                    </Button>\n                  </div>\n                )}\n\n                {/* مؤشر الحالة */}\n                <div className=\"status-indicator\">\n                  <div \n                    className={`indicator-dot ${display.status}`}\n                    style={{ backgroundColor: getStatusColor(display.status) }}\n                  ></div>\n                </div>\n\n                {/* تأثير الانتقاء */}\n                {selectedDisplayId === display.id && (\n                  <div className=\"selection-overlay\">\n                    <div className=\"selection-checkmark\">✓</div>\n                  </div>\n                )}\n              </div>\n            );\n          })}\n        </div>\n\n        {/* معلومات إضافية */}\n        <div className=\"page-info\">\n          <div className=\"info-card\">\n            <h3>{state.language === 'ar' ? 'معلومات الخدمة' : 'Service Information'}</h3>\n            <div className=\"info-grid\">\n              <div className=\"info-item\">\n                <span className=\"info-icon\">💰</span>\n                <span className=\"info-text\">\n                  {t('displayPrice', { price: state.settings.displayPrice })}\n                </span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">⏰</span>\n                <span className=\"info-text\">\n                  {t('displayDuration', { duration: Math.floor(state.settings.displayDuration / 60) })}\n                </span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">📱</span>\n                <span className=\"info-text\">\n                  {state.language === 'ar' \n                    ? 'دفع آمن بالبطاقة اللاسلكية' \n                    : 'Secure NFC Payment'\n                  }\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* مؤشر الحالات */}\n        <div className=\"status-legend\">\n          <div className=\"legend-item\">\n            <div className=\"legend-dot\" style={{ backgroundColor: getStatusColor('available') }}></div>\n            <span>{t('available')}</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-dot\" style={{ backgroundColor: getStatusColor('occupied') }}></div>\n            <span>{t('occupied')}</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-dot\" style={{ backgroundColor: getStatusColor('reserved') }}></div>\n            <span>{t('reserved')}</span>\n          </div>\n          <div className=\"legend-item\">\n            <div className=\"legend-dot\" style={{ backgroundColor: getStatusColor('maintenance') }}></div>\n            <span>{t('maintenance')}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DisplaySelectionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,eAAe,QAAQ,6BAA6B;AAE7D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,KAAK;IAAEC;EAAc,CAAC,GAAGZ,MAAM,CAAC,CAAC;EACzC,MAAM;IAAEa;EAAE,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAEa;EAAiB,CAAC,GAAGZ,eAAe,CAAC,CAAC;EAE9C,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EAE/EC,SAAS,CAAC,MAAM;IACduB,YAAY,CAAC,CAAC;;IAEd;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,YAAY,EAAE,KAAK,CAAC;IACjD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMtB,eAAe,CAACuB,cAAc,CAAC,CAAC;MACvD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCZ,WAAW,CAACS,QAAQ,CAACG,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLd,gBAAgB,CAAC;UACfe,IAAI,EAAE,OAAO;UACbC,OAAO,EAAEL,QAAQ,CAACM,KAAK,IAAIlB,CAAC,CAAC,aAAa;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdjB,gBAAgB,CAAC;QACfe,IAAI,EAAE,OAAO;QACbC,OAAO,EAAEjB,CAAC,CAAC,cAAc;MAC3B,CAAC,CAAC;IACJ,CAAC,SAAS;MACRK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAOC,OAAgB,IAAK;IACtD,IAAIA,OAAO,CAACC,MAAM,KAAK,WAAW,EAAE;MAClCpB,gBAAgB,CAAC;QACfe,IAAI,EAAE,SAAS;QACfC,OAAO,EAAEnB,KAAK,CAACwB,QAAQ,KAAK,IAAI,GAC5B,6BAA6B,GAC7B;MACN,CAAC,CAAC;MACF;IACF;IAEAf,oBAAoB,CAACa,OAAO,CAACG,EAAE,CAAC;IAChCxB,aAAa,CAACqB,OAAO,CAAC;;IAEtB;IACAvB,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM2B,cAAc,GAAIH,MAAyB,IAAK;IACpD,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMI,aAAa,GAAIJ,MAAyB,IAAK;IACnD,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAOrB,CAAC,CAAC,WAAW,CAAC;MACvC,KAAK,UAAU;QAAE,OAAOA,CAAC,CAAC,UAAU,CAAC;MACrC,KAAK,UAAU;QAAE,OAAOA,CAAC,CAAC,UAAU,CAAC;MACrC,KAAK,aAAa;QAAE,OAAOA,CAAC,CAAC,aAAa,CAAC;MAC3C;QAAS,OAAOqB,MAAM;IACxB;EACF,CAAC;EAED,MAAMK,mBAAmB,GAAIC,OAAgB,IAAK;IAChD,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,GAAG,GAAG,IAAID,IAAI,CAACF,OAAO,CAAC;IAC7B,MAAMI,IAAI,GAAGD,GAAG,CAACE,OAAO,CAAC,CAAC,GAAGJ,GAAG,CAACI,OAAO,CAAC,CAAC;IAE1C,IAAID,IAAI,IAAI,CAAC,EAAE,OAAO,EAAE;IAExB,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9C,MAAMK,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEJ,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;IAEvD,OAAO,GAAGE,OAAO,IAAIG,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D,CAAC;EAED,IAAIlC,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B9C,OAAA,CAACF,cAAc;QACbiD,UAAU;QACVC,IAAI,EAAE1C,CAAC,CAAC,SAAS,CAAE;QACnB2C,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAK6C,SAAS,EAAE,0BAA0BzC,KAAK,CAACwB,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,EAAG;IAAAkB,QAAA,eAClF9C,OAAA;MAAK6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7B9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA,CAACH,MAAM;UACLyD,OAAO,EAAC,WAAW;UACnBC,OAAO,EAAEA,CAAA,KAAMpD,QAAQ,CAAC,GAAG,CAAE;UAC7B0C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAEvB9C,OAAA;YAAM6C,SAAS,EAAC,UAAU;YAAAC,QAAA,EACvB1C,KAAK,CAACwB,QAAQ,KAAK,IAAI,GAAG,GAAG,GAAG;UAAG;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,EACN/C,CAAC,CAAC,MAAM,CAAC;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAETrD,OAAA;UAAI6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExC,CAAC,CAAC,eAAe;QAAC;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEpDrD,OAAA;UACE6C,SAAS,EAAC,gBAAgB;UAC1BU,OAAO,EAAEzC,YAAa;UACtB0C,KAAK,EAAEpD,KAAK,CAACwB,QAAQ,KAAK,IAAI,GAAG,OAAO,GAAG,SAAU;UAAAkB,QAAA,EACtD;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BtC,QAAQ,CAACiD,GAAG,CAAC/B,OAAO,IAAI;UACvB,MAAMgC,aAAa,GAAG1B,mBAAmB,CAACN,OAAO,CAACO,OAAO,CAAC;UAE1D,oBACEjC,OAAA;YAEE6C,SAAS,EAAE,gBAAgBnB,OAAO,CAACC,MAAM,IAAIf,iBAAiB,KAAKc,OAAO,CAACG,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;YAClG0B,OAAO,EAAEA,CAAA,KAAM9B,mBAAmB,CAACC,OAAO,CAAE;YAC5CiC,KAAK,EAAE;cACLC,WAAW,EAAE9B,cAAc,CAACJ,OAAO,CAACC,MAAM,CAAC;cAC3CkC,MAAM,EAAEnC,OAAO,CAACC,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG;YACvD,CAAE;YAAAmB,QAAA,gBAGF9C,OAAA;cAAK6C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9C,OAAA;gBAAM6C,SAAS,EAAC,QAAQ;gBAAAC,QAAA,EAAEpB,OAAO,CAACoC;cAAa;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvDrD,OAAA;gBAAK6C,SAAS,EAAC,WAAW;gBAACc,KAAK,EAAE;kBAAEI,eAAe,EAAEjC,cAAc,CAACJ,OAAO,CAACC,MAAM;gBAAE;cAAE;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC,eAGNrD,OAAA;cAAK6C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BpB,OAAO,CAACsC;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNrD,OAAA;cACE6C,SAAS,EAAC,gBAAgB;cAC1Bc,KAAK,EAAE;gBAAEI,eAAe,EAAEjC,cAAc,CAACJ,OAAO,CAACC,MAAM;cAAE,CAAE;cAAAmB,QAAA,gBAE3D9C,OAAA;gBAAM6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEf,aAAa,CAACL,OAAO,CAACC,MAAM;cAAC;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACnE3B,OAAO,CAACC,MAAM,KAAK,UAAU,IAAI+B,aAAa,iBAC7C1D,OAAA;gBAAM6C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,eAC5B,EAACY,aAAa;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL3B,OAAO,CAACuC,YAAY,iBACnBjE,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B9C,OAAA;gBAAM6C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC7B1C,KAAK,CAACwB,QAAQ,KAAK,IAAI,GAAG,SAAS,GAAG;cAAW;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACPrD,OAAA;gBAAM6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEpB,OAAO,CAACuC;cAAY;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CACN,EAGA3B,OAAO,CAACC,MAAM,KAAK,WAAW,iBAC7B3B,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B9C,OAAA,CAACH,MAAM;gBACLyD,OAAO,EAAC,SAAS;gBACjBL,IAAI,EAAC,OAAO;gBACZiB,SAAS;gBACTX,OAAO,EAAGY,CAAsC,IAAK;kBACnDA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnB3C,mBAAmB,CAACC,OAAO,CAAC;gBAC9B,CAAE;gBAAAoB,QAAA,gBAEF9C,OAAA;kBAAM6C,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAClC/C,CAAC,CAAC,mBAAmB,CAAC;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAGDrD,OAAA;cAAK6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B9C,OAAA;gBACE6C,SAAS,EAAE,iBAAiBnB,OAAO,CAACC,MAAM,EAAG;gBAC7CgC,KAAK,EAAE;kBAAEI,eAAe,EAAEjC,cAAc,CAACJ,OAAO,CAACC,MAAM;gBAAE;cAAE;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGLzC,iBAAiB,KAAKc,OAAO,CAACG,EAAE,iBAC/B7B,OAAA;cAAK6C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC9C,OAAA;gBAAK6C,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CACN;UAAA,GAzEI3B,OAAO,CAACG,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0EZ,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9C,OAAA;YAAA8C,QAAA,EAAK1C,KAAK,CAACwB,QAAQ,KAAK,IAAI,GAAG,gBAAgB,GAAG;UAAqB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7ErD,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9C,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9C,OAAA;gBAAM6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCrD,OAAA;gBAAM6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACxBxC,CAAC,CAAC,cAAc,EAAE;kBAAE+D,KAAK,EAAEjE,KAAK,CAACkE,QAAQ,CAACC;gBAAa,CAAC;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrD,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9C,OAAA;gBAAM6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpCrD,OAAA;gBAAM6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACxBxC,CAAC,CAAC,iBAAiB,EAAE;kBAAEkE,QAAQ,EAAEhC,IAAI,CAACC,KAAK,CAACrC,KAAK,CAACkE,QAAQ,CAACG,eAAe,GAAG,EAAE;gBAAE,CAAC;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrD,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9C,OAAA;gBAAM6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCrD,OAAA;gBAAM6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACxB1C,KAAK,CAACwB,QAAQ,KAAK,IAAI,GACpB,4BAA4B,GAC5B;cAAoB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9C,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAACc,KAAK,EAAE;cAAEI,eAAe,EAAEjC,cAAc,CAAC,WAAW;YAAE;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3FrD,OAAA;YAAA8C,QAAA,EAAOxC,CAAC,CAAC,WAAW;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACNrD,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAACc,KAAK,EAAE;cAAEI,eAAe,EAAEjC,cAAc,CAAC,UAAU;YAAE;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1FrD,OAAA;YAAA8C,QAAA,EAAOxC,CAAC,CAAC,UAAU;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNrD,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAACc,KAAK,EAAE;cAAEI,eAAe,EAAEjC,cAAc,CAAC,UAAU;YAAE;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1FrD,OAAA;YAAA8C,QAAA,EAAOxC,CAAC,CAAC,UAAU;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNrD,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAACc,KAAK,EAAE;cAAEI,eAAe,EAAEjC,cAAc,CAAC,aAAa;YAAE;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7FrD,OAAA;YAAA8C,QAAA,EAAOxC,CAAC,CAAC,aAAa;UAAC;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CA7QID,oBAA8B;EAAA,QACjBT,WAAW,EACKC,MAAM,EACzBC,cAAc,EACCC,eAAe;AAAA;AAAA+E,EAAA,GAJxCzE,oBAA8B;AA+QpC,eAAeA,oBAAoB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}