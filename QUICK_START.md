# دليل البدء السريع - نظام إدارة الشاشات
## Quick Start Guide - Display Management System

## 🚀 التشغيل السريع

### 1. تشغيل الخادم
```bash
npm start
```
الخادم سيعمل على: `http://localhost:3001`

### 2. تشغيل الواجهة الأمامية
```bash
cd client
npm start
```
الواجهة ستعمل على: `http://localhost:3000`

### 3. تشغيل كلاهما معاً (وضع التطوير)
```bash
npm run dev
```

## 🔗 الروابط المهمة

### للعملاء
- **الصفحة الرئيسية**: http://localhost:3000
- **اختيار الشاشة**: http://localhost:3000/select-display
- **تسجيل الدخول**: http://localhost:3000/login

### للمالك
- **تسجيل دخول المالك**: http://localhost:3000/owner-login
- **لوحة التحكم**: http://localhost:3000/owner-dashboard

### شاشات العرض
- **الشاشة 1**: http://localhost:3000/display/1
- **الشاشة 2**: http://localhost:3000/display/2
- **الشاشة 3**: http://localhost:3000/display/3
- **الشاشة 4**: http://localhost:3000/display/4
- **الشاشة 5**: http://localhost:3000/display/5

## 🔑 بيانات الدخول الافتراضية

### المالك
- **كلمة المرور**: `admin123`

### العملاء
- **رقم الهاتف**: أي رقم سعودي صحيح (مثل: +966501234567)
- **البريد الإلكتروني**: أي بريد إلكتروني صحيح
- **رمز OTP**: سيتم إرساله للبريد الإلكتروني المدخل

## 🧪 اختبار النظام

### 1. اختبار واجهة العميل
1. اذهب إلى http://localhost:3000
2. اختر اللغة (عربي/إنجليزي)
3. اضغط "ابدأ المعاملة"
4. اختر شاشة متاحة
5. أدخل رقم هاتف وبريد إلكتروني
6. تحقق من البريد الإلكتروني للحصول على OTP
7. أدخل الرمز
8. اكتب اسمك
9. أكمل عملية الدفع (محاكاة)
10. شاهد النتيجة

### 2. اختبار واجهة المالك
1. اذهب إلى http://localhost:3000/owner-login
2. أدخل كلمة المرور: `admin123`
3. راقب الإحصائيات في لوحة التحكم
4. شاهد حالة الشاشات
5. راقب المعاملات النشطة

### 3. اختبار شاشات العرض
1. افتح شاشة في نافذة منفصلة: http://localhost:3000/display/1
2. قم بمعاملة من واجهة العميل
3. شاهد التحديث المباشر على الشاشة

## ⚙️ إعدادات مهمة

### تكوين البريد الإلكتروني
في ملف `.env`:
```env
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

### تكوين Stripe (للدفع)
```env
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
```

### تكوين التطبيق
```env
DEFAULT_DISPLAY_PRICE=50
DEFAULT_DISPLAY_DURATION=300
OTP_EXPIRY_MINUTES=5
```

## 🐛 حل المشاكل الشائعة

### المشكلة: الخادم لا يعمل
**الحل**: تأكد من تثبيت التبعيات
```bash
npm install
```

### المشكلة: قاعدة البيانات لا تعمل
**الحل**: تأكد من وجود مجلد database
```bash
mkdir database
```

### المشكلة: OTP لا يُرسل
**الحل**: تحقق من إعدادات البريد الإلكتروني في `.env`

### المشكلة: الدفع لا يعمل
**الحل**: تحقق من مفاتيح Stripe في `.env`

## 📱 بناء تطبيق سطح المكتب

### بناء ملف .exe
```bash
npm run dist
```

### تشغيل Electron في وضع التطوير
```bash
npm run electron-dev
```

## 🔄 التحديثات المباشرة

النظام يدعم التحديثات المباشرة عبر Socket.IO:
- تحديث حالة الشاشات
- إشعارات المعاملات الجديدة
- تحديث الإحصائيات

## 📊 مراقبة النظام

### لوحة تحكم المالك تعرض:
- عدد المعاملات اليوم
- إيرادات اليوم
- إجمالي المعاملات
- إجمالي الإيرادات
- حالة جميع الشاشات
- المعاملات النشطة

## 🌐 دعم اللغات

النظام يدعم:
- العربية (افتراضي)
- الإنجليزية

يمكن تغيير اللغة من الصفحة الرئيسية.

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من console المتصفح للأخطاء
3. راجع logs الخادم في terminal

---

**نصيحة**: احتفظ بهذا الملف مفتوحاً أثناء الاختبار للرجوع السريع للروابط والإعدادات! 🚀
