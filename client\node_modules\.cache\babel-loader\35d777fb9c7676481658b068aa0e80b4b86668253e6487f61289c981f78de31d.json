{"ast": null, "code": "import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport const withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = useTranslation(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return createElement(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n};", "map": {"version": 3, "names": ["createElement", "forwardRef", "forwardRefReact", "useTranslation", "getDisplayName", "withTranslation", "ns", "options", "Extend", "WrappedComponent", "I18nextWithTranslation", "forwardedRef", "rest", "t", "i18n", "ready", "keyPrefix", "passDownProps", "tReady", "with<PERSON>ef", "ref", "displayName", "props", "Object", "assign"], "sources": ["D:/برمجة/tste 1/node_modules/react-i18next/dist/es/withTranslation.js"], "sourcesContent": ["import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport const withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = useTranslation(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return createElement(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n};"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,IAAIC,eAAe,QAAQ,OAAO;AACpE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,MAAMC,eAAe,GAAGA,CAACC,EAAE,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK,SAASC,MAAMA,CAACC,gBAAgB,EAAE;EACrF,SAASC,sBAAsBA,CAAC;IAC9BC,YAAY;IACZ,GAAGC;EACL,CAAC,EAAE;IACD,MAAM,CAACC,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGZ,cAAc,CAACG,EAAE,EAAE;MAC1C,GAAGM,IAAI;MACPI,SAAS,EAAET,OAAO,CAACS;IACrB,CAAC,CAAC;IACF,MAAMC,aAAa,GAAG;MACpB,GAAGL,IAAI;MACPC,CAAC;MACDC,IAAI;MACJI,MAAM,EAAEH;IACV,CAAC;IACD,IAAIR,OAAO,CAACY,OAAO,IAAIR,YAAY,EAAE;MACnCM,aAAa,CAACG,GAAG,GAAGT,YAAY;IAClC,CAAC,MAAM,IAAI,CAACJ,OAAO,CAACY,OAAO,IAAIR,YAAY,EAAE;MAC3CM,aAAa,CAACN,YAAY,GAAGA,YAAY;IAC3C;IACA,OAAOX,aAAa,CAACS,gBAAgB,EAAEQ,aAAa,CAAC;EACvD;EACAP,sBAAsB,CAACW,WAAW,GAAG,0BAA0BjB,cAAc,CAACK,gBAAgB,CAAC,GAAG;EAClGC,sBAAsB,CAACD,gBAAgB,GAAGA,gBAAgB;EAC1D,MAAMR,UAAU,GAAGA,CAACqB,KAAK,EAAEF,GAAG,KAAKpB,aAAa,CAACU,sBAAsB,EAAEa,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAE;IAChGX,YAAY,EAAES;EAChB,CAAC,CAAC,CAAC;EACH,OAAOb,OAAO,CAACY,OAAO,GAAGjB,eAAe,CAACD,UAAU,CAAC,GAAGS,sBAAsB;AAC/E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}