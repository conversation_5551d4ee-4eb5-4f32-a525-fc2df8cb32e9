"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var headingRole = {
  abstract: false,
  accessibleNameRequired: true,
  baseConcepts: [],
  childrenPresentational: false,
  nameFrom: ['author', 'contents'],
  prohibitedProps: [],
  props: {
    'aria-level': '2'
  },
  relatedConcepts: [{
    concept: {
      name: 'h1'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'h2'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'h3'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'h4'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'h5'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'h6'
    },
    module: 'HTML'
  }],
  requireContextRole: [],
  requiredContextRole: [],
  requiredOwnedElements: [],
  requiredProps: {
    'aria-level': '2'
  },
  superClass: [['roletype', 'structure', 'sectionhead']]
};
var _default = headingRole;
exports.default = _default;