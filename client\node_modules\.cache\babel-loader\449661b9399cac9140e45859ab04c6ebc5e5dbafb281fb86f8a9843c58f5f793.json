{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import reportWebVitals from'./reportWebVitals';// إعداد متغير البيئة للـ API\nimport{jsx as _jsx}from\"react/jsx-runtime\";if(!process.env.REACT_APP_API_URL){process.env.REACT_APP_API_URL='http://localhost:3001/api';}const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "jsx", "_jsx", "process", "env", "REACT_APP_API_URL", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["D:/برمجة/tste 1/client/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\n// إعداد متغير البيئة للـ API\nif (!process.env.REACT_APP_API_URL) {\n  process.env.REACT_APP_API_URL = 'http://localhost:3001/api';\n}\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAE/C;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,GAAI,CAACC,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAE,CAClCF,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAG,2BAA2B,CAC7D,CAEA,KAAM,CAAAC,IAAI,CAAGR,QAAQ,CAACS,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTR,IAAA,CAACL,KAAK,CAACc,UAAU,EAAAC,QAAA,cACfV,IAAA,CAACH,GAAG,GAAE,CAAC,CACS,CACpB,CAAC,CAED;AACA;AACA;AACAC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}