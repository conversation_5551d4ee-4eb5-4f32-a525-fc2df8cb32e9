import { useState, useEffect, useContext, createContext } from 'react';
import { authService } from '../services/api';
import { saveToLocalStorage, getFromLocalStorage, removeFromLocalStorage } from '../utils/helpers';

// إنشاء Context للمصادقة
const AuthContext = createContext();

// Provider للمصادقة
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // التحقق من وجود token عند تحميل التطبيق
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('authToken');
        const userInfo = getFromLocalStorage('userInfo');
        
        if (token && userInfo) {
          // التحقق من صحة الرمز المميز
          const response = await authService.verifyToken();
          if (response.success) {
            setUser(userInfo);
            setIsAuthenticated(true);
          } else {
            // إزالة البيانات غير الصحيحة
            logout();
          }
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        logout();
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // تسجيل الدخول
  const login = (userData, token) => {
    localStorage.setItem('authToken', token);
    saveToLocalStorage('userInfo', userData);
    setUser(userData);
    setIsAuthenticated(true);
  };

  // تسجيل الخروج
  const logout = () => {
    removeFromLocalStorage('authToken');
    removeFromLocalStorage('userInfo');
    setUser(null);
    setIsAuthenticated(false);
  };

  // تحديث بيانات المستخدم
  const updateUser = (userData) => {
    saveToLocalStorage('userInfo', userData);
    setUser(userData);
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook لاستخدام المصادقة
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook لإرسال OTP
export const useOTP = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [otpSent, setOtpSent] = useState(false);

  const sendOTP = async (phoneNumber, email, language = 'ar') => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await authService.sendOTP(phoneNumber, email, language);
      if (response.success) {
        setOtpSent(true);
        return response;
      } else {
        setError(response.message || 'فشل في إرسال رمز التحقق');
        return null;
      }
    } catch (error) {
      setError(error.message || 'خطأ في الشبكة');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const verifyOTP = async (phoneNumber, otpCode) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await authService.verifyOTP(phoneNumber, otpCode);
      if (response.success) {
        return response;
      } else {
        setError(response.message || 'رمز التحقق غير صحيح');
        return null;
      }
    } catch (error) {
      setError(error.message || 'خطأ في التحقق من الرمز');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const resetOTP = () => {
    setOtpSent(false);
    setError(null);
  };

  return {
    loading,
    error,
    otpSent,
    sendOTP,
    verifyOTP,
    resetOTP,
  };
};

// Hook لتسجيل دخول المالك
export const useOwnerAuth = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { login } = useAuth();

  const ownerLogin = async (password) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await authService.ownerLogin(password);
      if (response.success) {
        login(response.data.owner, response.data.token);
        return response;
      } else {
        setError(response.message || 'كلمة المرور غير صحيحة');
        return null;
      }
    } catch (error) {
      setError(error.message || 'خطأ في تسجيل الدخول');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    ownerLogin,
  };
};

export default useAuth;
