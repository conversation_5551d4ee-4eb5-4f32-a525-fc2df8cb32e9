import axios from 'axios';

// إعداد الـ API base URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// إنشاء instance من axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// إضافة interceptor للطلبات لإضافة token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابات لمعالجة الأخطاء
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // إزالة token منتهي الصلاحية
      localStorage.removeItem('authToken');
      localStorage.removeItem('userInfo');
      window.location.href = '/';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// خدمات المصادقة
export const authService = {
  // إرسال OTP
  sendOTP: (phoneNumber, email, language = 'ar') => {
    return api.post('/auth/send-otp', { phoneNumber, email, language });
  },

  // التحقق من OTP
  verifyOTP: (phoneNumber, otpCode) => {
    return api.post('/auth/verify-otp', { phoneNumber, otpCode });
  },

  // تسجيل دخول المالك
  ownerLogin: (password) => {
    return api.post('/auth/owner-login', { password });
  },

  // التحقق من صحة الرمز المميز
  verifyToken: () => {
    return api.get('/auth/verify-token');
  },
};

// خدمات الشاشات
export const displayService = {
  // الحصول على جميع الشاشات
  getAllDisplays: () => {
    return api.get('/displays');
  },

  // الحصول على شاشة محددة
  getDisplay: (displayId) => {
    return api.get(`/displays/${displayId}`);
  },

  // حجز شاشة
  reserveDisplay: (displayId, customerName) => {
    return api.post(`/displays/${displayId}/reserve`, { customerName });
  },

  // إلغاء حجز الشاشة
  cancelReservation: (displayId) => {
    return api.post(`/displays/${displayId}/cancel-reservation`);
  },

  // تحديث إعدادات الشاشة (للمالك)
  updateDisplay: (displayId, data) => {
    return api.put(`/displays/${displayId}`, data);
  },
};

// خدمات المعاملات
export const transactionService = {
  // إنشاء معاملة جديدة
  createTransaction: (displayId, customerName, amount, duration) => {
    return api.post('/transactions', { displayId, customerName, amount, duration });
  },

  // تأكيد المعاملة بعد الدفع
  confirmTransaction: (transactionId, paymentIntentId) => {
    return api.post(`/transactions/${transactionId}/confirm`, { paymentIntentId });
  },

  // الحصول على معاملات العميل
  getMyTransactions: () => {
    return api.get('/transactions/my-transactions');
  },

  // الحصول على جميع المعاملات (للمالك)
  getAllTransactions: (params = {}) => {
    return api.get('/transactions/all', { params });
  },
};

// خدمات الدفع
export const paymentService = {
  // الحصول على إعدادات الدفع
  getPaymentConfig: () => {
    return api.get('/payment/config');
  },

  // إنشاء Payment Intent
  createPaymentIntent: (amount, transactionId, currency = 'sar') => {
    return api.post('/payment/create-payment-intent', { amount, transactionId, currency });
  },

  // التحقق من حالة الدفع
  getPaymentStatus: (paymentIntentId) => {
    return api.get(`/payment/payment-status/${paymentIntentId}`);
  },

  // محاكاة دفع NFC
  simulateNFCPayment: (transactionId, cardNumber = '****************') => {
    return api.post('/payment/simulate-nfc-payment', { transactionId, cardNumber });
  },
};

// خدمات المالك
export const ownerService = {
  // الحصول على إحصائيات لوحة التحكم
  getDashboardStats: () => {
    return api.get('/owner/dashboard-stats');
  },

  // الحصول على الإعدادات
  getSettings: () => {
    return api.get('/owner/settings');
  },

  // تحديث الإعدادات
  updateSettings: (settings) => {
    return api.put('/owner/settings', { settings });
  },

  // الحصول على تقرير الإيرادات
  getRevenueReport: (params = {}) => {
    return api.get('/owner/revenue-report', { params });
  },

  // الحصول على تقرير استخدام الشاشات
  getDisplayUsageReport: (params = {}) => {
    return api.get('/owner/display-usage-report', { params });
  },

  // إنهاء معاملة يدوياً
  endTransaction: (transactionId, reason) => {
    return api.post(`/owner/end-transaction/${transactionId}`, { reason });
  },
};

// خدمات عامة
export const generalService = {
  // اختبار الاتصال
  ping: () => {
    return api.get('/ping');
  },
};

export default api;
