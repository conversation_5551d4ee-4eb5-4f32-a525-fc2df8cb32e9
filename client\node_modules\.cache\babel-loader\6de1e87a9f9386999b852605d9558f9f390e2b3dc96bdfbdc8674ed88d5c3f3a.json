{"ast": null, "code": "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n  value = typeof XMLHttpRequest !== 'undefined' && 'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n}\nexport const hasCORS = value;", "map": {"version": 3, "names": ["value", "XMLHttpRequest", "err", "hasCORS"], "sources": ["D:/برمجة/tste 1/node_modules/engine.io-client/build/esm/contrib/has-cors.js"], "sourcesContent": ["// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n"], "mappings": "AAAA;AACA,IAAIA,KAAK,GAAG,KAAK;AACjB,IAAI;EACAA,KAAK,GAAG,OAAOC,cAAc,KAAK,WAAW,IACzC,iBAAiB,IAAI,IAAIA,cAAc,CAAC,CAAC;AACjD,CAAC,CACD,OAAOC,GAAG,EAAE;EACR;EACA;AAAA;AAEJ,OAAO,MAAMC,OAAO,GAAGH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}