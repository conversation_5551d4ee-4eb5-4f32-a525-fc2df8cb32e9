{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate,useLocation}from'react-router-dom';import styled from'styled-components';import{displayService}from'../services/api';import{sanitizeText,getFromLocalStorage}from'../utils/helpers';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Container=styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Card=styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n`;const Header=styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;const Title=styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;const DisplayInfo=styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  text-align: center;\n`;const DisplayNumber=styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 10px;\n`;const PriceInfo=styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-bottom: 30px;\n`;const InfoCard=styled.div`\n  background: #e3f2fd;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n`;const InfoLabel=styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 5px;\n`;const InfoValue=styled.div`\n  font-size: 1.3rem;\n  font-weight: bold;\n  color: #1976d2;\n`;const Form=styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;const InputGroup=styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;const Label=styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1.1rem;\n`;const NameInput=styled.input`\n  padding: 20px;\n  border: 3px solid #ddd;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  text-align: center;\n  transition: border-color 0.3s ease;\n  font-weight: 600;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n`;const KeyboardContainer=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\n  gap: 8px;\n  margin-top: 20px;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n`;const KeyButton=styled.button`\n  padding: 12px 8px;\n  border: 2px solid #ddd;\n  background: white;\n  border-radius: 8px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #f8f9fa;\n    border-color: #667eea;\n  }\n\n  &:active {\n    background: #667eea;\n    color: white;\n  }\n`;const LanguageToggle=styled.button`\n  background: #6c757d;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-bottom: 10px;\n  align-self: center;\n\n  &:hover {\n    background: #5a6268;\n  }\n`;const Button=styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props=>props.disabled?0.6:1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;const BackButton=styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;const NameInputPage=()=>{var _location$state,_location$state2;const{t,i18n}=useTranslation();const navigate=useNavigate();const location=useLocation();const[customerName,setCustomerName]=useState('');const[keyboardLanguage,setKeyboardLanguage]=useState('ar');const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[settings,setSettings]=useState({displayPrice:50,displayDuration:300});const isRTL=i18n.language==='ar';const selectedDisplay=(_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.selectedDisplay;const customer=(_location$state2=location.state)===null||_location$state2===void 0?void 0:_location$state2.customer;// لوحة المفاتيح العربية\nconst arabicKeys=[['ض','ص','ث','ق','ف','غ','ع','ه','خ','ح','ج','د'],['ش','س','ي','ب','ل','ا','ت','ن','م','ك','ط'],['ئ','ء','ؤ','ر','لا','ى','ة','و','ز','ظ'],['مسافة','حذف']];// لوحة المفاتيح الإنجليزية\nconst englishKeys=[['Q','W','E','R','T','Y','U','I','O','P'],['A','S','D','F','G','H','J','K','L'],['Z','X','C','V','B','N','M'],['Space','Delete']];useEffect(()=>{// تحميل الإعدادات من localStorage أو استخدام القيم الافتراضية\nconst savedSettings=getFromLocalStorage('appSettings');if(savedSettings){setSettings({displayPrice:savedSettings.display_price||50,displayDuration:savedSettings.display_duration||300});}// التحقق من وجود البيانات المطلوبة\nif(!selectedDisplay||!customer){navigate('/select-display');}},[selectedDisplay,customer,navigate]);const handleKeyPress=key=>{if(key==='مسافة'||key==='Space'){setCustomerName(prev=>prev+' ');}else if(key==='حذف'||key==='Delete'){setCustomerName(prev=>prev.slice(0,-1));}else{setCustomerName(prev=>prev+key);}};const handleSubmit=async e=>{e.preventDefault();const trimmedName=customerName.trim();if(!trimmedName){setError(t('enterName'));return;}if(trimmedName.length<2){setError(isRTL?'الاسم يجب أن يكون حرفين على الأقل':'Name must be at least 2 characters');return;}setLoading(true);setError(null);try{// حجز الشاشة\nconst response=await displayService.reserveDisplay(selectedDisplay.id,sanitizeText(trimmedName));if(response.success){// الانتقال لصفحة الدفع\nnavigate('/payment',{state:{selectedDisplay,customer,customerName:sanitizeText(trimmedName),amount:settings.displayPrice,duration:settings.displayDuration}});}else{setError(response.message||'فشل في حجز الشاشة');}}catch(error){setError('خطأ في الشبكة');console.error('Error reserving display:',error);}finally{setLoading(false);}};const handleBack=()=>{navigate('/select-display');};if(!selectedDisplay||!customer){return null;}return/*#__PURE__*/_jsx(Container,{isRTL:isRTL,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Header,{children:/*#__PURE__*/_jsx(Title,{children:t('enterName')})}),/*#__PURE__*/_jsxs(DisplayInfo,{children:[/*#__PURE__*/_jsx(DisplayNumber,{children:t('displayNumber',{number:selectedDisplay.displayNumber})}),/*#__PURE__*/_jsx(\"div\",{children:selectedDisplay.name})]}),/*#__PURE__*/_jsxs(PriceInfo,{children:[/*#__PURE__*/_jsxs(InfoCard,{children:[/*#__PURE__*/_jsx(InfoLabel,{children:t('displayPrice',{price:''})}),/*#__PURE__*/_jsxs(InfoValue,{children:[settings.displayPrice,\" \",isRTL?'ريال':'SAR']})]}),/*#__PURE__*/_jsxs(InfoCard,{children:[/*#__PURE__*/_jsx(InfoLabel,{children:t('displayDuration',{duration:''})}),/*#__PURE__*/_jsxs(InfoValue,{children:[Math.floor(settings.displayDuration/60),\" \",t('minutes')]})]})]}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(Label,{children:t('customerName')}),/*#__PURE__*/_jsx(NameInput,{type:\"text\",value:customerName,onChange:e=>setCustomerName(e.target.value),placeholder:t('enterName'),maxLength:\"50\"})]}),/*#__PURE__*/_jsx(LanguageToggle,{type:\"button\",onClick:()=>setKeyboardLanguage(prev=>prev==='ar'?'en':'ar'),children:keyboardLanguage==='ar'?'English':'العربية'}),/*#__PURE__*/_jsx(KeyboardContainer,{children:(keyboardLanguage==='ar'?arabicKeys:englishKeys).flat().map((key,index)=>/*#__PURE__*/_jsx(KeyButton,{type:\"button\",onClick:()=>handleKeyPress(key),style:{gridColumn:key==='مسافة'||key==='Space'?'span 6':key==='حذف'||key==='Delete'?'span 3':'span 1'},children:key},index))}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading||!customerName.trim(),children:loading?t('processing'):t('proceedToPayment')})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'20px',textAlign:'center'},children:/*#__PURE__*/_jsx(BackButton,{onClick:handleBack,children:t('back')})})]})});};export default NameInputPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useLocation", "styled", "displayService", "sanitizeText", "getFromLocalStorage", "jsx", "_jsx", "jsxs", "_jsxs", "Container", "div", "props", "isRTL", "Card", "Header", "Title", "h1", "DisplayInfo", "DisplayNumber", "PriceInfo", "InfoCard", "InfoLabel", "InfoValue", "Form", "form", "InputGroup", "Label", "label", "NameInput", "input", "KeyboardContainer", "KeyButton", "button", "LanguageToggle", "<PERSON><PERSON>", "disabled", "BackButton", "ErrorMessage", "NameInputPage", "_location$state", "_location$state2", "t", "i18n", "navigate", "location", "customerName", "setCustomerName", "keyboardLanguage", "setKeyboardLanguage", "loading", "setLoading", "error", "setError", "settings", "setSettings", "displayPrice", "displayDuration", "language", "selectedDisplay", "state", "customer", "arabicKeys", "english<PERSON>eys", "savedSettings", "display_price", "display_duration", "handleKeyPress", "key", "prev", "slice", "handleSubmit", "e", "preventDefault", "trimmedName", "trim", "length", "response", "reserveDisplay", "id", "success", "amount", "duration", "message", "console", "handleBack", "children", "number", "displayNumber", "name", "price", "Math", "floor", "onSubmit", "type", "value", "onChange", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "onClick", "flat", "map", "index", "style", "gridColumn", "marginTop", "textAlign"], "sources": ["D:/برمجة/tste 1/client/src/pages/NameInputPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { displayService } from '../services/api';\nimport { sanitizeText, getFromLocalStorage } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst DisplayInfo = styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  text-align: center;\n`;\n\nconst DisplayNumber = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 10px;\n`;\n\nconst PriceInfo = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n\nconst InfoCard = styled.div`\n  background: #e3f2fd;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n`;\n\nconst InfoLabel = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 5px;\n`;\n\nconst InfoValue = styled.div`\n  font-size: 1.3rem;\n  font-weight: bold;\n  color: #1976d2;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1.1rem;\n`;\n\nconst NameInput = styled.input`\n  padding: 20px;\n  border: 3px solid #ddd;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  text-align: center;\n  transition: border-color 0.3s ease;\n  font-weight: 600;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n`;\n\nconst KeyboardContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\n  gap: 8px;\n  margin-top: 20px;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst KeyButton = styled.button`\n  padding: 12px 8px;\n  border: 2px solid #ddd;\n  background: white;\n  border-radius: 8px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #f8f9fa;\n    border-color: #667eea;\n  }\n\n  &:active {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst LanguageToggle = styled.button`\n  background: #6c757d;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-bottom: 10px;\n  align-self: center;\n\n  &:hover {\n    background: #5a6268;\n  }\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst NameInputPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const [customerName, setCustomerName] = useState('');\n  const [keyboardLanguage, setKeyboardLanguage] = useState('ar');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [settings, setSettings] = useState({\n    displayPrice: 50,\n    displayDuration: 300\n  });\n\n  const isRTL = i18n.language === 'ar';\n  const selectedDisplay = location.state?.selectedDisplay;\n  const customer = location.state?.customer;\n\n  // لوحة المفاتيح العربية\n  const arabicKeys = [\n    ['ض', 'ص', 'ث', 'ق', 'ف', 'غ', 'ع', 'ه', 'خ', 'ح', 'ج', 'د'],\n    ['ش', 'س', 'ي', 'ب', 'ل', 'ا', 'ت', 'ن', 'م', 'ك', 'ط'],\n    ['ئ', 'ء', 'ؤ', 'ر', 'لا', 'ى', 'ة', 'و', 'ز', 'ظ'],\n    ['مسافة', 'حذف']\n  ];\n\n  // لوحة المفاتيح الإنجليزية\n  const englishKeys = [\n    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],\n    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],\n    ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],\n    ['Space', 'Delete']\n  ];\n\n  useEffect(() => {\n    // تحميل الإعدادات من localStorage أو استخدام القيم الافتراضية\n    const savedSettings = getFromLocalStorage('appSettings');\n    if (savedSettings) {\n      setSettings({\n        displayPrice: savedSettings.display_price || 50,\n        displayDuration: savedSettings.display_duration || 300\n      });\n    }\n\n    // التحقق من وجود البيانات المطلوبة\n    if (!selectedDisplay || !customer) {\n      navigate('/select-display');\n    }\n  }, [selectedDisplay, customer, navigate]);\n\n  const handleKeyPress = (key) => {\n    if (key === 'مسافة' || key === 'Space') {\n      setCustomerName(prev => prev + ' ');\n    } else if (key === 'حذف' || key === 'Delete') {\n      setCustomerName(prev => prev.slice(0, -1));\n    } else {\n      setCustomerName(prev => prev + key);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const trimmedName = customerName.trim();\n    if (!trimmedName) {\n      setError(t('enterName'));\n      return;\n    }\n\n    if (trimmedName.length < 2) {\n      setError(isRTL ? 'الاسم يجب أن يكون حرفين على الأقل' : 'Name must be at least 2 characters');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      // حجز الشاشة\n      const response = await displayService.reserveDisplay(selectedDisplay.id, sanitizeText(trimmedName));\n      \n      if (response.success) {\n        // الانتقال لصفحة الدفع\n        navigate('/payment', {\n          state: {\n            selectedDisplay,\n            customer,\n            customerName: sanitizeText(trimmedName),\n            amount: settings.displayPrice,\n            duration: settings.displayDuration\n          }\n        });\n      } else {\n        setError(response.message || 'فشل في حجز الشاشة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error reserving display:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/select-display');\n  };\n\n  if (!selectedDisplay || !customer) {\n    return null;\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Header>\n          <Title>{t('enterName')}</Title>\n        </Header>\n\n        <DisplayInfo>\n          <DisplayNumber>\n            {t('displayNumber', { number: selectedDisplay.displayNumber })}\n          </DisplayNumber>\n          <div>{selectedDisplay.name}</div>\n        </DisplayInfo>\n\n        <PriceInfo>\n          <InfoCard>\n            <InfoLabel>{t('displayPrice', { price: '' })}</InfoLabel>\n            <InfoValue>{settings.displayPrice} {isRTL ? 'ريال' : 'SAR'}</InfoValue>\n          </InfoCard>\n          <InfoCard>\n            <InfoLabel>{t('displayDuration', { duration: '' })}</InfoLabel>\n            <InfoValue>{Math.floor(settings.displayDuration / 60)} {t('minutes')}</InfoValue>\n          </InfoCard>\n        </PriceInfo>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <Label>{t('customerName')}</Label>\n            <NameInput\n              type=\"text\"\n              value={customerName}\n              onChange={(e) => setCustomerName(e.target.value)}\n              placeholder={t('enterName')}\n              maxLength=\"50\"\n            />\n          </InputGroup>\n\n          <LanguageToggle\n            type=\"button\"\n            onClick={() => setKeyboardLanguage(prev => prev === 'ar' ? 'en' : 'ar')}\n          >\n            {keyboardLanguage === 'ar' ? 'English' : 'العربية'}\n          </LanguageToggle>\n\n          <KeyboardContainer>\n            {(keyboardLanguage === 'ar' ? arabicKeys : englishKeys).flat().map((key, index) => (\n              <KeyButton\n                key={index}\n                type=\"button\"\n                onClick={() => handleKeyPress(key)}\n                style={{\n                  gridColumn: (key === 'مسافة' || key === 'Space') ? 'span 6' : \n                             (key === 'حذف' || key === 'Delete') ? 'span 3' : 'span 1'\n                }}\n              >\n                {key}\n              </KeyButton>\n            ))}\n          </KeyboardContainer>\n\n          <Button type=\"submit\" disabled={loading || !customerName.trim()}>\n            {loading ? t('processing') : t('proceedToPayment')}\n          </Button>\n        </Form>\n\n        <div style={{ marginTop: '20px', textAlign: 'center' }}>\n          <BackButton onClick={handleBack}>\n            {t('back')}\n          </BackButton>\n        </div>\n      </Card>\n    </Container>\n  );\n};\n\nexport default NameInputPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,cAAc,KAAQ,iBAAiB,CAChD,OAASC,YAAY,CAAEC,mBAAmB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErE,KAAM,CAAAC,SAAS,CAAGR,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGZ,MAAM,CAACS,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,MAAM,CAAGb,MAAM,CAACS,GAAG;AACzB;AACA;AACA,CAAC,CAED,KAAM,CAAAK,KAAK,CAAGd,MAAM,CAACe,EAAE;AACvB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGhB,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,aAAa,CAAGjB,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,SAAS,CAAGlB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAU,QAAQ,CAAGnB,MAAM,CAACS,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,SAAS,CAAGpB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,SAAS,CAAGrB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,IAAI,CAAGtB,MAAM,CAACuB,IAAI;AACxB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGxB,MAAM,CAACS,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAgB,KAAK,CAAGzB,MAAM,CAAC0B,KAAK;AAC1B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG3B,MAAM,CAAC4B,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAG7B,MAAM,CAACS,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAqB,SAAS,CAAG9B,MAAM,CAAC+B,MAAM;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGhC,MAAM,CAAC+B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,MAAM,CAAGjC,MAAM,CAAC+B,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAarB,KAAK,EAAIA,KAAK,CAACwB,QAAQ,CAAG,GAAG,CAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGnC,MAAM,CAAC+B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAK,YAAY,CAAGpC,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAA4B,aAAa,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,gBAAA,CAC1B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG5C,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA6C,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6C,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACmD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuD,KAAK,CAAEC,QAAQ,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACyD,QAAQ,CAAEC,WAAW,CAAC,CAAG1D,QAAQ,CAAC,CACvC2D,YAAY,CAAE,EAAE,CAChBC,eAAe,CAAE,GACnB,CAAC,CAAC,CAEF,KAAM,CAAA5C,KAAK,CAAG8B,IAAI,CAACe,QAAQ,GAAK,IAAI,CACpC,KAAM,CAAAC,eAAe,EAAAnB,eAAA,CAAGK,QAAQ,CAACe,KAAK,UAAApB,eAAA,iBAAdA,eAAA,CAAgBmB,eAAe,CACvD,KAAM,CAAAE,QAAQ,EAAApB,gBAAA,CAAGI,QAAQ,CAACe,KAAK,UAAAnB,gBAAA,iBAAdA,gBAAA,CAAgBoB,QAAQ,CAEzC;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC5D,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACvD,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACnD,CAAC,OAAO,CAAE,KAAK,CAAC,CACjB,CAED;AACA,KAAM,CAAAC,WAAW,CAAG,CAClB,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAClD,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC7C,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACnC,CAAC,OAAO,CAAE,QAAQ,CAAC,CACpB,CAEDjE,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAkE,aAAa,CAAG3D,mBAAmB,CAAC,aAAa,CAAC,CACxD,GAAI2D,aAAa,CAAE,CACjBT,WAAW,CAAC,CACVC,YAAY,CAAEQ,aAAa,CAACC,aAAa,EAAI,EAAE,CAC/CR,eAAe,CAAEO,aAAa,CAACE,gBAAgB,EAAI,GACrD,CAAC,CAAC,CACJ,CAEA;AACA,GAAI,CAACP,eAAe,EAAI,CAACE,QAAQ,CAAE,CACjCjB,QAAQ,CAAC,iBAAiB,CAAC,CAC7B,CACF,CAAC,CAAE,CAACe,eAAe,CAAEE,QAAQ,CAAEjB,QAAQ,CAAC,CAAC,CAEzC,KAAM,CAAAuB,cAAc,CAAIC,GAAG,EAAK,CAC9B,GAAIA,GAAG,GAAK,OAAO,EAAIA,GAAG,GAAK,OAAO,CAAE,CACtCrB,eAAe,CAACsB,IAAI,EAAIA,IAAI,CAAG,GAAG,CAAC,CACrC,CAAC,IAAM,IAAID,GAAG,GAAK,KAAK,EAAIA,GAAG,GAAK,QAAQ,CAAE,CAC5CrB,eAAe,CAACsB,IAAI,EAAIA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,CAC5C,CAAC,IAAM,CACLvB,eAAe,CAACsB,IAAI,EAAIA,IAAI,CAAGD,GAAG,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,KAAM,CAAAC,WAAW,CAAG5B,YAAY,CAAC6B,IAAI,CAAC,CAAC,CACvC,GAAI,CAACD,WAAW,CAAE,CAChBrB,QAAQ,CAACX,CAAC,CAAC,WAAW,CAAC,CAAC,CACxB,OACF,CAEA,GAAIgC,WAAW,CAACE,MAAM,CAAG,CAAC,CAAE,CAC1BvB,QAAQ,CAACxC,KAAK,CAAG,mCAAmC,CAAG,oCAAoC,CAAC,CAC5F,OACF,CAEAsC,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF;AACA,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAA1E,cAAc,CAAC2E,cAAc,CAACnB,eAAe,CAACoB,EAAE,CAAE3E,YAAY,CAACsE,WAAW,CAAC,CAAC,CAEnG,GAAIG,QAAQ,CAACG,OAAO,CAAE,CACpB;AACApC,QAAQ,CAAC,UAAU,CAAE,CACnBgB,KAAK,CAAE,CACLD,eAAe,CACfE,QAAQ,CACRf,YAAY,CAAE1C,YAAY,CAACsE,WAAW,CAAC,CACvCO,MAAM,CAAE3B,QAAQ,CAACE,YAAY,CAC7B0B,QAAQ,CAAE5B,QAAQ,CAACG,eACrB,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLJ,QAAQ,CAACwB,QAAQ,CAACM,OAAO,EAAI,mBAAmB,CAAC,CACnD,CACF,CAAE,MAAO/B,KAAK,CAAE,CACdC,QAAQ,CAAC,eAAe,CAAC,CACzB+B,OAAO,CAAChC,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkC,UAAU,CAAGA,CAAA,GAAM,CACvBzC,QAAQ,CAAC,iBAAiB,CAAC,CAC7B,CAAC,CAED,GAAI,CAACe,eAAe,EAAI,CAACE,QAAQ,CAAE,CACjC,MAAO,KAAI,CACb,CAEA,mBACEtD,IAAA,CAACG,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAAyE,QAAA,cACtB7E,KAAA,CAACK,IAAI,EAAAwE,QAAA,eACH/E,IAAA,CAACQ,MAAM,EAAAuE,QAAA,cACL/E,IAAA,CAACS,KAAK,EAAAsE,QAAA,CAAE5C,CAAC,CAAC,WAAW,CAAC,CAAQ,CAAC,CACzB,CAAC,cAETjC,KAAA,CAACS,WAAW,EAAAoE,QAAA,eACV/E,IAAA,CAACY,aAAa,EAAAmE,QAAA,CACX5C,CAAC,CAAC,eAAe,CAAE,CAAE6C,MAAM,CAAE5B,eAAe,CAAC6B,aAAc,CAAC,CAAC,CACjD,CAAC,cAChBjF,IAAA,QAAA+E,QAAA,CAAM3B,eAAe,CAAC8B,IAAI,CAAM,CAAC,EACtB,CAAC,cAEdhF,KAAA,CAACW,SAAS,EAAAkE,QAAA,eACR7E,KAAA,CAACY,QAAQ,EAAAiE,QAAA,eACP/E,IAAA,CAACe,SAAS,EAAAgE,QAAA,CAAE5C,CAAC,CAAC,cAAc,CAAE,CAAEgD,KAAK,CAAE,EAAG,CAAC,CAAC,CAAY,CAAC,cACzDjF,KAAA,CAACc,SAAS,EAAA+D,QAAA,EAAEhC,QAAQ,CAACE,YAAY,CAAC,GAAC,CAAC3C,KAAK,CAAG,MAAM,CAAG,KAAK,EAAY,CAAC,EAC/D,CAAC,cACXJ,KAAA,CAACY,QAAQ,EAAAiE,QAAA,eACP/E,IAAA,CAACe,SAAS,EAAAgE,QAAA,CAAE5C,CAAC,CAAC,iBAAiB,CAAE,CAAEwC,QAAQ,CAAE,EAAG,CAAC,CAAC,CAAY,CAAC,cAC/DzE,KAAA,CAACc,SAAS,EAAA+D,QAAA,EAAEK,IAAI,CAACC,KAAK,CAACtC,QAAQ,CAACG,eAAe,CAAG,EAAE,CAAC,CAAC,GAAC,CAACf,CAAC,CAAC,SAAS,CAAC,EAAY,CAAC,EACzE,CAAC,EACF,CAAC,CAEXU,KAAK,eAAI7C,IAAA,CAAC+B,YAAY,EAAAgD,QAAA,CAAElC,KAAK,CAAe,CAAC,cAE9C3C,KAAA,CAACe,IAAI,EAACqE,QAAQ,CAAEtB,YAAa,CAAAe,QAAA,eAC3B7E,KAAA,CAACiB,UAAU,EAAA4D,QAAA,eACT/E,IAAA,CAACoB,KAAK,EAAA2D,QAAA,CAAE5C,CAAC,CAAC,cAAc,CAAC,CAAQ,CAAC,cAClCnC,IAAA,CAACsB,SAAS,EACRiE,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEjD,YAAa,CACpBkD,QAAQ,CAAGxB,CAAC,EAAKzB,eAAe,CAACyB,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE,CACjDG,WAAW,CAAExD,CAAC,CAAC,WAAW,CAAE,CAC5ByD,SAAS,CAAC,IAAI,CACf,CAAC,EACQ,CAAC,cAEb5F,IAAA,CAAC2B,cAAc,EACb4D,IAAI,CAAC,QAAQ,CACbM,OAAO,CAAEA,CAAA,GAAMnD,mBAAmB,CAACoB,IAAI,EAAIA,IAAI,GAAK,IAAI,CAAG,IAAI,CAAG,IAAI,CAAE,CAAAiB,QAAA,CAEvEtC,gBAAgB,GAAK,IAAI,CAAG,SAAS,CAAG,SAAS,CACpC,CAAC,cAEjBzC,IAAA,CAACwB,iBAAiB,EAAAuD,QAAA,CACf,CAACtC,gBAAgB,GAAK,IAAI,CAAGc,UAAU,CAAGC,WAAW,EAAEsC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,CAAClC,GAAG,CAAEmC,KAAK,gBAC5EhG,IAAA,CAACyB,SAAS,EAER8D,IAAI,CAAC,QAAQ,CACbM,OAAO,CAAEA,CAAA,GAAMjC,cAAc,CAACC,GAAG,CAAE,CACnCoC,KAAK,CAAE,CACLC,UAAU,CAAGrC,GAAG,GAAK,OAAO,EAAIA,GAAG,GAAK,OAAO,CAAI,QAAQ,CAC/CA,GAAG,GAAK,KAAK,EAAIA,GAAG,GAAK,QAAQ,CAAI,QAAQ,CAAG,QAC9D,CAAE,CAAAkB,QAAA,CAEDlB,GAAG,EARCmC,KASI,CACZ,CAAC,CACe,CAAC,cAEpBhG,IAAA,CAAC4B,MAAM,EAAC2D,IAAI,CAAC,QAAQ,CAAC1D,QAAQ,CAAEc,OAAO,EAAI,CAACJ,YAAY,CAAC6B,IAAI,CAAC,CAAE,CAAAW,QAAA,CAC7DpC,OAAO,CAAGR,CAAC,CAAC,YAAY,CAAC,CAAGA,CAAC,CAAC,kBAAkB,CAAC,CAC5C,CAAC,EACL,CAAC,cAEPnC,IAAA,QAAKiG,KAAK,CAAE,CAAEE,SAAS,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAArB,QAAA,cACrD/E,IAAA,CAAC8B,UAAU,EAAC+D,OAAO,CAAEf,UAAW,CAAAC,QAAA,CAC7B5C,CAAC,CAAC,MAAM,CAAC,CACA,CAAC,CACV,CAAC,EACF,CAAC,CACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}