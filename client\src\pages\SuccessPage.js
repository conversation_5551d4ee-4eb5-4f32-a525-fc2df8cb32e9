import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import styled, { keyframes } from 'styled-components';
import { formatCurrency, formatDate, copyToClipboard } from '../utils/helpers';

const checkmark = keyframes`
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const Card = styled.div`
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  text-align: center;
  animation: ${fadeIn} 0.6s ease-out;
`;

const SuccessIcon = styled.div`
  width: 120px;
  height: 120px;
  margin: 0 auto 30px;
  position: relative;
`;

const CheckmarkSVG = styled.svg`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #28a745;
  stroke-miterlimit: 10;
  box-shadow: inset 0px 0px 0px #28a745;
  animation: ${checkmark} 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
`;

const CheckmarkPath = styled.path`
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: #28a745;
  margin-bottom: 20px;
  font-weight: 700;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 30px;
`;

const TransactionDetails = styled.div`
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
  text-align: ${props => props.isRTL ? 'right' : 'left'};
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;

  &:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
`;

const DetailLabel = styled.span`
  font-weight: 600;
  color: #333;
`;

const DetailValue = styled.span`
  color: #666;
  font-weight: 500;
`;

const TransactionNumber = styled.div`
  background: #e3f2fd;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background: #bbdefb;
  }
`;

const DisplayPreview = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
`;

const DisplayName = styled.div`
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
`;

const DisplayInfo = styled.div`
  font-size: 1rem;
  opacity: 0.9;
`;

const CountdownTimer = styled.div`
  background: #fff3cd;
  color: #856404;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
`;

const Button = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }
`;

const SecondaryButton = styled(Button)`
  background: #6c757d;
  
  &:hover {
    background: #5a6268;
    box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
  }
`;

const SuccessPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [countdown, setCountdown] = useState(30);
  const [copied, setCopied] = useState(false);

  const isRTL = i18n.language === 'ar';
  const { transaction, customerName, selectedDisplay } = location.state || {};

  useEffect(() => {
    // التحقق من وجود البيانات المطلوبة
    if (!transaction || !customerName || !selectedDisplay) {
      navigate('/');
      return;
    }

    // العد التنازلي للعودة للصفحة الرئيسية
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          navigate('/');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [transaction, customerName, selectedDisplay, navigate]);

  const handleCopyTransactionNumber = async () => {
    if (transaction?.transactionNumber) {
      const success = await copyToClipboard(transaction.transactionNumber);
      if (success) {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    }
  };

  const handleNewTransaction = () => {
    navigate('/select-display');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  if (!transaction || !customerName || !selectedDisplay) {
    return null;
  }

  return (
    <Container isRTL={isRTL}>
      <Card>
        <SuccessIcon>
          <CheckmarkSVG viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="#28a745"
              strokeWidth="3"
            />
            <CheckmarkPath
              fill="none"
              d="M25,50 L40,65 L75,30"
            />
          </CheckmarkSVG>
        </SuccessIcon>

        <Title>{t('paymentSuccessful')}</Title>
        <Subtitle>
          {isRTL ? 'تم تأكيد معاملتك بنجاح' : 'Your transaction has been confirmed successfully'}
        </Subtitle>

        <TransactionNumber onClick={handleCopyTransactionNumber}>
          {t('transactionNumber')}: {transaction.transactionNumber}
          {copied && (
            <div style={{ fontSize: '0.9rem', marginTop: '5px', color: '#28a745' }}>
              {isRTL ? 'تم النسخ!' : 'Copied!'}
            </div>
          )}
        </TransactionNumber>

        <TransactionDetails isRTL={isRTL}>
          <DetailRow>
            <DetailLabel>{t('customerName')}:</DetailLabel>
            <DetailValue>{customerName}</DetailValue>
          </DetailRow>
          <DetailRow>
            <DetailLabel>{t('displayNumber', { number: selectedDisplay.displayNumber })}:</DetailLabel>
            <DetailValue>{selectedDisplay.name}</DetailValue>
          </DetailRow>
          <DetailRow>
            <DetailLabel>{t('amount')}:</DetailLabel>
            <DetailValue>{formatCurrency(transaction.amount || 50, 'SAR', isRTL ? 'ar-SA' : 'en-US')}</DetailValue>
          </DetailRow>
          <DetailRow>
            <DetailLabel>{t('duration')}:</DetailLabel>
            <DetailValue>{Math.floor((transaction.duration || 300) / 60)} {t('minutes')}</DetailValue>
          </DetailRow>
          <DetailRow>
            <DetailLabel>{t('startTime')}:</DetailLabel>
            <DetailValue>{formatDate(transaction.startTime || new Date(), isRTL ? 'ar-SA' : 'en-US')}</DetailValue>
          </DetailRow>
        </TransactionDetails>

        <DisplayPreview>
          <DisplayName>{customerName}</DisplayName>
          <DisplayInfo>
            {isRTL ? 'يتم عرض اسمك الآن على' : 'Your name is now displayed on'} {selectedDisplay.name}
          </DisplayInfo>
        </DisplayPreview>

        <CountdownTimer>
          {isRTL ? 
            `سيتم توجيهك للصفحة الرئيسية خلال ${countdown} ثانية` :
            `Redirecting to home page in ${countdown} seconds`
          }
        </CountdownTimer>

        <ButtonGroup>
          <Button onClick={handleNewTransaction}>
            {isRTL ? 'معاملة جديدة' : 'New Transaction'}
          </Button>
          <SecondaryButton onClick={handleGoHome}>
            {isRTL ? 'الصفحة الرئيسية' : 'Home Page'}
          </SecondaryButton>
        </ButtonGroup>
      </Card>
    </Container>
  );
};

export default SuccessPage;
