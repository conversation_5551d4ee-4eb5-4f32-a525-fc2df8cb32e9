{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate}from'react-router-dom';import styled from'styled-components';import{displayService}from'../services/api';import{formatTimeRemaining}from'../utils/helpers';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Container=styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  direction: ${props=>props.isRTL?'rtl':'ltr'};\n`;const Header=styled.div`\n  text-align: center;\n  color: white;\n  margin-bottom: 30px;\n`;const Title=styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;const Subtitle=styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;const DisplayGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n`;const DisplayCard=styled.div`\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  cursor: ${props=>props.available?'pointer':'not-allowed'};\n  opacity: ${props=>props.available?1:0.6};\n  transition: all 0.3s ease;\n  border: 3px solid ${props=>{switch(props.status){case'available':return'#28a745';case'occupied':return'#dc3545';case'reserved':return'#ffc107';case'maintenance':return'#6c757d';default:return'#ddd';}}};\n\n  &:hover {\n    transform: ${props=>props.available?'translateY(-5px)':'none'};\n    box-shadow: ${props=>props.available?'0 15px 40px rgba(0, 0, 0, 0.15)':'0 10px 30px rgba(0, 0, 0, 0.1)'};\n  }\n`;const DisplayNumber=styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  color: #333;\n  text-align: center;\n  margin-bottom: 15px;\n`;const DisplayName=styled.h3`\n  font-size: 1.3rem;\n  color: #333;\n  text-align: center;\n  margin-bottom: 15px;\n`;const StatusBadge=styled.div`\n  display: inline-block;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-align: center;\n  margin-bottom: 15px;\n  width: 100%;\n  color: white;\n  background: ${props=>{switch(props.status){case'available':return'#28a745';case'occupied':return'#dc3545';case'reserved':return'#ffc107';case'maintenance':return'#6c757d';default:return'#ddd';}}};\n`;const CustomerInfo=styled.div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 10px;\n  margin-top: 15px;\n`;const CustomerName=styled.div`\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n`;const TimeRemaining=styled.div`\n  color: #dc3545;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;const BackButton=styled.button`\n  position: fixed;\n  top: 20px;\n  ${props=>props.isRTL?'right: 20px':'left: 20px'};\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: white;\n    color: #667eea;\n  }\n`;const LoadingSpinner=styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: white;\n  font-size: 1.2rem;\n`;const ErrorMessage=styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n  margin: 20px auto;\n  max-width: 500px;\n`;const DisplaySelectionPage=()=>{const{t,i18n}=useTranslation();const navigate=useNavigate();const[displays,setDisplays]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const isRTL=i18n.language==='ar';useEffect(()=>{fetchDisplays();// تحديث البيانات كل 30 ثانية\nconst interval=setInterval(fetchDisplays,30000);return()=>clearInterval(interval);},[]);const fetchDisplays=async()=>{try{const response=await displayService.getAllDisplays();if(response.success){setDisplays(response.data);setError(null);}else{setError(response.message||'فشل في تحميل الشاشات');}}catch(error){setError('خطأ في الشبكة');console.error('Error fetching displays:',error);}finally{setLoading(false);}};const handleDisplaySelect=display=>{if(display.status==='available'){navigate('/login',{state:{selectedDisplay:display}});}};const handleBack=()=>{navigate('/');};const getStatusText=status=>{switch(status){case'available':return t('available');case'occupied':return t('occupied');case'reserved':return t('reserved');case'maintenance':return t('maintenance');default:return status;}};if(loading){return/*#__PURE__*/_jsx(Container,{isRTL:isRTL,children:/*#__PURE__*/_jsx(LoadingSpinner,{children:isRTL?'جاري التحميل...':'Loading...'})});}return/*#__PURE__*/_jsxs(Container,{isRTL:isRTL,children:[/*#__PURE__*/_jsx(BackButton,{isRTL:isRTL,onClick:handleBack,children:t('back')}),/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Title,{children:t('selectDisplay')}),/*#__PURE__*/_jsx(Subtitle,{children:isRTL?'اختر الشاشة التي تريد عرض اسمك عليها':'Choose the display to show your name'})]}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),/*#__PURE__*/_jsx(DisplayGrid,{children:displays.map(display=>/*#__PURE__*/_jsxs(DisplayCard,{available:display.status==='available',status:display.status,onClick:()=>handleDisplaySelect(display),children:[/*#__PURE__*/_jsx(DisplayNumber,{children:display.displayNumber}),/*#__PURE__*/_jsx(DisplayName,{children:display.name}),/*#__PURE__*/_jsx(StatusBadge,{status:display.status,children:getStatusText(display.status)}),display.status==='occupied'&&display.customerName&&/*#__PURE__*/_jsxs(CustomerInfo,{children:[/*#__PURE__*/_jsxs(CustomerName,{children:[isRTL?'العميل: ':'Customer: ',display.customerName]}),display.timeRemaining&&!display.timeRemaining.expired&&/*#__PURE__*/_jsx(TimeRemaining,{children:t('timeRemaining',{time:formatTimeRemaining(display.timeRemaining.remaining)})})]}),display.status==='reserved'&&display.customerName&&/*#__PURE__*/_jsx(CustomerInfo,{children:/*#__PURE__*/_jsxs(CustomerName,{children:[isRTL?'محجوز لـ: ':'Reserved for: ',display.customerName]})})]},display.id))})]});};export default DisplaySelectionPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "styled", "displayService", "formatTimeRemaining", "jsx", "_jsx", "jsxs", "_jsxs", "Container", "div", "props", "isRTL", "Header", "Title", "h1", "Subtitle", "p", "DisplayGrid", "DisplayCard", "available", "status", "DisplayNumber", "DisplayName", "h3", "StatusBadge", "CustomerInfo", "CustomerName", "TimeRemaining", "BackButton", "button", "LoadingSpinner", "ErrorMessage", "DisplaySelectionPage", "t", "i18n", "navigate", "displays", "setDisplays", "loading", "setLoading", "error", "setError", "language", "fetchDisplays", "interval", "setInterval", "clearInterval", "response", "getAllDisplays", "success", "data", "message", "console", "handleDisplaySelect", "display", "state", "selectedDisplay", "handleBack", "getStatusText", "children", "onClick", "map", "displayNumber", "name", "customerName", "timeRemaining", "expired", "time", "remaining", "id"], "sources": ["D:/برمجة/tste 1/client/src/pages/DisplaySelectionPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { displayService } from '../services/api';\nimport { formatTimeRemaining } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  color: white;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n  font-weight: 700;\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n\nconst DisplayGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst DisplayCard = styled.div`\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  cursor: ${props => props.available ? 'pointer' : 'not-allowed'};\n  opacity: ${props => props.available ? 1 : 0.6};\n  transition: all 0.3s ease;\n  border: 3px solid ${props => {\n    switch (props.status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  }};\n\n  &:hover {\n    transform: ${props => props.available ? 'translateY(-5px)' : 'none'};\n    box-shadow: ${props => props.available ? '0 15px 40px rgba(0, 0, 0, 0.15)' : '0 10px 30px rgba(0, 0, 0, 0.1)'};\n  }\n`;\n\nconst DisplayNumber = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  color: #333;\n  text-align: center;\n  margin-bottom: 15px;\n`;\n\nconst DisplayName = styled.h3`\n  font-size: 1.3rem;\n  color: #333;\n  text-align: center;\n  margin-bottom: 15px;\n`;\n\nconst StatusBadge = styled.div`\n  display: inline-block;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-align: center;\n  margin-bottom: 15px;\n  width: 100%;\n  color: white;\n  background: ${props => {\n    switch (props.status) {\n      case 'available': return '#28a745';\n      case 'occupied': return '#dc3545';\n      case 'reserved': return '#ffc107';\n      case 'maintenance': return '#6c757d';\n      default: return '#ddd';\n    }\n  }};\n`;\n\nconst CustomerInfo = styled.div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 10px;\n  margin-top: 15px;\n`;\n\nconst CustomerName = styled.div`\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n`;\n\nconst TimeRemaining = styled.div`\n  color: #dc3545;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n\nconst BackButton = styled.button`\n  position: fixed;\n  top: 20px;\n  ${props => props.isRTL ? 'right: 20px' : 'left: 20px'};\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: 2px solid white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: white;\n    color: #667eea;\n  }\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: white;\n  font-size: 1.2rem;\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n  margin: 20px auto;\n  max-width: 500px;\n`;\n\nconst DisplaySelectionPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const [displays, setDisplays] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  const isRTL = i18n.language === 'ar';\n\n  useEffect(() => {\n    fetchDisplays();\n    \n    // تحديث البيانات كل 30 ثانية\n    const interval = setInterval(fetchDisplays, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchDisplays = async () => {\n    try {\n      const response = await displayService.getAllDisplays();\n      if (response.success) {\n        setDisplays(response.data);\n        setError(null);\n      } else {\n        setError(response.message || 'فشل في تحميل الشاشات');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error fetching displays:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDisplaySelect = (display) => {\n    if (display.status === 'available') {\n      navigate('/login', { \n        state: { \n          selectedDisplay: display \n        } \n      });\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/');\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'available': return t('available');\n      case 'occupied': return t('occupied');\n      case 'reserved': return t('reserved');\n      case 'maintenance': return t('maintenance');\n      default: return status;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Container isRTL={isRTL}>\n        <LoadingSpinner>\n          {isRTL ? 'جاري التحميل...' : 'Loading...'}\n        </LoadingSpinner>\n      </Container>\n    );\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <BackButton isRTL={isRTL} onClick={handleBack}>\n        {t('back')}\n      </BackButton>\n\n      <Header>\n        <Title>{t('selectDisplay')}</Title>\n        <Subtitle>\n          {isRTL ? 'اختر الشاشة التي تريد عرض اسمك عليها' : 'Choose the display to show your name'}\n        </Subtitle>\n      </Header>\n\n      {error && (\n        <ErrorMessage>\n          {error}\n        </ErrorMessage>\n      )}\n\n      <DisplayGrid>\n        {displays.map((display) => (\n          <DisplayCard\n            key={display.id}\n            available={display.status === 'available'}\n            status={display.status}\n            onClick={() => handleDisplaySelect(display)}\n          >\n            <DisplayNumber>\n              {display.displayNumber}\n            </DisplayNumber>\n            \n            <DisplayName>\n              {display.name}\n            </DisplayName>\n            \n            <StatusBadge status={display.status}>\n              {getStatusText(display.status)}\n            </StatusBadge>\n\n            {display.status === 'occupied' && display.customerName && (\n              <CustomerInfo>\n                <CustomerName>\n                  {isRTL ? 'العميل: ' : 'Customer: '}{display.customerName}\n                </CustomerName>\n                {display.timeRemaining && !display.timeRemaining.expired && (\n                  <TimeRemaining>\n                    {t('timeRemaining', { \n                      time: formatTimeRemaining(display.timeRemaining.remaining) \n                    })}\n                  </TimeRemaining>\n                )}\n              </CustomerInfo>\n            )}\n\n            {display.status === 'reserved' && display.customerName && (\n              <CustomerInfo>\n                <CustomerName>\n                  {isRTL ? 'محجوز لـ: ' : 'Reserved for: '}{display.customerName}\n                </CustomerName>\n              </CustomerInfo>\n            )}\n          </DisplayCard>\n        ))}\n      </DisplayGrid>\n    </Container>\n  );\n};\n\nexport default DisplaySelectionPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,cAAc,KAAQ,iBAAiB,CAChD,OAASC,mBAAmB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,SAAS,CAAGP,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA,eAAeC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,KAAK,CAAG,KAAK;AACnD,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGX,MAAM,CAACQ,GAAG;AACzB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,KAAK,CAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGd,MAAM,CAACe,CAAC;AACzB;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGhB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,WAAW,CAAGjB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,YAAYC,KAAK,EAAIA,KAAK,CAACS,SAAS,CAAG,SAAS,CAAG,aAAa;AAChE,aAAaT,KAAK,EAAIA,KAAK,CAACS,SAAS,CAAG,CAAC,CAAG,GAAG;AAC/C;AACA,sBAAsBT,KAAK,EAAI,CAC3B,OAAQA,KAAK,CAACU,MAAM,EAClB,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,aAAa,CAAE,MAAO,SAAS,CACpC,QAAS,MAAO,MAAM,CACxB,CACF,CAAC;AACH;AACA;AACA,iBAAiBV,KAAK,EAAIA,KAAK,CAACS,SAAS,CAAG,kBAAkB,CAAG,MAAM;AACvE,kBAAkBT,KAAK,EAAIA,KAAK,CAACS,SAAS,CAAG,iCAAiC,CAAG,gCAAgC;AACjH;AACA,CAAC,CAED,KAAM,CAAAE,aAAa,CAAGpB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,WAAW,CAAGrB,MAAM,CAACsB,EAAE;AAC7B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGvB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,EAAI,CACrB,OAAQA,KAAK,CAACU,MAAM,EAClB,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,UAAU,CAAE,MAAO,SAAS,CACjC,IAAK,aAAa,CAAE,MAAO,SAAS,CACpC,QAAS,MAAO,MAAM,CACxB,CACF,CAAC;AACH,CAAC,CAED,KAAM,CAAAK,YAAY,CAAGxB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAiB,YAAY,CAAGzB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAkB,aAAa,CAAG1B,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAmB,UAAU,CAAG3B,MAAM,CAAC4B,MAAM;AAChC;AACA;AACA,IAAInB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAG,aAAa,CAAG,YAAY;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAmB,cAAc,CAAG7B,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAsB,YAAY,CAAG9B,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAuB,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGnC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAoC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACoC,QAAQ,CAAEC,WAAW,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2C,KAAK,CAAEC,QAAQ,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAExC,KAAM,CAAAc,KAAK,CAAGuB,IAAI,CAACQ,QAAQ,GAAK,IAAI,CAEpC5C,SAAS,CAAC,IAAM,CACd6C,aAAa,CAAC,CAAC,CAEf;AACA,KAAM,CAAAC,QAAQ,CAAGC,WAAW,CAACF,aAAa,CAAE,KAAK,CAAC,CAClD,MAAO,IAAMG,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAA7C,cAAc,CAAC8C,cAAc,CAAC,CAAC,CACtD,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpBZ,WAAW,CAACU,QAAQ,CAACG,IAAI,CAAC,CAC1BT,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,IAAM,CACLA,QAAQ,CAACM,QAAQ,CAACI,OAAO,EAAI,sBAAsB,CAAC,CACtD,CACF,CAAE,MAAOX,KAAK,CAAE,CACdC,QAAQ,CAAC,eAAe,CAAC,CACzBW,OAAO,CAACZ,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAc,mBAAmB,CAAIC,OAAO,EAAK,CACvC,GAAIA,OAAO,CAAClC,MAAM,GAAK,WAAW,CAAE,CAClCe,QAAQ,CAAC,QAAQ,CAAE,CACjBoB,KAAK,CAAE,CACLC,eAAe,CAAEF,OACnB,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvBtB,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,KAAM,CAAAuB,aAAa,CAAItC,MAAM,EAAK,CAChC,OAAQA,MAAM,EACZ,IAAK,WAAW,CAAE,MAAO,CAAAa,CAAC,CAAC,WAAW,CAAC,CACvC,IAAK,UAAU,CAAE,MAAO,CAAAA,CAAC,CAAC,UAAU,CAAC,CACrC,IAAK,UAAU,CAAE,MAAO,CAAAA,CAAC,CAAC,UAAU,CAAC,CACrC,IAAK,aAAa,CAAE,MAAO,CAAAA,CAAC,CAAC,aAAa,CAAC,CAC3C,QAAS,MAAO,CAAAb,MAAM,CACxB,CACF,CAAC,CAED,GAAIkB,OAAO,CAAE,CACX,mBACEjC,IAAA,CAACG,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAAgD,QAAA,cACtBtD,IAAA,CAACyB,cAAc,EAAA6B,QAAA,CACZhD,KAAK,CAAG,iBAAiB,CAAG,YAAY,CAC3B,CAAC,CACR,CAAC,CAEhB,CAEA,mBACEJ,KAAA,CAACC,SAAS,EAACG,KAAK,CAAEA,KAAM,CAAAgD,QAAA,eACtBtD,IAAA,CAACuB,UAAU,EAACjB,KAAK,CAAEA,KAAM,CAACiD,OAAO,CAAEH,UAAW,CAAAE,QAAA,CAC3C1B,CAAC,CAAC,MAAM,CAAC,CACA,CAAC,cAEb1B,KAAA,CAACK,MAAM,EAAA+C,QAAA,eACLtD,IAAA,CAACQ,KAAK,EAAA8C,QAAA,CAAE1B,CAAC,CAAC,eAAe,CAAC,CAAQ,CAAC,cACnC5B,IAAA,CAACU,QAAQ,EAAA4C,QAAA,CACNhD,KAAK,CAAG,sCAAsC,CAAG,sCAAsC,CAChF,CAAC,EACL,CAAC,CAER6B,KAAK,eACJnC,IAAA,CAAC0B,YAAY,EAAA4B,QAAA,CACVnB,KAAK,CACM,CACf,cAEDnC,IAAA,CAACY,WAAW,EAAA0C,QAAA,CACTvB,QAAQ,CAACyB,GAAG,CAAEP,OAAO,eACpB/C,KAAA,CAACW,WAAW,EAEVC,SAAS,CAAEmC,OAAO,CAAClC,MAAM,GAAK,WAAY,CAC1CA,MAAM,CAAEkC,OAAO,CAAClC,MAAO,CACvBwC,OAAO,CAAEA,CAAA,GAAMP,mBAAmB,CAACC,OAAO,CAAE,CAAAK,QAAA,eAE5CtD,IAAA,CAACgB,aAAa,EAAAsC,QAAA,CACXL,OAAO,CAACQ,aAAa,CACT,CAAC,cAEhBzD,IAAA,CAACiB,WAAW,EAAAqC,QAAA,CACTL,OAAO,CAACS,IAAI,CACF,CAAC,cAEd1D,IAAA,CAACmB,WAAW,EAACJ,MAAM,CAAEkC,OAAO,CAAClC,MAAO,CAAAuC,QAAA,CACjCD,aAAa,CAACJ,OAAO,CAAClC,MAAM,CAAC,CACnB,CAAC,CAEbkC,OAAO,CAAClC,MAAM,GAAK,UAAU,EAAIkC,OAAO,CAACU,YAAY,eACpDzD,KAAA,CAACkB,YAAY,EAAAkC,QAAA,eACXpD,KAAA,CAACmB,YAAY,EAAAiC,QAAA,EACVhD,KAAK,CAAG,UAAU,CAAG,YAAY,CAAE2C,OAAO,CAACU,YAAY,EAC5C,CAAC,CACdV,OAAO,CAACW,aAAa,EAAI,CAACX,OAAO,CAACW,aAAa,CAACC,OAAO,eACtD7D,IAAA,CAACsB,aAAa,EAAAgC,QAAA,CACX1B,CAAC,CAAC,eAAe,CAAE,CAClBkC,IAAI,CAAEhE,mBAAmB,CAACmD,OAAO,CAACW,aAAa,CAACG,SAAS,CAC3D,CAAC,CAAC,CACW,CAChB,EACW,CACf,CAEAd,OAAO,CAAClC,MAAM,GAAK,UAAU,EAAIkC,OAAO,CAACU,YAAY,eACpD3D,IAAA,CAACoB,YAAY,EAAAkC,QAAA,cACXpD,KAAA,CAACmB,YAAY,EAAAiC,QAAA,EACVhD,KAAK,CAAG,YAAY,CAAG,gBAAgB,CAAE2C,OAAO,CAACU,YAAY,EAClD,CAAC,CACH,CACf,GAtCIV,OAAO,CAACe,EAuCF,CACd,CAAC,CACS,CAAC,EACL,CAAC,CAEhB,CAAC,CAED,cAAe,CAAArC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}