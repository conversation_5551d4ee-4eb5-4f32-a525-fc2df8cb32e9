{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\pages\\\\NameInputPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { displayService } from '../services/api';\nimport { sanitizeText, getFromLocalStorage } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n_c = Container;\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n`;\n_c2 = Card;\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n_c3 = Header;\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;\n_c4 = Title;\nconst DisplayInfo = styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  text-align: center;\n`;\n_c5 = DisplayInfo;\nconst DisplayNumber = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 10px;\n`;\n_c6 = DisplayNumber;\nconst PriceInfo = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n_c7 = PriceInfo;\nconst InfoCard = styled.div`\n  background: #e3f2fd;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n`;\n_c8 = InfoCard;\nconst InfoLabel = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 5px;\n`;\n_c9 = InfoLabel;\nconst InfoValue = styled.div`\n  font-size: 1.3rem;\n  font-weight: bold;\n  color: #1976d2;\n`;\n_c0 = InfoValue;\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c1 = Form;\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n_c10 = InputGroup;\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1.1rem;\n`;\n_c11 = Label;\nconst NameInput = styled.input`\n  padding: 20px;\n  border: 3px solid #ddd;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  text-align: center;\n  transition: border-color 0.3s ease;\n  font-weight: 600;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n`;\n_c12 = NameInput;\nconst KeyboardContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\n  gap: 8px;\n  margin-top: 20px;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c13 = KeyboardContainer;\nconst KeyButton = styled.button`\n  padding: 12px 8px;\n  border: 2px solid #ddd;\n  background: white;\n  border-radius: 8px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #f8f9fa;\n    border-color: #667eea;\n  }\n\n  &:active {\n    background: #667eea;\n    color: white;\n  }\n`;\n_c14 = KeyButton;\nconst LanguageToggle = styled.button`\n  background: #6c757d;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-bottom: 10px;\n  align-self: center;\n\n  &:hover {\n    background: #5a6268;\n  }\n`;\n_c15 = LanguageToggle;\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n_c16 = Button;\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n_c17 = BackButton;\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n_c18 = ErrorMessage;\nconst NameInputPage = () => {\n  _s();\n  var _location$state, _location$state2;\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [customerName, setCustomerName] = useState('');\n  const [keyboardLanguage, setKeyboardLanguage] = useState('ar');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [settings, setSettings] = useState({\n    displayPrice: 50,\n    displayDuration: 300\n  });\n  const isRTL = i18n.language === 'ar';\n  const selectedDisplay = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.selectedDisplay;\n  const customer = (_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.customer;\n\n  // لوحة المفاتيح العربية\n  const arabicKeys = [['ض', 'ص', 'ث', 'ق', 'ف', 'غ', 'ع', 'ه', 'خ', 'ح', 'ج', 'د'], ['ش', 'س', 'ي', 'ب', 'ل', 'ا', 'ت', 'ن', 'م', 'ك', 'ط'], ['ئ', 'ء', 'ؤ', 'ر', 'لا', 'ى', 'ة', 'و', 'ز', 'ظ'], ['مسافة', 'حذف']];\n\n  // لوحة المفاتيح الإنجليزية\n  const englishKeys = [['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'], ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'], ['Z', 'X', 'C', 'V', 'B', 'N', 'M'], ['Space', 'Delete']];\n  useEffect(() => {\n    // تحميل الإعدادات من localStorage أو استخدام القيم الافتراضية\n    const savedSettings = getFromLocalStorage('appSettings');\n    if (savedSettings) {\n      setSettings({\n        displayPrice: savedSettings.display_price || 50,\n        displayDuration: savedSettings.display_duration || 300\n      });\n    }\n\n    // التحقق من وجود البيانات المطلوبة\n    if (!selectedDisplay || !customer) {\n      navigate('/select-display');\n    }\n  }, [selectedDisplay, customer, navigate]);\n  const handleKeyPress = key => {\n    if (key === 'مسافة' || key === 'Space') {\n      setCustomerName(prev => prev + ' ');\n    } else if (key === 'حذف' || key === 'Delete') {\n      setCustomerName(prev => prev.slice(0, -1));\n    } else {\n      setCustomerName(prev => prev + key);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const trimmedName = customerName.trim();\n    if (!trimmedName) {\n      setError(t('enterName'));\n      return;\n    }\n    if (trimmedName.length < 2) {\n      setError(isRTL ? 'الاسم يجب أن يكون حرفين على الأقل' : 'Name must be at least 2 characters');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      // حجز الشاشة\n      const response = await displayService.reserveDisplay(selectedDisplay.id, sanitizeText(trimmedName));\n      if (response.success) {\n        // الانتقال لصفحة الدفع\n        navigate('/payment', {\n          state: {\n            selectedDisplay,\n            customer,\n            customerName: sanitizeText(trimmedName),\n            amount: settings.displayPrice,\n            duration: settings.displayDuration\n          }\n        });\n      } else {\n        setError(response.message || 'فشل في حجز الشاشة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error reserving display:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    navigate('/select-display');\n  };\n  if (!selectedDisplay || !customer) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    isRTL: isRTL,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: /*#__PURE__*/_jsxDEV(Title, {\n          children: t('enterName')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DisplayInfo, {\n        children: [/*#__PURE__*/_jsxDEV(DisplayNumber, {\n          children: t('displayNumber', {\n            number: selectedDisplay.displayNumber\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: selectedDisplay.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PriceInfo, {\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          children: [/*#__PURE__*/_jsxDEV(InfoLabel, {\n            children: t('displayPrice', {\n              price: ''\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InfoValue, {\n            children: [settings.displayPrice, \" \", isRTL ? 'ريال' : 'SAR']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          children: [/*#__PURE__*/_jsxDEV(InfoLabel, {\n            children: t('displayDuration', {\n              duration: ''\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InfoValue, {\n            children: [Math.floor(settings.displayDuration / 60), \" \", t('minutes')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: t('customerName')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NameInput, {\n            type: \"text\",\n            value: customerName,\n            onChange: e => setCustomerName(e.target.value),\n            placeholder: t('enterName'),\n            maxLength: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LanguageToggle, {\n          type: \"button\",\n          onClick: () => setKeyboardLanguage(prev => prev === 'ar' ? 'en' : 'ar'),\n          children: keyboardLanguage === 'ar' ? 'English' : 'العربية'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(KeyboardContainer, {\n          children: (keyboardLanguage === 'ar' ? arabicKeys : englishKeys).flat().map((key, index) => /*#__PURE__*/_jsxDEV(KeyButton, {\n            type: \"button\",\n            onClick: () => handleKeyPress(key),\n            style: {\n              gridColumn: key === 'مسافة' || key === 'Space' ? 'span 6' : key === 'حذف' || key === 'Delete' ? 'span 3' : 'span 1'\n            },\n            children: key\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading || !customerName.trim(),\n          children: loading ? t('processing') : t('proceedToPayment')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: handleBack,\n          children: t('back')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 320,\n    columnNumber: 5\n  }, this);\n};\n_s(NameInputPage, \"gHhHP1+dvFDyFnwBd92var+/zi8=\", false, function () {\n  return [useTranslation, useNavigate, useLocation];\n});\n_c19 = NameInputPage;\nexport default NameInputPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"Header\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"DisplayInfo\");\n$RefreshReg$(_c6, \"DisplayNumber\");\n$RefreshReg$(_c7, \"PriceInfo\");\n$RefreshReg$(_c8, \"InfoCard\");\n$RefreshReg$(_c9, \"InfoLabel\");\n$RefreshReg$(_c0, \"InfoValue\");\n$RefreshReg$(_c1, \"Form\");\n$RefreshReg$(_c10, \"InputGroup\");\n$RefreshReg$(_c11, \"Label\");\n$RefreshReg$(_c12, \"NameInput\");\n$RefreshReg$(_c13, \"KeyboardContainer\");\n$RefreshReg$(_c14, \"KeyButton\");\n$RefreshReg$(_c15, \"LanguageToggle\");\n$RefreshReg$(_c16, \"Button\");\n$RefreshReg$(_c17, \"BackButton\");\n$RefreshReg$(_c18, \"ErrorMessage\");\n$RefreshReg$(_c19, \"NameInputPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useLocation", "styled", "displayService", "sanitizeText", "getFromLocalStorage", "jsxDEV", "_jsxDEV", "Container", "div", "props", "isRTL", "_c", "Card", "_c2", "Header", "_c3", "Title", "h1", "_c4", "DisplayInfo", "_c5", "DisplayNumber", "_c6", "PriceInfo", "_c7", "InfoCard", "_c8", "InfoLabel", "_c9", "InfoValue", "_c0", "Form", "form", "_c1", "InputGroup", "_c10", "Label", "label", "_c11", "NameInput", "input", "_c12", "KeyboardContainer", "_c13", "KeyButton", "button", "_c14", "LanguageToggle", "_c15", "<PERSON><PERSON>", "disabled", "_c16", "BackButton", "_c17", "ErrorMessage", "_c18", "NameInputPage", "_s", "_location$state", "_location$state2", "t", "i18n", "navigate", "location", "customerName", "setCustomerName", "keyboardLanguage", "setKeyboardLanguage", "loading", "setLoading", "error", "setError", "settings", "setSettings", "displayPrice", "displayDuration", "language", "selectedDisplay", "state", "customer", "arabicKeys", "english<PERSON>eys", "savedSettings", "display_price", "display_duration", "handleKeyPress", "key", "prev", "slice", "handleSubmit", "e", "preventDefault", "trimmedName", "trim", "length", "response", "reserveDisplay", "id", "success", "amount", "duration", "message", "console", "handleBack", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "number", "displayNumber", "name", "price", "Math", "floor", "onSubmit", "type", "value", "onChange", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "onClick", "flat", "map", "index", "style", "gridColumn", "marginTop", "textAlign", "_c19", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/pages/NameInputPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { displayService } from '../services/api';\nimport { sanitizeText, getFromLocalStorage } from '../utils/helpers';\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};\n`;\n\nconst Card = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 600px;\n  width: 100%;\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h1`\n  font-size: 2rem;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst DisplayInfo = styled.div`\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  text-align: center;\n`;\n\nconst DisplayNumber = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 10px;\n`;\n\nconst PriceInfo = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n\nconst InfoCard = styled.div`\n  background: #e3f2fd;\n  padding: 15px;\n  border-radius: 10px;\n  text-align: center;\n`;\n\nconst InfoLabel = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 5px;\n`;\n\nconst InfoValue = styled.div`\n  font-size: 1.3rem;\n  font-weight: bold;\n  color: #1976d2;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-weight: 600;\n  color: #333;\n  font-size: 1.1rem;\n`;\n\nconst NameInput = styled.input`\n  padding: 20px;\n  border: 3px solid #ddd;\n  border-radius: 15px;\n  font-size: 1.3rem;\n  text-align: center;\n  transition: border-color 0.3s ease;\n  font-weight: 600;\n\n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n`;\n\nconst KeyboardContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\n  gap: 8px;\n  margin-top: 20px;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst KeyButton = styled.button`\n  padding: 12px 8px;\n  border: 2px solid #ddd;\n  background: white;\n  border-radius: 8px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: #f8f9fa;\n    border-color: #667eea;\n  }\n\n  &:active {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst LanguageToggle = styled.button`\n  background: #6c757d;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  cursor: pointer;\n  margin-bottom: 10px;\n  align-self: center;\n\n  &:hover {\n    background: #5a6268;\n  }\n`;\n\nconst Button = styled.button`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 18px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  border-radius: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: ${props => props.disabled ? 0.6 : 1};\n\n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: 2px solid #667eea;\n  color: #667eea;\n  padding: 12px 24px;\n  font-size: 1rem;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: #667eea;\n    color: white;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background: #dc3545;\n  color: white;\n  padding: 12px;\n  border-radius: 8px;\n  text-align: center;\n  margin-bottom: 20px;\n`;\n\nconst NameInputPage = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const [customerName, setCustomerName] = useState('');\n  const [keyboardLanguage, setKeyboardLanguage] = useState('ar');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [settings, setSettings] = useState({\n    displayPrice: 50,\n    displayDuration: 300\n  });\n\n  const isRTL = i18n.language === 'ar';\n  const selectedDisplay = location.state?.selectedDisplay;\n  const customer = location.state?.customer;\n\n  // لوحة المفاتيح العربية\n  const arabicKeys = [\n    ['ض', 'ص', 'ث', 'ق', 'ف', 'غ', 'ع', 'ه', 'خ', 'ح', 'ج', 'د'],\n    ['ش', 'س', 'ي', 'ب', 'ل', 'ا', 'ت', 'ن', 'م', 'ك', 'ط'],\n    ['ئ', 'ء', 'ؤ', 'ر', 'لا', 'ى', 'ة', 'و', 'ز', 'ظ'],\n    ['مسافة', 'حذف']\n  ];\n\n  // لوحة المفاتيح الإنجليزية\n  const englishKeys = [\n    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],\n    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],\n    ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],\n    ['Space', 'Delete']\n  ];\n\n  useEffect(() => {\n    // تحميل الإعدادات من localStorage أو استخدام القيم الافتراضية\n    const savedSettings = getFromLocalStorage('appSettings');\n    if (savedSettings) {\n      setSettings({\n        displayPrice: savedSettings.display_price || 50,\n        displayDuration: savedSettings.display_duration || 300\n      });\n    }\n\n    // التحقق من وجود البيانات المطلوبة\n    if (!selectedDisplay || !customer) {\n      navigate('/select-display');\n    }\n  }, [selectedDisplay, customer, navigate]);\n\n  const handleKeyPress = (key) => {\n    if (key === 'مسافة' || key === 'Space') {\n      setCustomerName(prev => prev + ' ');\n    } else if (key === 'حذف' || key === 'Delete') {\n      setCustomerName(prev => prev.slice(0, -1));\n    } else {\n      setCustomerName(prev => prev + key);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const trimmedName = customerName.trim();\n    if (!trimmedName) {\n      setError(t('enterName'));\n      return;\n    }\n\n    if (trimmedName.length < 2) {\n      setError(isRTL ? 'الاسم يجب أن يكون حرفين على الأقل' : 'Name must be at least 2 characters');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      // حجز الشاشة\n      const response = await displayService.reserveDisplay(selectedDisplay.id, sanitizeText(trimmedName));\n      \n      if (response.success) {\n        // الانتقال لصفحة الدفع\n        navigate('/payment', {\n          state: {\n            selectedDisplay,\n            customer,\n            customerName: sanitizeText(trimmedName),\n            amount: settings.displayPrice,\n            duration: settings.displayDuration\n          }\n        });\n      } else {\n        setError(response.message || 'فشل في حجز الشاشة');\n      }\n    } catch (error) {\n      setError('خطأ في الشبكة');\n      console.error('Error reserving display:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/select-display');\n  };\n\n  if (!selectedDisplay || !customer) {\n    return null;\n  }\n\n  return (\n    <Container isRTL={isRTL}>\n      <Card>\n        <Header>\n          <Title>{t('enterName')}</Title>\n        </Header>\n\n        <DisplayInfo>\n          <DisplayNumber>\n            {t('displayNumber', { number: selectedDisplay.displayNumber })}\n          </DisplayNumber>\n          <div>{selectedDisplay.name}</div>\n        </DisplayInfo>\n\n        <PriceInfo>\n          <InfoCard>\n            <InfoLabel>{t('displayPrice', { price: '' })}</InfoLabel>\n            <InfoValue>{settings.displayPrice} {isRTL ? 'ريال' : 'SAR'}</InfoValue>\n          </InfoCard>\n          <InfoCard>\n            <InfoLabel>{t('displayDuration', { duration: '' })}</InfoLabel>\n            <InfoValue>{Math.floor(settings.displayDuration / 60)} {t('minutes')}</InfoValue>\n          </InfoCard>\n        </PriceInfo>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <Label>{t('customerName')}</Label>\n            <NameInput\n              type=\"text\"\n              value={customerName}\n              onChange={(e) => setCustomerName(e.target.value)}\n              placeholder={t('enterName')}\n              maxLength=\"50\"\n            />\n          </InputGroup>\n\n          <LanguageToggle\n            type=\"button\"\n            onClick={() => setKeyboardLanguage(prev => prev === 'ar' ? 'en' : 'ar')}\n          >\n            {keyboardLanguage === 'ar' ? 'English' : 'العربية'}\n          </LanguageToggle>\n\n          <KeyboardContainer>\n            {(keyboardLanguage === 'ar' ? arabicKeys : englishKeys).flat().map((key, index) => (\n              <KeyButton\n                key={index}\n                type=\"button\"\n                onClick={() => handleKeyPress(key)}\n                style={{\n                  gridColumn: (key === 'مسافة' || key === 'Space') ? 'span 6' : \n                             (key === 'حذف' || key === 'Delete') ? 'span 3' : 'span 1'\n                }}\n              >\n                {key}\n              </KeyButton>\n            ))}\n          </KeyboardContainer>\n\n          <Button type=\"submit\" disabled={loading || !customerName.trim()}>\n            {loading ? t('processing') : t('proceedToPayment')}\n          </Button>\n        </Form>\n\n        <div style={{ marginTop: '20px', textAlign: 'center' }}>\n          <BackButton onClick={handleBack}>\n            {t('back')}\n          </BackButton>\n        </div>\n      </Card>\n    </Container>\n  );\n};\n\nexport default NameInputPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAS,GAAGN,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;AACnD,CAAC;AAACC,EAAA,GARIJ,SAAS;AAUf,MAAMK,IAAI,GAAGX,MAAM,CAACO,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAPID,IAAI;AASV,MAAME,MAAM,GAAGb,MAAM,CAACO,GAAG;AACzB;AACA;AACA,CAAC;AAACO,GAAA,GAHID,MAAM;AAKZ,MAAME,KAAK,GAAGf,MAAM,CAACgB,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,WAAW,GAAGlB,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,WAAW;AAQjB,MAAME,aAAa,GAAGpB,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GALID,aAAa;AAOnB,MAAME,SAAS,GAAGtB,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGxB,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GALID,QAAQ;AAOd,MAAME,SAAS,GAAG1B,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAG5B,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAJID,SAAS;AAMf,MAAME,IAAI,GAAG9B,MAAM,CAAC+B,IAAI;AACxB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,UAAU,GAAGjC,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAAC2B,IAAA,GAJID,UAAU;AAMhB,MAAME,KAAK,GAAGnC,MAAM,CAACoC,KAAK;AAC1B;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAJIF,KAAK;AAMX,MAAMG,SAAS,GAAGtC,MAAM,CAACuC,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,SAAS;AAgBf,MAAMG,iBAAiB,GAAGzC,MAAM,CAACO,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GARID,iBAAiB;AAUvB,MAAME,SAAS,GAAG3C,MAAM,CAAC4C,MAAM;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAnBIF,SAAS;AAqBf,MAAMG,cAAc,GAAG9C,MAAM,CAAC4C,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAdID,cAAc;AAgBpB,MAAME,MAAM,GAAGhD,MAAM,CAAC4C,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAapC,KAAK,IAAIA,KAAK,CAACyC,QAAQ,GAAG,GAAG,GAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GApBIF,MAAM;AAsBZ,MAAMG,UAAU,GAAGnD,MAAM,CAAC4C,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,IAAA,GAfID,UAAU;AAiBhB,MAAME,YAAY,GAAGrD,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GAPID,YAAY;AASlB,MAAME,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAC1B,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAG/D,cAAc,CAAC,CAAC;EACpC,MAAMgE,QAAQ,GAAG/D,WAAW,CAAC,CAAC;EAC9B,MAAMgE,QAAQ,GAAG/D,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC;IACvC8E,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMjE,KAAK,GAAGmD,IAAI,CAACe,QAAQ,KAAK,IAAI;EACpC,MAAMC,eAAe,IAAAnB,eAAA,GAAGK,QAAQ,CAACe,KAAK,cAAApB,eAAA,uBAAdA,eAAA,CAAgBmB,eAAe;EACvD,MAAME,QAAQ,IAAApB,gBAAA,GAAGI,QAAQ,CAACe,KAAK,cAAAnB,gBAAA,uBAAdA,gBAAA,CAAgBoB,QAAQ;;EAEzC;EACA,MAAMC,UAAU,GAAG,CACjB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC5D,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACvD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACnD,CAAC,OAAO,EAAE,KAAK,CAAC,CACjB;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAClD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAC7C,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACnC,CAAC,OAAO,EAAE,QAAQ,CAAC,CACpB;EAEDpF,SAAS,CAAC,MAAM;IACd;IACA,MAAMqF,aAAa,GAAG9E,mBAAmB,CAAC,aAAa,CAAC;IACxD,IAAI8E,aAAa,EAAE;MACjBT,WAAW,CAAC;QACVC,YAAY,EAAEQ,aAAa,CAACC,aAAa,IAAI,EAAE;QAC/CR,eAAe,EAAEO,aAAa,CAACE,gBAAgB,IAAI;MACrD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACP,eAAe,IAAI,CAACE,QAAQ,EAAE;MACjCjB,QAAQ,CAAC,iBAAiB,CAAC;IAC7B;EACF,CAAC,EAAE,CAACe,eAAe,EAAEE,QAAQ,EAAEjB,QAAQ,CAAC,CAAC;EAEzC,MAAMuB,cAAc,GAAIC,GAAG,IAAK;IAC9B,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,OAAO,EAAE;MACtCrB,eAAe,CAACsB,IAAI,IAAIA,IAAI,GAAG,GAAG,CAAC;IACrC,CAAC,MAAM,IAAID,GAAG,KAAK,KAAK,IAAIA,GAAG,KAAK,QAAQ,EAAE;MAC5CrB,eAAe,CAACsB,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLvB,eAAe,CAACsB,IAAI,IAAIA,IAAI,GAAGD,GAAG,CAAC;IACrC;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,WAAW,GAAG5B,YAAY,CAAC6B,IAAI,CAAC,CAAC;IACvC,IAAI,CAACD,WAAW,EAAE;MAChBrB,QAAQ,CAACX,CAAC,CAAC,WAAW,CAAC,CAAC;MACxB;IACF;IAEA,IAAIgC,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;MAC1BvB,QAAQ,CAAC7D,KAAK,GAAG,mCAAmC,GAAG,oCAAoC,CAAC;MAC5F;IACF;IAEA2D,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMwB,QAAQ,GAAG,MAAM7F,cAAc,CAAC8F,cAAc,CAACnB,eAAe,CAACoB,EAAE,EAAE9F,YAAY,CAACyF,WAAW,CAAC,CAAC;MAEnG,IAAIG,QAAQ,CAACG,OAAO,EAAE;QACpB;QACApC,QAAQ,CAAC,UAAU,EAAE;UACnBgB,KAAK,EAAE;YACLD,eAAe;YACfE,QAAQ;YACRf,YAAY,EAAE7D,YAAY,CAACyF,WAAW,CAAC;YACvCO,MAAM,EAAE3B,QAAQ,CAACE,YAAY;YAC7B0B,QAAQ,EAAE5B,QAAQ,CAACG;UACrB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLJ,QAAQ,CAACwB,QAAQ,CAACM,OAAO,IAAI,mBAAmB,CAAC;MACnD;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,QAAQ,CAAC,eAAe,CAAC;MACzB+B,OAAO,CAAChC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,UAAU,GAAGA,CAAA,KAAM;IACvBzC,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;EAED,IAAI,CAACe,eAAe,IAAI,CAACE,QAAQ,EAAE;IACjC,OAAO,IAAI;EACb;EAEA,oBACEzE,OAAA,CAACC,SAAS;IAACG,KAAK,EAAEA,KAAM;IAAA8F,QAAA,eACtBlG,OAAA,CAACM,IAAI;MAAA4F,QAAA,gBACHlG,OAAA,CAACQ,MAAM;QAAA0F,QAAA,eACLlG,OAAA,CAACU,KAAK;UAAAwF,QAAA,EAAE5C,CAAC,CAAC,WAAW;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAETtG,OAAA,CAACa,WAAW;QAAAqF,QAAA,gBACVlG,OAAA,CAACe,aAAa;UAAAmF,QAAA,EACX5C,CAAC,CAAC,eAAe,EAAE;YAAEiD,MAAM,EAAEhC,eAAe,CAACiC;UAAc,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAChBtG,OAAA;UAAAkG,QAAA,EAAM3B,eAAe,CAACkC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEdtG,OAAA,CAACiB,SAAS;QAAAiF,QAAA,gBACRlG,OAAA,CAACmB,QAAQ;UAAA+E,QAAA,gBACPlG,OAAA,CAACqB,SAAS;YAAA6E,QAAA,EAAE5C,CAAC,CAAC,cAAc,EAAE;cAAEoD,KAAK,EAAE;YAAG,CAAC;UAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzDtG,OAAA,CAACuB,SAAS;YAAA2E,QAAA,GAAEhC,QAAQ,CAACE,YAAY,EAAC,GAAC,EAAChE,KAAK,GAAG,MAAM,GAAG,KAAK;UAAA;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACXtG,OAAA,CAACmB,QAAQ;UAAA+E,QAAA,gBACPlG,OAAA,CAACqB,SAAS;YAAA6E,QAAA,EAAE5C,CAAC,CAAC,iBAAiB,EAAE;cAAEwC,QAAQ,EAAE;YAAG,CAAC;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DtG,OAAA,CAACuB,SAAS;YAAA2E,QAAA,GAAES,IAAI,CAACC,KAAK,CAAC1C,QAAQ,CAACG,eAAe,GAAG,EAAE,CAAC,EAAC,GAAC,EAACf,CAAC,CAAC,SAAS,CAAC;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEXtC,KAAK,iBAAIhE,OAAA,CAACgD,YAAY;QAAAkD,QAAA,EAAElC;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAE9CtG,OAAA,CAACyB,IAAI;QAACoF,QAAQ,EAAE1B,YAAa;QAAAe,QAAA,gBAC3BlG,OAAA,CAAC4B,UAAU;UAAAsE,QAAA,gBACTlG,OAAA,CAAC8B,KAAK;YAAAoE,QAAA,EAAE5C,CAAC,CAAC,cAAc;UAAC;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClCtG,OAAA,CAACiC,SAAS;YACR6E,IAAI,EAAC,MAAM;YACXC,KAAK,EAAErD,YAAa;YACpBsD,QAAQ,EAAG5B,CAAC,IAAKzB,eAAe,CAACyB,CAAC,CAAC6B,MAAM,CAACF,KAAK,CAAE;YACjDG,WAAW,EAAE5D,CAAC,CAAC,WAAW,CAAE;YAC5B6D,SAAS,EAAC;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbtG,OAAA,CAACyC,cAAc;UACbqE,IAAI,EAAC,QAAQ;UACbM,OAAO,EAAEA,CAAA,KAAMvD,mBAAmB,CAACoB,IAAI,IAAIA,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAE;UAAAiB,QAAA,EAEvEtC,gBAAgB,KAAK,IAAI,GAAG,SAAS,GAAG;QAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEjBtG,OAAA,CAACoC,iBAAiB;UAAA8D,QAAA,EACf,CAACtC,gBAAgB,KAAK,IAAI,GAAGc,UAAU,GAAGC,WAAW,EAAE0C,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,CAACtC,GAAG,EAAEuC,KAAK,kBAC5EvH,OAAA,CAACsC,SAAS;YAERwE,IAAI,EAAC,QAAQ;YACbM,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAACC,GAAG,CAAE;YACnCwC,KAAK,EAAE;cACLC,UAAU,EAAGzC,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,OAAO,GAAI,QAAQ,GAC/CA,GAAG,KAAK,KAAK,IAAIA,GAAG,KAAK,QAAQ,GAAI,QAAQ,GAAG;YAC9D,CAAE;YAAAkB,QAAA,EAEDlB;UAAG,GARCuC,KAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASD,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC,eAEpBtG,OAAA,CAAC2C,MAAM;UAACmE,IAAI,EAAC,QAAQ;UAAClE,QAAQ,EAAEkB,OAAO,IAAI,CAACJ,YAAY,CAAC6B,IAAI,CAAC,CAAE;UAAAW,QAAA,EAC7DpC,OAAO,GAAGR,CAAC,CAAC,YAAY,CAAC,GAAGA,CAAC,CAAC,kBAAkB;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPtG,OAAA;QAAKwH,KAAK,EAAE;UAAEE,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAzB,QAAA,eACrDlG,OAAA,CAAC8C,UAAU;UAACsE,OAAO,EAAEnB,UAAW;UAAAC,QAAA,EAC7B5C,CAAC,CAAC,MAAM;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACnD,EAAA,CA1LID,aAAa;EAAA,QACG1D,cAAc,EACjBC,WAAW,EACXC,WAAW;AAAA;AAAAkI,IAAA,GAHxB1E,aAAa;AA4LnB,eAAeA,aAAa;AAAC,IAAA7C,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA2E,IAAA;AAAAC,YAAA,CAAAxH,EAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}