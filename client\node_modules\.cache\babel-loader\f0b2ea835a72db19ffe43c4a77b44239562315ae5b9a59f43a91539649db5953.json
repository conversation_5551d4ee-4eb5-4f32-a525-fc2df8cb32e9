{"ast": null, "code": "import { useTranslation } from './useTranslation.js';\nexport const Translation = _ref => {\n  let {\n    ns,\n    children,\n    ...options\n  } = _ref;\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};", "map": {"version": 3, "names": ["useTranslation", "Translation", "_ref", "ns", "children", "options", "t", "i18n", "ready", "lng", "language"], "sources": ["D:/برمجة/tste 1/node_modules/react-i18next/dist/es/Translation.js"], "sourcesContent": ["import { useTranslation } from './useTranslation.js';\nexport const Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,MAAMC,WAAW,GAAGC,IAAA,IAIrB;EAAA,IAJsB;IAC1BC,EAAE;IACFC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAAH,IAAA;EACC,MAAM,CAACI,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGR,cAAc,CAACG,EAAE,EAAEE,OAAO,CAAC;EACpD,OAAOD,QAAQ,CAACE,CAAC,EAAE;IACjBC,IAAI;IACJE,GAAG,EAAEF,IAAI,CAACG;EACZ,CAAC,EAAEF,KAAK,CAAC;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}