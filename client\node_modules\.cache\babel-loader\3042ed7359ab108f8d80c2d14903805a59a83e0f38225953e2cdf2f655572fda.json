{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\tste 1\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './hooks/useAuth';\nimport './utils/i18n';\nimport './App.css';\n\n// استيراد الصفحات\nimport HomePage from './pages/HomePage';\nimport DisplaySelectionPage from './pages/DisplaySelectionPage';\nimport LoginPage from './pages/LoginPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/select-display\",\n            element: /*#__PURE__*/_jsxDEV(DisplaySelectionPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "HomePage", "DisplaySelectionPage", "LoginPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/برمجة/tste 1/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './hooks/useAuth';\nimport './utils/i18n';\nimport './App.css';\n\n// استيراد الصفحات\nimport HomePage from './pages/HomePage';\nimport DisplaySelectionPage from './pages/DisplaySelectionPage';\nimport LoginPage from './pages/LoginPage';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/select-display\" element={<DisplaySelectionPage />} />\n            <Route path=\"/login\" element={<LoginPage />} />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,cAAc;AACrB,OAAO,WAAW;;AAElB;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,SAAS,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACL,YAAY;IAAAO,QAAA,eACXF,OAAA,CAACR,MAAM;MAAAU,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,eAClBF,OAAA,CAACP,MAAM;UAAAS,QAAA,gBACLF,OAAA,CAACN,KAAK;YAACU,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEL,OAAA,CAACJ,QAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCT,OAAA,CAACN,KAAK;YAACU,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAEL,OAAA,CAACH,oBAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnET,OAAA,CAACN,KAAK;YAACU,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACF,SAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACC,EAAA,GAdQT,GAAG;AAgBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}